"""
配置管理器模块
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Set, Union
from dataclasses import dataclass, field, asdict

from .utils import PathUtils, FileUtils

# 配置日志记录器
logger = logging.getLogger(__name__)

@dataclass
class GeneratorConfig:
    """生成器配置类"""
    
    # 项目根目录
    project_root: Path
    
    # 文档输出目录
    docs_dir: Path
    
    # 要处理的模块列表
    modules: List[str]
    
    # 输出格式 (html/markdown)
    output_format: str = "markdown"
    
    # HTML主题 (sphinx_rtd_theme/alabaster等)
    html_theme: Optional[str] = None
    
    # 排除的目录
    excluded_dirs: Set[str] = field(default_factory=lambda: {
        ".git", ".venv", "__pycache__",
        "build", "dist", "node_modules",
        ".idea", ".vscode", ".pytest_cache"
    })
    
    # 排除的文件
    excluded_files: Set[str] = field(default_factory=lambda: {
        "setup.py", "conftest.py", "__init__.py"
    })
    
    # 包含的文件扩展名
    included_extensions: Set[str] = field(default_factory=lambda: {
        ".py", ".js", ".ts", ".jsx", ".tsx",
        ".java", ".cpp", ".c", ".h", ".hpp"
    })
    
    def __post_init__(self):
        """初始化后处理
        
        - 转换路径为Path对象
        - 创建必要的目录
        - 验证配置有效性
        """
        # 转换路径
        self.project_root = PathUtils.normalize_path(self.project_root)
        self.docs_dir = PathUtils.normalize_path(self.docs_dir)
        
        # 创建目录
        self.docs_dir.mkdir(parents=True, exist_ok=True)
        
        # 验证配置
        self._validate_config()
        
    def _validate_config(self) -> None:
        """验证配置有效性"""
        # 检查项目根目录
        if not self.project_root.exists():
            raise ValueError(f"项目根目录不存在: {self.project_root}")
            
        # 检查模块列表
        if not self.modules:
            raise ValueError("未指定要处理的模块")
            
        # 检查输出格式
        if self.output_format not in {"html", "markdown"}:
            raise ValueError(f"不支持的输出格式: {self.output_format}")
            
    def get_module_path(self, module: str) -> Path:
        """获取模块路径
        
        Args:
            module: 模块名
            
        Returns:
            模块路径
        """
        return self.project_root / module
        
    def should_process_file(self, path: Path) -> bool:
        """检查是否应处理该文件
        
        Args:
            path: 文件路径
            
        Returns:
            是否应处理
        """
        # 检查是否在排除目录中
        if any(p.name in self.excluded_dirs for p in path.parents):
            return False
            
        # 检查是否是排除的文件
        if path.name in self.excluded_files:
            return False
            
        # 检查扩展名
        return path.suffix in self.included_extensions
        
    def to_dict(self) -> Dict:
        """转换为字典
        
        Returns:
            配置字典
        """
        config_dict = asdict(self)
        
        # 转换路径为字符串
        config_dict["project_root"] = str(self.project_root)
        config_dict["docs_dir"] = str(self.docs_dir)
        
        # 转换集合为列表
        config_dict["excluded_dirs"] = sorted(self.excluded_dirs)
        config_dict["excluded_files"] = sorted(self.excluded_files)
        config_dict["included_extensions"] = sorted(self.included_extensions)
        
        return config_dict
        
    def save(self, path: Union[str, Path]) -> bool:
        """保存配置到文件
        
        Args:
            path: 配置文件路径
            
        Returns:
            是否保存成功
        """
        try:
            config_dict = self.to_dict()
            return FileUtils.safe_write_file(
                Path(path),
                json.dumps(config_dict, indent=2, ensure_ascii=False)
            )
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return False
            
    @classmethod
    def load(cls, path: Union[str, Path]) -> Optional["GeneratorConfig"]:
        """从文件加载配置
        
        Args:
            path: 配置文件路径
            
        Returns:
            配置对象，如果加载失败则返回None
        """
        try:
            content = FileUtils.safe_read_file(Path(path))
            if not content:
                return None
                
            config_dict = json.loads(content)
            
            # 转换集合
            for key in ["excluded_dirs", "excluded_files", "included_extensions"]:
                if key in config_dict:
                    config_dict[key] = set(config_dict[key])
                    
            return cls(**config_dict)
            
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            return None
            
    @classmethod
    def from_dict(cls, config_dict: Dict) -> "GeneratorConfig":
        """从字典创建配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            配置对象
        """
        # 转换集合
        for key in ["excluded_dirs", "excluded_files", "included_extensions"]:
            if key in config_dict:
                config_dict[key] = set(config_dict[key])
                
        return cls(**config_dict)
        
def create_default_config(
    project_root: Union[str, Path],
    docs_dir: Optional[Union[str, Path]] = None,
    modules: Optional[List[str]] = None
) -> GeneratorConfig:
    """创建默认配置
    
    Args:
        project_root: 项目根目录
        docs_dir: 文档输出目录，默认为 project_root/docs
        modules: 要处理的模块列表，默认为 project_root 下的所有目录
        
    Returns:
        默认配置对象
    """
    project_root = PathUtils.normalize_path(project_root)
    
    # 设置默认文档目录
    if docs_dir is None:
        docs_dir = project_root / "docs"
    else:
        docs_dir = PathUtils.normalize_path(docs_dir)
        
    # 查找默认模块
    if modules is None:
        modules = []
        for item in project_root.iterdir():
            if (item.is_dir() and 
                not item.name.startswith(".") and
                not item.name.startswith("__")):
                modules.append(item.name)
                
    return GeneratorConfig(
        project_root=project_root,
        docs_dir=docs_dir,
        modules=modules
    ) 