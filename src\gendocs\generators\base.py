"""
基础生成器模块
"""

import io
import mmap
import logging
import subprocess
import asyncio
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Optional, Tuple, Iterator, Dict, Any
import ast
from concurrent.futures import ThreadPoolExecutor

from ..config import GenDocsConfig, ConfigManager
from ..ai import AIProviderFactory, AIProvider, PromptManager, AIRequest
from ..utils import (
    run_command,
    ensure_dir,
    copy_file,
    is_tool_available,
    get_package_version
)

class BaseGenerator(ABC):
    """文档生成器基类
    
    提供统一的文档生成接口，支持多语言分析和AI增强功能。
    """
    
    # 大文件阈值（10MB）
    LARGE_FILE_THRESHOLD = 10 * 1024 * 1024
    
    def __init__(self, config: GenDocsConfig):
        """初始化生成器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._output_buffer = io.StringIO()
        
        # AI相关组件
        self._ai_provider: Optional[AIProvider] = None
        self._prompt_manager = PromptManager()
        
        # 线程池用于并发处理
        self._executor = ThreadPoolExecutor(max_workers=config.generation.concurrent_jobs)
        
    def _write(self, content: str) -> None:
        """写入内容到缓冲区
        
        Args:
            content: 要写入的内容
        """
        self._output_buffer.write(content)
        
    def _get_output(self) -> str:
        """获取缓冲区内容
        
        Returns:
            缓冲区内容
        """
        return self._output_buffer.getvalue()
        
    def _clear_output(self) -> None:
        """清空缓冲区"""
        self._output_buffer.seek(0)
        self._output_buffer.truncate()
        
    def generate(self) -> bool:
        """生成文档
        
        Returns:
            生成是否成功
        """
        try:
            # 确保输出目录存在
            output_dir = self.config.output.get_output_path(Path.cwd())
            ensure_dir(output_dir)
            
            # 初始化AI提供商（如果需要）
            if self._needs_ai_enhancement():
                self._initialize_ai_provider()
            
            # 执行具体生成逻辑
            return self._generate()
            
        except Exception as e:
            self.logger.error(f"生成失败: {str(e)}")
            return False
        finally:
            # 清理资源
            if self._ai_provider:
                asyncio.create_task(self._cleanup_ai_provider())
            
    @abstractmethod
    def _generate(self) -> bool:
        """具体的生成逻辑，由子类实现
        
        Returns:
            生成是否成功
        """
        pass
        
    def find_source_files(self, path: Path, extensions: Optional[List[str]] = None) -> List[Path]:
        """查找指定路径下的源代码文件
        
        Args:
            path: 要搜索的路径
            extensions: 文件扩展名列表，默认从配置获取
            
        Returns:
            找到的源代码文件列表
        """
        if extensions is None:
            extensions = [".py"]  # 默认支持Python，未来扩展其他语言
        
        files = []
        if path.is_file():
            if self._should_process_file(path, extensions):
                files.append(path)
        else:
            # 构建搜索模式
            patterns = []
            for ext in extensions:
                patterns.extend([f"*{ext}", f"**/*{ext}"])
            
            for pattern in patterns:
                for file_path in path.glob(pattern):
                    if (file_path.is_file() and 
                        self._should_process_file(file_path, extensions) and
                        not self._is_excluded_path(file_path)):
                        files.append(file_path)
        
        return sorted(files)  # 保持结果稳定
        
    def get_relative_path(self, path: Path, base_path: Optional[Path] = None) -> Path:
        """获取相对路径
        
        Args:
            path: 完整路径
            base_path: 基础路径，默认为当前工作目录
            
        Returns:
            相对路径
        """
        if base_path is None:
            base_path = Path.cwd()
        
        try:
            return path.relative_to(base_path)
        except ValueError:
            return path
            
    def get_module_name(self, path: Path) -> str:
        """获取模块名称
        
        Args:
            path: 文件路径
            
        Returns:
            模块名称
        """
        rel_path = self.get_relative_path(path)
        parts = list(rel_path.parts)
        
        # 移除.py扩展名
        if parts[-1].endswith(".py"):
            parts[-1] = parts[-1][:-3]
            
        return ".".join(parts)
        
    def read_file_content(self, path: Path) -> Optional[str]:
        """读取文件内容
        
        对大文件使用mmap，小文件使用普通读取
        
        Args:
            path: 文件路径
            
        Returns:
            文件内容，如果读取失败则返回None
        """
        try:
            file_size = path.stat().st_size
            
            # 大文件使用mmap
            if file_size >= self.LARGE_FILE_THRESHOLD:
                with open(path, 'rb') as f:
                    with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                        return mm.read().decode('utf-8')
            
            # 小文件直接读取
            with open(path, 'r', encoding='utf-8') as f:
                return f.read()
                
        except Exception as e:
            self.logger.warning(f"读取文件 {path} 失败: {str(e)}")
            return None
            
    def get_file_docstring(self, path: Path) -> Optional[str]:
        """获取文件的文档字符串
        
        Args:
            path: 文件路径
            
        Returns:
            文档字符串，如果没有则返回None
        """
        content = self.read_file_content(path)
        if not content:
            return None
            
        try:
            # 使用exec模式解析，提高性能
            tree = ast.parse(content, mode='exec')
            
            # 查找模块级docstring
            if (isinstance(tree, ast.Module) and 
                tree.body and 
                isinstance(tree.body[0], ast.Expr) and
                isinstance(tree.body[0].value, ast.Str)):
                return tree.body[0].value.s.strip()
                
            return None
            
        except Exception as e:
            self.logger.warning(f"解析文件 {path} 失败: {str(e)}")
            return None
            
    def run_tool(self, cmd: str, cwd: Optional[Path] = None) -> Tuple[bool, str]:
        """运行外部工具
        
        Args:
            cmd: 要运行的命令
            cwd: 工作目录
            
        Returns:
            (是否成功, 输出信息)的元组
        """
        try:
            returncode, stdout, stderr = run_command(
                cmd,
                cwd=cwd or self.config.project_root,
                capture_output=True,
                check=False
            )
            
            if returncode != 0:
                self.logger.error(f"命令执行失败: {stderr or stdout}")
                return False, stderr or stdout
                
            return True, stdout
            
        except Exception as e:
            self.logger.error(f"命令执行出错: {str(e)}")
            return False, str(e)
            
    def ensure_tool(self, name: str, package: Optional[str] = None) -> bool:
        """确保工具可用
        
        Args:
            name: 工具名称
            package: 对应的Python包名，如果需要安装
            
        Returns:
            工具是否可用
        """
        if is_tool_available(name):
            return True
            
        if package:
            self.logger.info(f"工具 {name} 不可用，尝试安装 {package}...")
            try:
                returncode, _, stderr = run_command(
                    f"{self.config.python_executable} -m pip install {package}",
                    check=False
                )
                if returncode == 0:
                    self.logger.info(f"✓ {package} 已安装")
                    return True
                else:
                    self.logger.error(f"安装失败: {stderr}")
            except Exception as e:
                self.logger.error(f"安装出错: {str(e)}")
                
        return False
        
    def write_to_file(self, file_path: Path, clear_buffer: bool = True) -> bool:
        """将缓冲区内容写入文件
        
        Args:
            file_path: 输出文件路径
            clear_buffer: 是否清空缓冲区
            
        Returns:
            写入是否成功
        """
        try:
            content = self._get_output()
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            if clear_buffer:
                self._clear_output()
                
            return True
            
        except Exception as e:
            self.logger.error(f"写入文件 {file_path} 失败: {str(e)}")
            return False
    
    # AI增强功能相关方法
    
    def _needs_ai_enhancement(self) -> bool:
        """检查是否需要AI增强功能"""
        return (self.__class__.__name__.lower() in 
                [doc.lower() for doc in self.config.generation.ai_enhanced_docs])
    
    def _initialize_ai_provider(self) -> None:
        """初始化AI提供商"""
        try:
            if self._ai_provider is None:
                self._ai_provider = AIProviderFactory.create_provider(self.config.ai)
                self.logger.info(f"已初始化AI提供商: {self.config.ai.provider}")
        except Exception as e:
            self.logger.warning(f"AI提供商初始化失败: {e}")
    
    async def _cleanup_ai_provider(self) -> None:
        """清理AI提供商资源"""
        if self._ai_provider and hasattr(self._ai_provider, 'close'):
            try:
                await self._ai_provider.close()
            except Exception as e:
                self.logger.warning(f"AI提供商清理失败: {e}")
    
    async def generate_ai_content(self, prompt_type: str, **kwargs) -> Optional[str]:
        """生成AI增强内容
        
        Args:
            prompt_type: 提示词类型
            **kwargs: 模板变量
            
        Returns:
            生成的内容，失败时返回None
        """
        if not self._ai_provider:
            self.logger.warning("AI提供商未初始化，跳过AI内容生成")
            return None
        
        try:
            system_prompt, user_prompt = self._prompt_manager.get_prompt(prompt_type, **kwargs)
            
            request = AIRequest(
                prompt=user_prompt,
                system_prompt=system_prompt,
                context=kwargs
            )
            
            response = await self._ai_provider.generate_completion(request)
            return response.content
            
        except Exception as e:
            self.logger.error(f"AI内容生成失败: {e}")
            return None
    
    # 多语言支持相关方法
    
    def _should_process_file(self, file_path: Path, extensions: List[str]) -> bool:
        """检查文件是否应该处理
        
        Args:
            file_path: 文件路径
            extensions: 支持的扩展名列表
            
        Returns:
            是否应该处理
        """
        return (file_path.suffix.lower() in [ext.lower() for ext in extensions] and
                file_path.name not in {'.gitignore', '.DS_Store'})
    
    def _is_excluded_path(self, file_path: Path) -> bool:
        """检查路径是否被排除
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否被排除
        """
        excluded_dirs = {
            '.git', '.venv', '__pycache__', 'node_modules',
            '.idea', '.vscode', '.pytest_cache', 'build', 'dist'
        }
        
        # 检查路径中是否包含排除的目录
        for parent in file_path.parents:
            if parent.name in excluded_dirs:
                return True
        
        return False
    
    def get_project_metadata(self) -> Dict[str, Any]:
        """获取项目元数据
        
        Returns:
            项目元数据字典
        """
        project_root = Path.cwd()
        
        metadata = {
            "project_name": project_root.name,
            "project_path": str(project_root),
            "generator": self.__class__.__name__,
            "config": {
                "ai_provider": self.config.ai.provider,
                "languages": self.config.languages.enabled,
                "output_formats": [fmt.value for fmt in self.config.output.formats]
            }
        }
        
        return metadata
    
    # 并发处理相关方法
    
    def process_files_concurrently(self, files: List[Path], process_func, *args, **kwargs) -> List[Any]:
        """并发处理文件列表
        
        Args:
            files: 文件路径列表
            process_func: 处理函数
            *args: 传递给处理函数的参数
            **kwargs: 传递给处理函数的关键字参数
            
        Returns:
            处理结果列表
        """
        if not files:
            return []
        
        # 如果文件数量较少，直接串行处理
        if len(files) <= 2:
            return [process_func(file, *args, **kwargs) for file in files]
        
        # 并发处理
        try:
            futures = []
            for file_path in files:
                future = self._executor.submit(process_func, file_path, *args, **kwargs)
                futures.append(future)
            
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    results.append(result)
                except Exception as e:
                    self.logger.warning(f"文件处理失败: {e}")
                    results.append(None)
            
            return results
            
        except Exception as e:
            self.logger.error(f"并发处理失败: {e}")
            # 回退到串行处理
            return [process_func(file, *args, **kwargs) for file in files] 