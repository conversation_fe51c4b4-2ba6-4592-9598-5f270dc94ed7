"""
代码质量分析生成器模块
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from .base import BaseGenerator

class QualityGenerator(BaseGenerator):
    """代码质量分析生成器"""
    
    def _generate(self) -> bool:
        """生成代码质量分析文档
        
        Returns:
            生成是否成功
        """
        self.logger.info("正在生成代码质量分析文档...")
        
        # 检查工具
        tools = {
            "pylint": "pylint",
            "mypy": "mypy",
            "flake8": "flake8",
            "radon": "radon"
        }
        
        for tool, package in tools.items():
            if not self.ensure_tool(tool, package):
                self.logger.warning(f"工具 {tool} 不可用，将跳过相关分析")
                
        try:
            # 写入标题
            self._write("# 代码质量分析\n\n")
            
            # 处理每个模块
            for module in self.config.modules:
                module_path = self.config.get_module_path(module)
                if not module_path.exists():
                    self.logger.warning(f"模块路径不存在: {module_path}")
                    continue
                    
                # 写入模块标题
                self._write(f"## {module}\n\n")
                
                # 代码复杂度分析
                self._write("### 代码复杂度\n\n")
                if self._analyze_complexity(module_path):
                    self._write("\n")
                else:
                    self._write("复杂度分析失败\n\n")
                    
                # 类型检查
                self._write("### 类型检查\n\n")
                if self._analyze_types(module_path):
                    self._write("\n")
                else:
                    self._write("类型检查失败\n\n")
                    
                # 代码风格检查
                self._write("### 代码风格\n\n")
                if self._analyze_style(module_path):
                    self._write("\n")
                else:
                    self._write("风格检查失败\n\n")
                    
                # 代码质量检查
                self._write("### 代码质量\n\n")
                if self._analyze_quality(module_path):
                    self._write("\n")
                else:
                    self._write("质量检查失败\n\n")
                    
            # 写入文件
            output_file = self.config.docs_dir / "quality.md"
            return self.write_to_file(output_file)
            
        except Exception as e:
            self.logger.error(f"生成代码质量分析文档失败: {str(e)}")
            return False
            
    def _analyze_complexity(self, path: Path) -> bool:
        """分析代码复杂度
        
        Args:
            path: 模块路径
            
        Returns:
            分析是否成功
        """
        self.logger.info(f"正在分析代码复杂度: {path}")
        
        # 圈复杂度
        success, output = self.run_tool(f"radon cc {path} -a -s -j")
        if success and output.strip():
            try:
                # 解析JSON输出
                data = json.loads(output)
                if data:
                    self._write("#### 圈复杂度\n\n")
                    self._write("| 文件 | 函数 | 复杂度 | 等级 |\n")
                    self._write("|------|------|--------|------|\n")
                    for file_path, functions in data.items():
                        for func in functions:
                            self._write(f"| {file_path} | {func['name']} | {func['complexity']} | {func['rank']} |\n")
                    self._write("\n")
            except json.JSONDecodeError:
                self._write("```\n")
                self._write(output)
                self._write("```\n")
            
        # 维护性指标
        success, output = self.run_tool(f"radon mi {path} -s -j")
        if success and output.strip():
            try:
                # 解析JSON输出
                data = json.loads(output)
                if data:
                    self._write("#### 维护性指标\n\n")
                    self._write("| 文件 | 维护性指标 | 等级 |\n")
                    self._write("|------|------------|------|\n")
                    for file_path, mi in data.items():
                        self._write(f"| {file_path} | {mi['mi']} | {mi['rank']} |\n")
                    self._write("\n")
            except json.JSONDecodeError:
                self._write("```\n")
                self._write(output)
                self._write("```\n")
            
        return True
        
    def _analyze_types(self, path: Path) -> bool:
        """分析类型
        
        Args:
            path: 模块路径
            
        Returns:
            分析是否成功
        """
        self.logger.info(f"正在进行类型检查: {path}")
        
        success, output = self.run_tool(f"mypy {path} --no-error-summary")
        if success:
            self._write("✓ 未发现类型问题\n")
        else:
            self._write("```\n")
            self._write(output)
            self._write("```\n")
            
        return True
        
    def _analyze_style(self, path: Path) -> bool:
        """分析代码风格
        
        Args:
            path: 模块路径
            
        Returns:
            分析是否成功
        """
        self.logger.info(f"正在检查代码风格: {path}")
        
        success, output = self.run_tool(f"flake8 {path} --format=json")
        if success:
            self._write("✓ 代码风格符合规范\n")
        else:
            try:
                # 解析JSON输出
                data = json.loads(output)
                if data:
                    self._write("| 文件 | 行号 | 列号 | 错误代码 | 描述 |\n")
                    self._write("|------|------|------|-----------|------|\n")
                    for error in data:
                        self._write(f"| {error['path']} | {error['line']} | {error['column']} | {error['code']} | {error['text']} |\n")
                else:
                    self._write("✓ 代码风格符合规范\n")
            except json.JSONDecodeError:
                self._write("```\n")
                self._write(output)
                self._write("```\n")
            
        return True
        
    def _analyze_quality(self, path: Path) -> bool:
        """分析代码质量
        
        Args:
            path: 模块路径
            
        Returns:
            分析是否成功
        """
        self.logger.info(f"正在分析代码质量: {path}")
        
        success, output = self.run_tool(f"pylint {path} --output-format=json")
        if success:
            self._write("✓ 代码质量检查通过\n")
        else:
            try:
                # 解析JSON输出
                data = json.loads(output)
                if data:
                    self._write("| 文件 | 行号 | 类型 | 符号 | 消息 |\n")
                    self._write("|------|------|------|------|------|\n")
                    for message in data:
                        self._write(f"| {message['path']} | {message['line']} | {message['type']} | {message['symbol']} | {message['message']} |\n")
                else:
                    self._write("✓ 代码质量检查通过\n")
            except json.JSONDecodeError:
                self._write("```\n")
                self._write(output)
                self._write("```\n")
            
        return True 