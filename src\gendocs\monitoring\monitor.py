"""
性能监控主模块

提供统一的性能监控接口和后台监控服务。
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, Callable

from .metrics import MetricsCollector, get_metrics_collector
from .profiler import ProfileManager, ProfilerConfig
from .reporter import PerformanceReporter, ReportConfig


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 metrics_collector: Optional[MetricsCollector] = None,
                 profiler_config: Optional[ProfilerConfig] = None,
                 reporter_config: Optional[ReportConfig] = None):
        """初始化性能监控器
        
        Args:
            metrics_collector: 指标收集器
            profiler_config: 性能分析器配置
            reporter_config: 报告器配置
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 组件初始化
        self.metrics_collector = metrics_collector or get_metrics_collector()
        self.profile_manager = ProfileManager(profiler_config or ProfilerConfig())
        self.reporter = PerformanceReporter(reporter_config or ReportConfig())
        
        # 监控状态
        self._monitoring_active = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        # 配置
        self.system_metrics_interval = 30  # 系统指标收集间隔(秒)
        self.auto_report_interval = 3600  # 自动报告间隔(秒)
        
        # 回调函数
        self._alert_callbacks: Dict[str, Callable] = {}
        
        # 性能阈值
        self.performance_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'avg_generation_time': 30.0,
            'ai_failure_rate': 20.0,  # 百分比
            'error_rate': 10.0  # 每小时错误数
        }
        
        self.logger.info("性能监控器已初始化")
    
    def start_monitoring(self) -> None:
        """启动性能监控"""
        if self._monitoring_active:
            self.logger.warning("性能监控已在运行")
            return
        
        self._monitoring_active = True
        self._stop_event.clear()
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="PerformanceMonitor",
            daemon=True
        )
        self._monitor_thread.start()
        
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止性能监控"""
        if not self._monitoring_active:
            return
        
        self._monitoring_active = False
        self._stop_event.set()
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        last_system_metrics_time = 0
        last_report_time = 0
        last_threshold_check_time = 0
        
        while self._monitoring_active and not self._stop_event.is_set():
            try:
                current_time = time.time()
                
                # 收集系统指标
                if current_time - last_system_metrics_time >= self.system_metrics_interval:
                    self.metrics_collector.update_system_metrics()
                    last_system_metrics_time = current_time
                
                # 检查性能阈值
                if current_time - last_threshold_check_time >= 60:  # 每分钟检查一次
                    self._check_performance_thresholds()
                    last_threshold_check_time = current_time
                
                # 自动生成报告
                if (self.auto_report_interval > 0 and 
                    current_time - last_report_time >= self.auto_report_interval):
                    try:
                        self._generate_auto_report()
                        last_report_time = current_time
                    except Exception as e:
                        self.logger.error(f"自动生成报告失败: {e}")
                
                # 短暂休眠
                self._stop_event.wait(min(5.0, self.system_metrics_interval / 6))
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)
    
    def _check_performance_thresholds(self) -> None:
        """检查性能阈值"""
        try:
            # 获取最新系统指标
            system_metrics_history = self.metrics_collector.get_system_metrics_history(duration_minutes=5)
            if not system_metrics_history:
                return
            
            latest_system_metrics = system_metrics_history[-1]
            
            # 检查CPU使用率
            if latest_system_metrics.cpu_percent > self.performance_thresholds['cpu_percent']:
                self._trigger_alert('high_cpu', {
                    'current_value': latest_system_metrics.cpu_percent,
                    'threshold': self.performance_thresholds['cpu_percent']
                })
            
            # 检查内存使用率
            if latest_system_metrics.memory_percent > self.performance_thresholds['memory_percent']:
                self._trigger_alert('high_memory', {
                    'current_value': latest_system_metrics.memory_percent,
                    'threshold': self.performance_thresholds['memory_percent']
                })
            
            # 检查文档生成性能
            metrics_summary = self.metrics_collector.get_metrics_summary()
            if metrics_summary.total_documents_generated > 0:
                avg_generation_time = (
                    metrics_summary.generation_time_seconds / 
                    metrics_summary.total_documents_generated
                )
                
                if avg_generation_time > self.performance_thresholds['avg_generation_time']:
                    self._trigger_alert('slow_generation', {
                        'current_value': avg_generation_time,
                        'threshold': self.performance_thresholds['avg_generation_time']
                    })
            
            # 检查AI失败率
            if metrics_summary.ai_requests_total > 0:
                ai_failure_rate = (
                    metrics_summary.ai_requests_failed / 
                    metrics_summary.ai_requests_total * 100
                )
                
                if ai_failure_rate > self.performance_thresholds['ai_failure_rate']:
                    self._trigger_alert('high_ai_failure_rate', {
                        'current_value': ai_failure_rate,
                        'threshold': self.performance_thresholds['ai_failure_rate']
                    })
            
        except Exception as e:
            self.logger.error(f"检查性能阈值失败: {e}")
    
    def _trigger_alert(self, alert_type: str, data: Dict[str, Any]) -> None:
        """触发性能警报
        
        Args:
            alert_type: 警报类型
            data: 警报数据
        """
        alert_info = {
            'type': alert_type,
            'timestamp': datetime.now(),
            'data': data
        }
        
        self.logger.warning(f"性能警报: {alert_type}, 数据: {data}")
        
        # 调用注册的警报回调
        callback = self._alert_callbacks.get(alert_type)
        if callback:
            try:
                callback(alert_info)
            except Exception as e:
                self.logger.error(f"执行警报回调失败: {e}")
        
        # 调用通用警报回调
        general_callback = self._alert_callbacks.get('general')
        if general_callback:
            try:
                general_callback(alert_info)
            except Exception as e:
                self.logger.error(f"执行通用警报回调失败: {e}")
    
    def _generate_auto_report(self) -> None:
        """生成自动报告"""
        try:
            report_file = self.reporter.generate_comprehensive_report(
                self.metrics_collector,
                self.profile_manager
            )
            self.logger.info(f"自动性能报告已生成: {report_file}")
        except Exception as e:
            self.logger.error(f"生成自动报告失败: {e}")
    
    def register_alert_callback(self, alert_type: str, callback: Callable) -> None:
        """注册警报回调函数
        
        Args:
            alert_type: 警报类型 ('high_cpu', 'high_memory', 'slow_generation', 'high_ai_failure_rate', 'general')
            callback: 回调函数
        """
        self._alert_callbacks[alert_type] = callback
        self.logger.info(f"已注册警报回调: {alert_type}")
    
    def unregister_alert_callback(self, alert_type: str) -> None:
        """取消注册警报回调
        
        Args:
            alert_type: 警报类型
        """
        if alert_type in self._alert_callbacks:
            del self._alert_callbacks[alert_type]
            self.logger.info(f"已取消注册警报回调: {alert_type}")
    
    def set_performance_threshold(self, metric_name: str, threshold_value: float) -> None:
        """设置性能阈值
        
        Args:
            metric_name: 指标名称
            threshold_value: 阈值
        """
        self.performance_thresholds[metric_name] = threshold_value
        self.logger.info(f"已设置性能阈值: {metric_name} = {threshold_value}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前监控状态
        
        Returns:
            监控状态信息
        """
        metrics_summary = self.metrics_collector.get_metrics_summary()
        
        # 获取最新系统指标
        system_metrics_history = self.metrics_collector.get_system_metrics_history(duration_minutes=1)
        current_system_metrics = system_metrics_history[-1] if system_metrics_history else None
        
        return {
            'monitoring_active': self._monitoring_active,
            'uptime_seconds': time.time() - (self.metrics_collector._start_time if hasattr(self.metrics_collector, '_start_time') else time.time()),
            'current_system_metrics': {
                'cpu_percent': current_system_metrics.cpu_percent if current_system_metrics else 0,
                'memory_percent': current_system_metrics.memory_percent if current_system_metrics else 0,
                'memory_used_mb': current_system_metrics.memory_used_mb if current_system_metrics else 0
            } if current_system_metrics else {},
            'performance_summary': {
                'documents_generated': metrics_summary.total_documents_generated,
                'documents_success_rate': (
                    metrics_summary.successful_generations / max(metrics_summary.total_documents_generated, 1) * 100
                ),
                'ai_requests': metrics_summary.ai_requests_total,
                'ai_success_rate': (
                    metrics_summary.ai_requests_successful / max(metrics_summary.ai_requests_total, 1) * 100
                ),
                'total_errors': metrics_summary.total_errors
            },
            'thresholds': self.performance_thresholds
        }
    
    def generate_report_now(self) -> Path:
        """立即生成性能报告
        
        Returns:
            报告文件路径
        """
        return self.reporter.generate_comprehensive_report(
            self.metrics_collector,
            self.profile_manager
        )
    
    def export_metrics(self, file_path: Path) -> None:
        """导出指标数据
        
        Args:
            file_path: 导出文件路径
        """
        self.metrics_collector.export_metrics(file_path)
    
    def reset_metrics(self) -> None:
        """重置所有性能指标"""
        self.metrics_collector.reset_metrics()
        self.profile_manager.reset_function_profiles()
        self.logger.info("所有性能指标已重置")
    
    def get_performance_summary(self) -> str:
        """获取性能摘要文本
        
        Returns:
            性能摘要
        """
        return self.reporter.generate_simple_summary(self.metrics_collector)
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_monitoring()


# 全局性能监控器实例
_global_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例
    
    Returns:
        性能监控器实例
    """
    global _global_performance_monitor
    if _global_performance_monitor is None:
        _global_performance_monitor = PerformanceMonitor()
    return _global_performance_monitor


def reset_global_performance_monitor() -> None:
    """重置全局性能监控器"""
    global _global_performance_monitor
    if _global_performance_monitor:
        _global_performance_monitor.stop_monitoring()
    _global_performance_monitor = None


# 便捷的装饰器和上下文管理器
def monitor_performance(name: str = None):
    """性能监控装饰器
    
    Args:
        name: 监控名称
    """
    def decorator(func):
        monitor = get_performance_monitor()
        return monitor.profile_manager.profile_decorator(name)(func)
    return decorator


class performance_context:
    """性能监控上下文管理器"""
    
    def __init__(self, name: str):
        self.name = name
        self.monitor = get_performance_monitor()
    
    def __enter__(self):
        return self.monitor.profile_manager.profile_context(self.name).__enter__()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        return self.monitor.profile_manager.profile_context(self.name).__exit__(exc_type, exc_val, exc_tb)