"""
性能监控系统单元测试
"""

import pytest
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

from gendocs.monitoring.metrics import (
    MetricsCollector, PerformanceMetrics, SystemMetrics, MetricType, 
    get_metrics_collector, reset_global_metrics_collector
)
from gendocs.monitoring.profiler import ProfileManager, ProfilerConfig, profile_function
from gendocs.monitoring.reporter import PerformanceReporter, ReportConfig
from gendocs.monitoring.monitor import PerformanceMonitor, get_performance_monitor


class TestMetricsCollector:
    """指标收集器测试类"""
    
    def test_init(self):
        """测试指标收集器初始化"""
        collector = MetricsCollector()
        assert collector is not None
        assert collector.max_history == 1000
        assert len(collector._metric_points) == 0
    
    def test_start_end_timer(self):
        """测试计时器功能"""
        collector = MetricsCollector()
        
        # 开始计时
        collector.start_timer("test_operation")
        assert "test_operation" in collector._timers
        
        # 等待一段时间
        time.sleep(0.1)
        
        # 结束计时
        duration = collector.end_timer("test_operation")
        assert duration >= 0.1
        assert "test_operation" not in collector._timers
    
    def test_timer_not_started(self):
        """测试未启动的计时器"""
        collector = MetricsCollector()
        
        # 尝试结束未启动的计时器
        duration = collector.end_timer("nonexistent_timer")
        assert duration == 0.0
    
    def test_increment_counter(self):
        """测试计数器功能"""
        collector = MetricsCollector()
        
        # 增加计数器
        collector.increment_counter("test_counter", 5)
        assert collector._counters["test_counter"] == 5
        
        # 再次增加
        collector.increment_counter("test_counter", 3)
        assert collector._counters["test_counter"] == 8
    
    def test_set_gauge(self):
        """测试仪表功能"""
        collector = MetricsCollector()
        
        collector.set_gauge("cpu_usage", 75.5)
        assert collector._gauges["cpu_usage"] == 75.5
        
        # 更新值
        collector.set_gauge("cpu_usage", 80.0)
        assert collector._gauges["cpu_usage"] == 80.0
    
    def test_record_histogram(self):
        """测试直方图功能"""
        collector = MetricsCollector()
        
        # 记录多个值
        values = [1.0, 2.0, 3.0, 4.0, 5.0]
        for value in values:
            collector.record_histogram("response_time", value)
        
        assert len(collector._histograms["response_time"]) == 5
        assert collector._histograms["response_time"] == values
    
    def test_record_document_generation(self):
        """测试文档生成指标记录"""
        collector = MetricsCollector()
        
        # 记录成功的生成
        collector.record_document_generation(True, 2.5, "overview")
        
        metrics = collector.get_metrics_summary()
        assert metrics.total_documents_generated == 1
        assert metrics.successful_generations == 1
        assert metrics.failed_generations == 0
        assert metrics.generation_time_seconds == 2.5
        
        # 记录失败的生成
        collector.record_document_generation(False, 1.0, "api")
        
        metrics = collector.get_metrics_summary()
        assert metrics.total_documents_generated == 2
        assert metrics.successful_generations == 1
        assert metrics.failed_generations == 1
    
    def test_record_ai_request(self):
        """测试AI请求指标记录"""
        collector = MetricsCollector()
        
        # 记录成功的AI请求
        collector.record_ai_request(True, 1.5, 100, "openai")
        
        metrics = collector.get_metrics_summary()
        assert metrics.ai_requests_total == 1
        assert metrics.ai_requests_successful == 1
        assert metrics.ai_response_time_seconds == 1.5
        assert metrics.ai_tokens_consumed == 100
        
        # 记录失败的AI请求
        collector.record_ai_request(False, 3.0, 0, "openai")
        
        metrics = collector.get_metrics_summary()
        assert metrics.ai_requests_total == 2
        assert metrics.ai_requests_failed == 1
    
    def test_record_error(self):
        """测试错误记录"""
        collector = MetricsCollector()
        
        collector.record_error("config_error", "Configuration not found")
        collector.record_error("network_error", "Connection timeout")
        collector.record_error("config_error", "Invalid configuration")
        
        metrics = collector.get_metrics_summary()
        assert metrics.total_errors == 3
        assert metrics.error_types["config_error"] == 2
        assert metrics.error_types["network_error"] == 1
    
    @patch('gendocs.monitoring.metrics.SystemMetrics.collect_current')
    def test_update_system_metrics(self, mock_collect):
        """测试系统指标更新"""
        mock_metrics = SystemMetrics(
            cpu_percent=50.0,
            memory_percent=60.0,
            memory_used_mb=1024.0
        )
        mock_collect.return_value = mock_metrics
        
        collector = MetricsCollector()
        collector.update_system_metrics()
        
        # 验证峰值内存被更新
        assert collector._metrics.peak_memory_usage_mb == 1024.0
        
        # 再次更新为更高的值
        mock_metrics.memory_used_mb = 2048.0
        collector.update_system_metrics()
        assert collector._metrics.peak_memory_usage_mb == 2048.0
    
    def test_get_histogram_stats(self):
        """测试直方图统计"""
        collector = MetricsCollector()
        
        # 添加一些数据
        values = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0]
        for value in values:
            collector.record_histogram("test_metric", value)
        
        stats = collector.get_histogram_stats("test_metric")
        
        assert stats['count'] == 10
        assert stats['min'] == 1.0
        assert stats['max'] == 10.0
        assert stats['mean'] == 5.5
        assert stats['p50'] == 5.0  # 中位数
    
    def test_export_metrics(self, temp_dir):
        """测试导出指标"""
        collector = MetricsCollector()
        
        # 添加一些测试数据
        collector.increment_counter("test_counter", 10)
        collector.set_gauge("test_gauge", 42.0)
        
        export_file = temp_dir / "metrics_export.json"
        collector.export_metrics(export_file)
        
        assert export_file.exists()
        
        # 验证导出的内容
        import json
        with open(export_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert 'timestamp' in data
        assert 'summary' in data
        assert 'counters' in data
        assert data['counters']['test_counter'] == 10
    
    def test_reset_metrics(self):
        """测试重置指标"""
        collector = MetricsCollector()
        
        # 添加一些数据
        collector.increment_counter("test_counter", 5)
        collector.set_gauge("test_gauge", 10.0)
        
        # 重置
        collector.reset_metrics()
        
        # 验证重置后的状态
        assert len(collector._counters) == 0
        assert len(collector._gauges) == 0
        assert len(collector._metric_points) == 0
        
        metrics = collector.get_metrics_summary()
        assert metrics.total_documents_generated == 0
        assert metrics.total_errors == 0
    
    def test_global_metrics_collector(self):
        """测试全局指标收集器"""
        collector1 = get_metrics_collector()
        collector2 = get_metrics_collector()
        
        # 应该返回同一个实例
        assert collector1 is collector2
        
        # 重置全局实例
        reset_global_metrics_collector()
        collector3 = get_metrics_collector()
        
        # 应该是新的实例
        assert collector1 is not collector3


class TestSystemMetrics:
    """系统指标测试类"""
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    @patch('psutil.disk_usage')
    def test_collect_current(self, mock_disk, mock_memory, mock_cpu):
        """测试收集当前系统指标"""
        # Mock psutil返回值
        mock_cpu.return_value = 50.0
        
        mock_mem = Mock()
        mock_mem.percent = 60.0
        mock_mem.used = 2 * 1024 * 1024 * 1024  # 2GB
        mock_memory.return_value = mock_mem
        
        mock_disk_usage = Mock()
        mock_disk_usage.percent = 70.0
        mock_disk.return_value = mock_disk_usage
        
        metrics = SystemMetrics.collect_current()
        
        assert metrics.cpu_percent == 50.0
        assert metrics.memory_percent == 60.0
        assert metrics.memory_used_mb == 2048.0
        assert metrics.disk_usage_percent == 70.0
    
    @patch('psutil.cpu_percent', side_effect=Exception("psutil error"))
    def test_collect_current_error_handling(self, mock_cpu):
        """测试系统指标收集错误处理"""
        metrics = SystemMetrics.collect_current()
        
        # 应该返回默认值而不是抛出异常
        assert metrics.cpu_percent == 0.0
        assert metrics.memory_percent == 0.0


class TestProfileManager:
    """性能分析管理器测试类"""
    
    def test_init(self, temp_dir):
        """测试性能分析管理器初始化"""
        config = ProfilerConfig(profile_output_dir=temp_dir)
        manager = ProfileManager(config)
        
        assert manager.config == config
        assert manager.function_profiler is not None
    
    def test_profile_context(self, temp_dir):
        """测试性能分析上下文"""
        config = ProfilerConfig(
            enable_profiling=True,
            profile_output_dir=temp_dir
        )
        manager = ProfileManager(config)
        
        # 使用上下文管理器
        with manager.profile_context("test_operation"):
            time.sleep(0.1)  # 模拟一些工作
        
        # 验证是否生成了分析文件
        profile_files = list(temp_dir.glob("test_operation_*.prof"))
        assert len(profile_files) > 0
    
    def test_profile_decorator(self, temp_dir):
        """测试性能分析装饰器"""
        config = ProfilerConfig(
            enable_profiling=True,
            profile_output_dir=temp_dir
        )
        manager = ProfileManager(config)
        
        @manager.profile_decorator("test_function")
        def test_function():
            time.sleep(0.1)
            return "result"
        
        result = test_function()
        assert result == "result"
        
        # 验证是否生成了分析文件
        profile_files = list(temp_dir.glob("test_function_*.prof"))
        assert len(profile_files) > 0
    
    def test_function_profiler(self):
        """测试函数性能分析器"""
        config = ProfilerConfig(enable_profiling=True)
        manager = ProfileManager(config)
        
        @manager.function_profiler.profile_function("test_func")
        def test_func():
            time.sleep(0.1)
            return sum(range(1000))
        
        result = test_func()
        assert isinstance(result, int)
        
        # 获取性能统计
        stats = manager.get_function_profiles()
        if stats:  # 如果启用了性能分析
            assert isinstance(stats, dict)
    
    def test_disabled_profiling(self, temp_dir):
        """测试禁用性能分析"""
        config = ProfilerConfig(
            enable_profiling=False,
            profile_output_dir=temp_dir
        )
        manager = ProfileManager(config)
        
        # 使用上下文管理器
        with manager.profile_context("test_operation"):
            time.sleep(0.1)
        
        # 不应该生成分析文件
        profile_files = list(temp_dir.glob("*.prof"))
        assert len(profile_files) == 0


class TestPerformanceReporter:
    """性能报告器测试类"""
    
    def test_init(self, temp_dir):
        """测试性能报告器初始化"""
        config = ReportConfig(output_dir=temp_dir)
        reporter = PerformanceReporter(config)
        
        assert reporter.config == config
        assert temp_dir.exists()
    
    def test_generate_simple_summary(self):
        """测试生成简单摘要"""
        config = ReportConfig()
        reporter = PerformanceReporter(config)
        collector = MetricsCollector()
        
        # 添加一些测试数据
        collector.record_document_generation(True, 2.0, "overview")
        collector.record_ai_request(True, 1.5, 100, "openai")
        
        summary = reporter.generate_simple_summary(collector)
        
        assert isinstance(summary, str)
        assert "性能摘要" in summary
        assert "1/1 成功" in summary
    
    @patch('gendocs.monitoring.reporter.HAS_MATPLOTLIB', False)
    def test_generate_report_without_matplotlib(self, temp_dir):
        """测试无matplotlib时的报告生成"""
        config = ReportConfig(output_dir=temp_dir, include_charts=True)
        reporter = PerformanceReporter(config)
        collector = MetricsCollector()
        
        # 应该能够生成报告，只是没有图表
        report_file = reporter.generate_comprehensive_report(collector)
        assert report_file.exists()
    
    def test_collect_report_data(self):
        """测试收集报告数据"""
        config = ReportConfig()
        reporter = PerformanceReporter(config)
        collector = MetricsCollector()
        
        # 添加测试数据
        collector.record_document_generation(True, 1.0, "test")
        
        data = reporter._collect_report_data(collector, None)
        
        assert isinstance(data, dict)
        assert 'timestamp' in data
        assert 'performance_metrics' in data
        assert 'recent_metrics' in data


class TestPerformanceMonitor:
    """性能监控器测试类"""
    
    def test_init(self):
        """测试性能监控器初始化"""
        monitor = PerformanceMonitor()
        
        assert monitor.metrics_collector is not None
        assert monitor.profile_manager is not None
        assert monitor.reporter is not None
        assert monitor._monitoring_active is False
    
    def test_start_stop_monitoring(self):
        """测试启动停止监控"""
        monitor = PerformanceMonitor()
        
        # 启动监控
        monitor.start_monitoring()
        assert monitor._monitoring_active is True
        assert monitor._monitor_thread is not None
        
        # 短暂等待确保监控线程启动
        time.sleep(0.1)
        
        # 停止监控
        monitor.stop_monitoring()
        assert monitor._monitoring_active is False
    
    def test_performance_thresholds(self):
        """测试性能阈值设置"""
        monitor = PerformanceMonitor()
        
        # 设置阈值
        monitor.set_performance_threshold('cpu_percent', 90.0)
        assert monitor.performance_thresholds['cpu_percent'] == 90.0
        
        # 获取当前状态
        status = monitor.get_current_status()
        assert 'thresholds' in status
        assert status['thresholds']['cpu_percent'] == 90.0
    
    def test_alert_callbacks(self):
        """测试警报回调"""
        monitor = PerformanceMonitor()
        alert_called = []
        
        def test_callback(alert_info):
            alert_called.append(alert_info)
        
        # 注册回调
        monitor.register_alert_callback('high_cpu', test_callback)
        
        # 模拟触发警报
        monitor._trigger_alert('high_cpu', {'value': 95.0})
        
        assert len(alert_called) == 1
        assert alert_called[0]['type'] == 'high_cpu'
    
    def test_get_current_status(self):
        """测试获取当前状态"""
        monitor = PerformanceMonitor()
        
        status = monitor.get_current_status()
        
        assert isinstance(status, dict)
        assert 'monitoring_active' in status
        assert 'performance_summary' in status
        assert 'thresholds' in status
    
    def test_reset_metrics(self):
        """测试重置指标"""
        monitor = PerformanceMonitor()
        
        # 添加一些测试数据
        monitor.metrics_collector.increment_counter("test", 5)
        
        # 重置
        monitor.reset_metrics()
        
        # 验证重置
        metrics = monitor.metrics_collector.get_metrics_summary()
        assert metrics.total_documents_generated == 0
    
    def test_global_performance_monitor(self):
        """测试全局性能监控器"""
        monitor1 = get_performance_monitor()
        monitor2 = get_performance_monitor()
        
        # 应该返回同一个实例
        assert monitor1 is monitor2
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with PerformanceMonitor() as monitor:
            assert monitor._monitoring_active is True
        
        # 退出上下文后应该停止监控
        assert monitor._monitoring_active is False


class TestMonitoringIntegration:
    """监控系统集成测试类"""
    
    def test_end_to_end_monitoring(self, temp_dir):
        """测试端到端监控"""
        # 创建配置
        profiler_config = ProfilerConfig(
            enable_profiling=True,
            profile_output_dir=temp_dir / "profiles"
        )
        report_config = ReportConfig(
            output_dir=temp_dir / "reports"
        )
        
        # 创建监控器
        monitor = PerformanceMonitor(
            profiler_config=profiler_config,
            reporter_config=report_config
        )
        
        # 启动监控
        monitor.start_monitoring()
        
        try:
            # 模拟一些活动
            monitor.metrics_collector.record_document_generation(True, 1.0, "test")
            monitor.metrics_collector.record_ai_request(True, 0.5, 50, "openai")
            
            # 生成报告
            report_file = monitor.generate_report_now()
            assert report_file.exists()
            
            # 获取状态
            status = monitor.get_current_status()
            assert status['performance_summary']['documents_generated'] == 1
            
        finally:
            # 停止监控
            monitor.stop_monitoring()
    
    def test_performance_decorators(self):
        """测试性能装饰器"""
        from gendocs.monitoring import monitor_performance, performance_context
        
        @monitor_performance("test_function")
        def test_function():
            time.sleep(0.01)
            return 42
        
        result = test_function()
        assert result == 42
        
        # 测试上下文管理器
        with performance_context("test_context"):
            time.sleep(0.01)
    
    def test_metrics_threading(self):
        """测试指标收集的线程安全"""
        collector = MetricsCollector()
        
        def worker():
            for i in range(100):
                collector.increment_counter("thread_counter", 1)
                collector.set_gauge("thread_gauge", i)
        
        # 创建多个线程
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert collector._counters["thread_counter"] == 500
    
    @patch('gendocs.monitoring.metrics.psutil.cpu_percent')
    def test_threshold_checking(self, mock_cpu):
        """测试阈值检查"""
        mock_cpu.return_value = 95.0  # 高CPU使用率
        
        monitor = PerformanceMonitor()
        monitor.set_performance_threshold('cpu_percent', 80.0)
        
        alert_triggered = []
        
        def alert_callback(alert_info):
            alert_triggered.append(alert_info)
        
        monitor.register_alert_callback('high_cpu', alert_callback)
        
        # 手动触发阈值检查
        monitor._check_performance_thresholds()
        
        # 验证警报被触发
        assert len(alert_triggered) > 0
        assert alert_triggered[0]['type'] == 'high_cpu'