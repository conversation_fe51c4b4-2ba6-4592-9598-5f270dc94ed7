"""
企业级文档生成管理器

管理和协调所有企业级文档生成器。
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

from .enterprise import (
    OverviewGenerator,
    ArchitectureGenerator,
    APIGenerator,
    DevelopmentGenerator,
    DeploymentGenerator,
    UserStoryGenerator,
    ADRGenerator,
    RunbookGenerator,
    ChangelogGenerator
)
from ..config.manager import ConfigManager
from ..ai.factory import AIProviderFactory
from ..backup import BackupManager
from ..analyzers.registry import get_registry


class EnterpriseDocumentManager:
    """企业级文档生成管理器
    
    负责协调和管理所有企业级文档的生成过程。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """初始化管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化AI提供商
        self.ai_factory = AIProviderFactory()
        self.ai_provider = None
        
        # 初始化备份管理器
        self.backup_manager = BackupManager()
        
        # 初始化分析器注册表
        self.analyzer_registry = get_registry()
        
        # 注册生成器
        self.generators = self._register_generators()
    
    def _register_generators(self) -> Dict[str, Any]:
        """注册所有企业级生成器"""
        generators = {}
        
        # 基础文档生成器
        generators['overview'] = OverviewGenerator(self.config_manager, self.ai_provider)
        generators['architecture'] = ArchitectureGenerator(self.config_manager, self.ai_provider)
        generators['api'] = APIGenerator(self.config_manager, self.ai_provider)
        generators['development'] = DevelopmentGenerator(self.config_manager, self.ai_provider)
        generators['deployment'] = DeploymentGenerator(self.config_manager, self.ai_provider)
        
        # AI增强生成器
        generators['user_story'] = UserStoryGenerator(self.config_manager, self.ai_provider)
        generators['adr'] = ADRGenerator(self.config_manager, self.ai_provider)
        generators['runbook'] = RunbookGenerator(self.config_manager, self.ai_provider)
        generators['changelog'] = ChangelogGenerator(self.config_manager, self.ai_provider)
        
        return generators
    
    async def initialize_ai_provider(self) -> bool:
        """初始化AI提供商
        
        Returns:
            是否初始化成功
        """
        try:
            config = self.config_manager.get_config()
            ai_config = config.get('ai', {})
            
            if ai_config.get('enabled', False):
                provider_name = ai_config.get('provider', 'openai')
                self.ai_provider = await self.ai_factory.create_provider(provider_name, ai_config)
                
                # 更新所有生成器的AI提供商
                for generator in self.generators.values():
                    generator._ai_provider = self.ai_provider
                
                self.logger.info(f"AI提供商初始化成功: {provider_name}")
                return True
            else:
                self.logger.info("AI功能未启用")
                return True
                
        except Exception as e:
            self.logger.error(f"AI提供商初始化失败: {e}")
            return False
    
    async def generate_all_documents(self, 
                                   project_path: Path,
                                   output_dir: Optional[Path] = None,
                                   selected_generators: Optional[List[str]] = None) -> Dict[str, bool]:
        """生成所有企业级文档
        
        Args:
            project_path: 项目路径
            output_dir: 输出目录，默认为项目路径下的docs目录
            selected_generators: 选择的生成器列表，None表示全部
            
        Returns:
            生成结果字典，键为生成器名称，值为是否成功
        """
        if not output_dir:
            output_dir = project_path / 'docs'
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 确保AI提供商已初始化
        await self.initialize_ai_provider()
        
        # 分析项目
        analysis_result = self._analyze_project(project_path)
        
        # 选择要运行的生成器
        generators_to_run = selected_generators or list(self.generators.keys())
        
        # 构建通用上下文
        context = {
            'analysis_result': analysis_result,
            'project_path': project_path,
            'output_dir': output_dir
        }
        
        # 并发生成文档
        results = {}
        semaphore = asyncio.Semaphore(3)  # 限制并发数
        
        async def generate_document(generator_name: str) -> bool:
            async with semaphore:
                try:
                    generator = self.generators[generator_name]
                    self.logger.info(f"开始生成 {generator_name} 文档")
                    
                    # 检查是否需要AI支持
                    if hasattr(generator, 'requires_ai') and generator.requires_ai and not self.ai_provider:
                        self.logger.warning(f"{generator_name} 需要AI支持但AI未启用，跳过生成")
                        return False
                    
                    # 使用备份功能生成文档
                    if hasattr(generator, 'generate_with_backup'):
                        success = await generator.generate_with_backup(
                            project_path, output_dir, context,
                            f"生成{generator_name}文档前的备份"
                        )
                    else:
                        success = await generator.generate(project_path, output_dir, context)
                    
                    if success:
                        self.logger.info(f"{generator_name} 文档生成成功")
                    else:
                        self.logger.error(f"{generator_name} 文档生成失败")
                    
                    return success
                    
                except Exception as e:
                    self.logger.error(f"生成 {generator_name} 文档时出错: {e}")
                    return False
        
        # 执行并发生成
        tasks = [generate_document(name) for name in generators_to_run]
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        for i, generator_name in enumerate(generators_to_run):
            result = task_results[i]
            if isinstance(result, Exception):
                self.logger.error(f"生成器 {generator_name} 执行异常: {result}")
                results[generator_name] = False
            else:
                results[generator_name] = result
        
        # 生成汇总报告
        self._generate_summary_report(results, output_dir)
        
        return results
    
    def _analyze_project(self, project_path: Path):
        """分析项目
        
        Args:
            project_path: 项目路径
            
        Returns:
            项目分析结果
        """
        try:
            self.logger.info(f"开始分析项目: {project_path}")
            
            # 使用分析器注册表分析项目
            analysis_result = self.analyzer_registry.analyze_project(project_path)
            
            if analysis_result:
                self.logger.info(f"项目分析完成，类型: {analysis_result.project_type.value}")
            else:
                self.logger.warning("项目分析失败，将使用默认配置")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"项目分析出错: {e}")
            return None
    
    def _generate_summary_report(self, results: Dict[str, bool], output_dir: Path) -> None:
        """生成汇总报告
        
        Args:
            results: 生成结果
            output_dir: 输出目录
        """
        try:
            total_generators = len(results)
            successful_generators = sum(1 for success in results.values() if success)
            failed_generators = total_generators - successful_generators
            
            report_content = f"""# 文档生成报告

## 生成概览

- 总生成器数量: {total_generators}
- 成功生成: {successful_generators}
- 生成失败: {failed_generators}
- 成功率: {successful_generators/total_generators*100:.1f}%

## 详细结果

"""
            
            for generator_name, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                report_content += f"- {generator_name}: {status}\n"
            
            report_content += f"""
## 生成的文档

"""
            
            # 列出生成的文档文件
            doc_files = list(output_dir.glob('*.md'))
            for doc_file in sorted(doc_files):
                if doc_file.name != 'GENERATION_REPORT.md':
                    report_content += f"- [{doc_file.name}](./{doc_file.name})\n"
            
            # 写入报告文件
            report_path = output_dir / 'GENERATION_REPORT.md'
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"生成汇总报告: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {e}")
    
    def get_available_generators(self) -> List[Dict[str, str]]:
        """获取可用的生成器列表
        
        Returns:
            生成器信息列表
        """
        generator_info = []
        
        for name, generator in self.generators.items():
            info = {
                'name': name,
                'class_name': generator.__class__.__name__,
                'output_filename': getattr(generator, 'output_filename', f'{name}.md'),
                'requires_ai': getattr(generator, 'requires_ai', False),
                'description': generator.__class__.__doc__.split('\n')[0] if generator.__class__.__doc__ else ''
            }
            generator_info.append(info)
        
        return generator_info
    
    async def test_generators(self, project_path: Path) -> Dict[str, Dict[str, Any]]:
        """测试所有生成器
        
        Args:
            project_path: 测试项目路径
            
        Returns:
            测试结果
        """
        test_results = {}
        
        # 初始化AI提供商
        await self.initialize_ai_provider()
        
        # 分析项目
        analysis_result = self._analyze_project(project_path)
        
        for name, generator in self.generators.items():
            try:
                self.logger.info(f"测试生成器: {name}")
                
                # 检查生成器状态
                test_result = {
                    'name': name,
                    'available': True,
                    'requires_ai': getattr(generator, 'requires_ai', False),
                    'ai_available': self.ai_provider is not None,
                    'can_run': True,
                    'sections': [],
                    'error': None
                }
                
                # 检查是否需要AI但AI不可用
                if test_result['requires_ai'] and not test_result['ai_available']:
                    test_result['can_run'] = False
                    test_result['error'] = 'AI功能未启用但生成器需要AI支持'
                
                # 获取文档章节
                if hasattr(generator, 'get_document_sections'):
                    test_result['sections'] = generator.get_document_sections()
                
                test_results[name] = test_result
                
            except Exception as e:
                test_results[name] = {
                    'name': name,
                    'available': False,
                    'error': str(e)
                }
                self.logger.error(f"测试生成器 {name} 失败: {e}")
        
        return test_results