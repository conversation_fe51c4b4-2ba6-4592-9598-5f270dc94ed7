"""
GenDocs 测试包

提供完整的单元测试、集成测试和端到端测试套件。
"""

import sys
import os
from pathlib import Path

# 确保可以导入 src 下的模块
test_dir = Path(__file__).parent
project_root = test_dir.parent
src_dir = project_root / "src"

if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

# 测试配置
TEST_DATA_DIR = test_dir / "data"
TEST_FIXTURES_DIR = test_dir / "fixtures"
TEST_OUTPUT_DIR = test_dir / "output"

# 确保测试目录存在
TEST_DATA_DIR.mkdir(exist_ok=True)
TEST_FIXTURES_DIR.mkdir(exist_ok=True)
TEST_OUTPUT_DIR.mkdir(exist_ok=True)

__version__ = "0.2.0"
__author__ = "GenDocs Team"