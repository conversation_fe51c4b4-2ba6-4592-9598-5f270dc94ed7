"""
变更日志生成器

生成变更日志文档。
"""

from pathlib import Path
from typing import Dict, Any

from .base import EnterpriseGenerator


class ChangelogGenerator(EnterpriseGenerator):
    """变更日志生成器"""
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/changelog.md.j2"
        self.output_filename = "CHANGELOG.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成变更日志"""
        try:
            self.logger.info(f"开始生成变更日志: {project_path}")
            
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            changelog_context = self._build_changelog_context(enterprise_context)
            
            # 如果现有CHANGELOG.md文件，读取其内容
            existing_changelog = self._read_existing_changelog(project_path)
            if existing_changelog:
                changelog_context['existing_content'] = existing_changelog
            
            base_content = self.render_template(self.template_name, changelog_context)
            
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "changelog_enhancement",
                    **changelog_context
                )
                if enhanced_content:
                    base_content = enhanced_content
            
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            if self.validate_output(final_output_path):
                self.logger.info(f"变更日志生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成变更日志失败: {e}")
            return False
    
    def _build_changelog_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建变更日志上下文"""
        analysis_result = context.get('analysis_result')
        
        changelog_context = context.copy()
        changelog_context.update({
            'current_version': analysis_result.version if analysis_result else '1.0.0',
            'releases': self._generate_release_template(),
            'categories': self._get_changelog_categories(),
            'format': 'Keep a Changelog'
        })
        
        return changelog_context
    
    def _read_existing_changelog(self, project_path: Path) -> str:
        """读取现有的CHANGELOG文件"""
        changelog_files = ['CHANGELOG.md', 'CHANGELOG.rst', 'CHANGELOG.txt', 'HISTORY.md']
        
        for filename in changelog_files:
            changelog_path = project_path / filename
            if changelog_path.exists():
                try:
                    with open(changelog_path, 'r', encoding='utf-8') as f:
                        return f.read()
                except Exception as e:
                    self.logger.warning(f"读取现有CHANGELOG失败: {e}")
        
        return ""
    
    def _generate_release_template(self) -> list:
        """生成发布模板"""
        return [
            {
                'version': 'Unreleased',
                'date': None,
                'added': ['新功能列表'],
                'changed': ['变更列表'],
                'deprecated': ['废弃功能列表'],
                'removed': ['移除功能列表'],
                'fixed': ['修复问题列表'],
                'security': ['安全更新列表']
            }
        ]
    
    def _get_changelog_categories(self) -> list:
        """获取变更日志分类"""
        return [
            {'name': 'Added', 'description': '新增功能'},
            {'name': 'Changed', 'description': '功能变更'},
            {'name': 'Deprecated', 'description': '废弃功能'},
            {'name': 'Removed', 'description': '移除功能'},
            {'name': 'Fixed', 'description': '问题修复'},
            {'name': 'Security', 'description': '安全更新'}
        ]