# GenDocs 默认配置文件
# 此文件包含所有配置项的默认值

ai:
  # AI服务提供商配置
  provider: "openai"                    # 支持: openai, azure, deepseek, qwen, claude, custom
  base_url: "https://api.openai.com/v1" # API 端点地址
  api_key: ""                           # API密钥，建议通过环境变量设置
  model: "gpt-4o-mini"                  # 使用的模型名称
  max_tokens: 4000                      # 最大生成token数
  temperature: 0.3                      # 生成温度，控制随机性
  timeout: 60                           # 请求超时时间（秒）
  retry_attempts: 3                     # 失败重试次数

languages:
  # 编程语言支持配置
  enabled:                              # 启用的编程语言
    - "python"
  detection_order:                      # 项目类型检测顺序
    - "python"

generation:
  # 文档生成配置
  backup_existing: true                 # 是否备份现有文档
  backup_format: "timestamp"            # 备份格式: timestamp, incremental, none
  concurrent_jobs: 4                    # 并发处理任务数
  ai_enhanced_docs:                     # 需要AI增强的文档类型
    - "user_story_map"
    - "adr" 
    - "runbook"

output:
  # 输出配置
  base_dir: "docs"                      # 输出基础目录
  formats:                              # 输出格式
    - "markdown"
  include_metadata: true                # 是否包含元数据

templates:
  # 模板配置
  base_path: "templates"                # 模板基础路径
  custom_path: null                     # 自定义模板路径（可选）