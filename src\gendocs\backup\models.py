"""
文档备份数据模型

定义备份相关的数据结构。
"""

import json
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any


class BackupStrategy(str, Enum):
    """备份策略枚举"""
    TIMESTAMP = "timestamp"  # 基于时间戳的备份
    INCREMENTAL = "incremental"  # 增量备份
    FULL = "full"  # 完整备份
    ARCHIVE = "archive"  # 归档备份


@dataclass
class BackupInfo:
    """备份信息"""
    backup_id: str
    strategy: BackupStrategy
    created_at: datetime
    source_path: Path
    backup_path: Path
    description: Optional[str] = None
    file_count: int = 0
    total_size: int = 0  # 字节
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'backup_id': self.backup_id,
            'strategy': self.strategy.value,
            'created_at': self.created_at.isoformat(),
            'source_path': str(self.source_path),
            'backup_path': str(self.backup_path),
            'description': self.description,
            'file_count': self.file_count,
            'total_size': self.total_size,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupInfo':
        """从字典创建实例"""
        return cls(
            backup_id=data['backup_id'],
            strategy=BackupStrategy(data['strategy']),
            created_at=datetime.fromisoformat(data['created_at']),
            source_path=Path(data['source_path']),
            backup_path=Path(data['backup_path']),
            description=data.get('description'),
            file_count=data.get('file_count', 0),
            total_size=data.get('total_size', 0),
            metadata=data.get('metadata', {})
        )


@dataclass
class BackupConfig:
    """备份配置"""
    enabled: bool = True
    strategy: BackupStrategy = BackupStrategy.TIMESTAMP
    backup_dir: Optional[Path] = None
    max_backups: int = 10  # 最大备份数量
    exclude_patterns: List[str] = field(default_factory=lambda: [
        '*.tmp', '*.log', '.DS_Store', 'Thumbs.db'
    ])
    include_patterns: List[str] = field(default_factory=lambda: [
        '*.md', '*.rst', '*.txt', '*.adoc'
    ])
    compress: bool = False  # 是否压缩备份
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'enabled': self.enabled,
            'strategy': self.strategy.value,
            'backup_dir': str(self.backup_dir) if self.backup_dir else None,
            'max_backups': self.max_backups,
            'exclude_patterns': self.exclude_patterns,
            'include_patterns': self.include_patterns,
            'compress': self.compress
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupConfig':
        """从字典创建实例"""
        return cls(
            enabled=data.get('enabled', True),
            strategy=BackupStrategy(data.get('strategy', BackupStrategy.TIMESTAMP.value)),
            backup_dir=Path(data['backup_dir']) if data.get('backup_dir') else None,
            max_backups=data.get('max_backups', 10),
            exclude_patterns=data.get('exclude_patterns', []),
            include_patterns=data.get('include_patterns', []),
            compress=data.get('compress', False)
        )


@dataclass
class BackupManifest:
    """备份清单"""
    project_name: str
    project_path: Path
    backups: List[BackupInfo] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def add_backup(self, backup_info: BackupInfo) -> None:
        """添加备份记录"""
        self.backups.append(backup_info)
        self.updated_at = datetime.now()
    
    def remove_backup(self, backup_id: str) -> bool:
        """移除备份记录"""
        for i, backup in enumerate(self.backups):
            if backup.backup_id == backup_id:
                self.backups.pop(i)
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_backup(self, backup_id: str) -> Optional[BackupInfo]:
        """获取备份信息"""
        for backup in self.backups:
            if backup.backup_id == backup_id:
                return backup
        return None
    
    def get_latest_backup(self) -> Optional[BackupInfo]:
        """获取最新备份"""
        if not self.backups:
            return None
        return max(self.backups, key=lambda b: b.created_at)
    
    def cleanup_old_backups(self, max_backups: int) -> List[BackupInfo]:
        """清理旧备份，返回被清理的备份列表"""
        if len(self.backups) <= max_backups:
            return []
        
        # 按创建时间排序，保留最新的
        sorted_backups = sorted(self.backups, key=lambda b: b.created_at, reverse=True)
        to_keep = sorted_backups[:max_backups]
        to_remove = sorted_backups[max_backups:]
        
        self.backups = to_keep
        self.updated_at = datetime.now()
        
        return to_remove
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'project_name': self.project_name,
            'project_path': str(self.project_path),
            'backups': [backup.to_dict() for backup in self.backups],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupManifest':
        """从字典创建实例"""
        manifest = cls(
            project_name=data['project_name'],
            project_path=Path(data['project_path']),
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at'])
        )
        
        for backup_data in data.get('backups', []):
            backup_info = BackupInfo.from_dict(backup_data)
            manifest.backups.append(backup_info)
        
        return manifest
    
    def save_to_file(self, file_path: Path) -> None:
        """保存到文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> 'BackupManifest':
        """从文件加载"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return cls.from_dict(data)