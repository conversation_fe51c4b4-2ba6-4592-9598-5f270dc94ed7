"""
开发指南生成器

生成开发指南文档，包括环境搭建、编码规范、测试指南等。
"""

from pathlib import Path
from typing import Dict, Any, List

from .base import EnterpriseGenerator


class DevelopmentGenerator(EnterpriseGenerator):
    """开发指南生成器"""
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/dev_guide.md.j2"
        self.output_filename = "DEVELOPMENT_GUIDE.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成开发指南"""
        try:
            self.logger.info(f"开始生成开发指南: {project_path}")
            
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            dev_context = self._build_development_context(enterprise_context)
            
            base_content = self.render_template(self.template_name, dev_context)
            
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "development_guide_enhancement",
                    **dev_context
                )
                if enhanced_content:
                    base_content = enhanced_content
            
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            if self.validate_output(final_output_path):
                self.logger.info(f"开发指南生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成开发指南失败: {e}")
            return False
    
    def _build_development_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建开发特定上下文"""
        analysis_result = context.get('analysis_result')
        
        dev_context = context.copy()
        dev_context.update({
            'environment_setup': self._build_environment_setup(analysis_result),
            'coding_standards': self._build_coding_standards(analysis_result),
            'testing_guide': self._build_testing_guide(analysis_result),
            'debugging_tips': self._build_debugging_tips(analysis_result),
            'git_workflow': self._build_git_workflow(),
            'tools_setup': self._build_tools_setup(analysis_result)
        })
        
        return dev_context
    
    def _build_environment_setup(self, analysis_result) -> Dict[str, Any]:
        """构建环境搭建指南"""
        setup = {
            'prerequisites': [],
            'steps': [],
            'verification': []
        }
        
        if analysis_result:
            project_type = analysis_result.project_type.value.lower()
            
            if project_type == 'python':
                setup['prerequisites'] = ['Python 3.8+', 'pip', 'git']
                setup['steps'] = [
                    '克隆项目到本地',
                    '创建虚拟环境: python -m venv venv',
                    '激活虚拟环境',
                    '安装依赖: pip install -r requirements.txt',
                    '配置环境变量',
                    '运行测试: python -m pytest'
                ]
                setup['verification'] = [
                    'python --version',
                    'pip list',
                    'python -m pytest --version'
                ]
        
        return setup
    
    def _build_coding_standards(self, analysis_result) -> Dict[str, Any]:
        """构建编码规范"""
        standards = {
            'style_guide': 'PEP 8',
            'formatting': [],
            'naming': [],
            'documentation': []
        }
        
        if analysis_result:
            project_type = analysis_result.project_type.value.lower()
            
            if project_type == 'python':
                standards.update({
                    'style_guide': 'PEP 8 Python代码规范',
                    'formatting': [
                        '使用4个空格缩进',
                        '行长度不超过88字符',
                        '使用black进行代码格式化'
                    ],
                    'naming': [
                        '类名使用驼峰命名法',
                        '函数和变量使用下划线命名法',
                        '常量使用大写字母'
                    ],
                    'documentation': [
                        '所有公共函数必须有文档字符串',
                        '使用Google风格的文档字符串',
                        '类和模块需要详细的文档说明'
                    ]
                })
        
        return standards
    
    def _build_testing_guide(self, analysis_result) -> Dict[str, Any]:
        """构建测试指南"""
        testing = {
            'framework': 'pytest',
            'structure': [],
            'guidelines': [],
            'coverage': {}
        }
        
        if analysis_result:
            # 检查是否有测试框架依赖
            if analysis_result.dependencies:
                dep_names = {dep.name.lower() for dep in analysis_result.dependencies}
                
                if 'pytest' in dep_names:
                    testing['framework'] = 'pytest'
                elif 'unittest' in dep_names:
                    testing['framework'] = 'unittest'
            
            testing.update({
                'structure': [
                    'tests/ - 测试目录',
                    'tests/unit/ - 单元测试',
                    'tests/integration/ - 集成测试',
                    'conftest.py - pytest配置'
                ],
                'guidelines': [
                    '每个模块都应有对应的测试文件',
                    '测试函数名要清晰描述测试内容',
                    '使用fixture管理测试数据',
                    '保持测试的独立性和可重复性'
                ],
                'coverage': {
                    'target': '80%以上',
                    'command': 'pytest --cov=src --cov-report=html',
                    'exclude': ['migrations/', 'tests/', 'venv/']
                }
            })
        
        return testing
    
    def _build_debugging_tips(self, analysis_result) -> List[Dict[str, str]]:
        """构建调试技巧"""
        tips = [
            {
                'title': '使用日志记录',
                'description': '合理使用logging模块记录程序运行状态',
                'example': 'import logging\nlogger = logging.getLogger(__name__)'
            },
            {
                'title': '断点调试',
                'description': '使用pdb或IDE断点进行代码调试',
                'example': 'import pdb; pdb.set_trace()'
            },
            {
                'title': '单元测试',
                'description': '编写单元测试验证代码逻辑',
                'example': 'pytest tests/test_module.py -v'
            }
        ]
        
        if analysis_result and analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            if framework == 'django':
                tips.append({
                    'title': 'Django调试',
                    'description': '使用Django调试工具栏和shell',
                    'example': 'python manage.py shell\npython manage.py runserver --settings=debug_settings'
                })
        
        return tips
    
    def _build_git_workflow(self) -> Dict[str, Any]:
        """构建Git工作流"""
        return {
            'branching_model': 'Git Flow',
            'branch_types': [
                {'name': 'main', 'description': '主分支，用于发布'},
                {'name': 'develop', 'description': '开发分支，用于集成'},
                {'name': 'feature/*', 'description': '功能分支，用于新功能开发'},
                {'name': 'bugfix/*', 'description': '修复分支，用于bug修复'},
                {'name': 'release/*', 'description': '发布分支，用于版本发布'}
            ],
            'commit_message': {
                'format': 'type(scope): description',
                'types': ['feat', 'fix', 'docs', 'style', 'refactor', 'test', 'chore'],
                'example': 'feat(auth): add user login functionality'
            },
            'workflow': [
                '从develop分支创建feature分支',
                '在feature分支上开发新功能',
                '完成后创建Pull Request',
                '代码审查通过后合并到develop',
                '定期从develop创建release分支',
                '测试通过后合并到main和develop'
            ]
        }
    
    def _build_tools_setup(self, analysis_result) -> List[Dict[str, str]]:
        """构建工具配置"""
        tools = [
            {
                'name': 'VS Code',
                'description': '推荐的代码编辑器',
                'extensions': 'Python, GitLens, Pylance'
            },
            {
                'name': 'Git',
                'description': '版本控制工具',
                'setup': 'git config --global user.name "Your Name"'
            }
        ]
        
        if analysis_result:
            project_type = analysis_result.project_type.value.lower()
            
            if project_type == 'python':
                tools.extend([
                    {
                        'name': 'Black',
                        'description': '代码格式化工具',
                        'setup': 'pip install black'
                    },
                    {
                        'name': 'Flake8',
                        'description': '代码检查工具',
                        'setup': 'pip install flake8'
                    }
                ])
        
        return tools