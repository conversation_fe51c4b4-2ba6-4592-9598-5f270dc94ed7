"""
用户故事地图生成器

基于AI生成用户故事地图文档。
"""

from pathlib import Path
from typing import Dict, Any

from .base import EnterpriseGenerator


class UserStoryGenerator(EnterpriseGenerator):
    """用户故事地图生成器"""
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "ai_enhanced/user_story_map.md.j2"
        self.output_filename = "USER_STORY_MAP.md"
        self.requires_ai = True  # 标记此生成器需要AI
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成用户故事地图"""
        if not self._ai_provider:
            self.logger.warning("用户故事地图生成器需要AI支持，跳过生成")
            return False
        
        try:
            self.logger.info(f"开始生成用户故事地图: {project_path}")
            
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            # 使用AI生成用户故事内容
            story_content = await self.generate_ai_content(
                "user_story_generation",
                **enterprise_context
            )
            
            if not story_content:
                self.logger.error("AI生成用户故事内容失败")
                return False
            
            # 构建最终上下文
            story_context = enterprise_context.copy()
            story_context['ai_generated_content'] = story_content
            
            # 渲染模板
            base_content = self.render_template(self.template_name, story_context)
            
            # 写入文件
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            if self.validate_output(final_output_path):
                self.logger.info(f"用户故事地图生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成用户故事地图失败: {e}")
            return False