"""
项目分析器单元测试
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from gendocs.analyzers.base import BaseAnalyzer, ProjectAnalysisResult
from gendocs.analyzers.python import PythonAnalyzer
from gendocs.analyzers.types import ProjectType, FrameworkType
from gendocs.analyzers.registry import AnalyzerRegistry, get_registry


class TestBaseAnalyzer:
    """基础分析器测试类"""
    
    def test_init(self):
        """测试分析器初始化"""
        analyzer = BaseAnalyzer()
        assert analyzer is not None
    
    def test_can_analyze_not_implemented(self):
        """测试can_analyze方法未实现"""
        analyzer = BaseAnalyzer()
        with pytest.raises(NotImplementedError):
            analyzer.can_analyze(Path("/test"))
    
    def test_analyze_not_implemented(self):
        """测试analyze方法未实现"""
        analyzer = BaseAnalyzer()
        with pytest.raises(NotImplementedError):
            analyzer.analyze(Path("/test"))


class TestPythonAnalyzer:
    """Python分析器测试类"""
    
    def test_init(self):
        """测试Python分析器初始化"""
        analyzer = PythonAnalyzer()
        assert analyzer is not None
        assert hasattr(analyzer, 'supported_frameworks')
    
    def test_can_analyze_python_project(self, mock_project_dir):
        """测试能否分析Python项目"""
        analyzer = PythonAnalyzer()
        assert analyzer.can_analyze(mock_project_dir) is True
    
    def test_can_analyze_non_python_project(self, temp_dir):
        """测试不能分析非Python项目"""
        # 创建一个没有Python文件的目录
        non_python_dir = temp_dir / "non_python"
        non_python_dir.mkdir()
        (non_python_dir / "README.md").write_text("# Not a Python project")
        
        analyzer = PythonAnalyzer()
        assert analyzer.can_analyze(non_python_dir) is False
    
    def test_analyze_simple_project(self, mock_project_dir):
        """测试分析简单Python项目"""
        analyzer = PythonAnalyzer()
        result = analyzer.analyze(mock_project_dir)
        
        assert isinstance(result, ProjectAnalysisResult)
        assert result.project_name == mock_project_dir.name
        assert result.project_type == ProjectType.LIBRARY
        assert result.version is not None
    
    def test_analyze_complex_project(self, complex_project_dir):
        """测试分析复杂Python项目"""
        analyzer = PythonAnalyzer()
        result = analyzer.analyze(complex_project_dir)
        
        assert isinstance(result, ProjectAnalysisResult)
        assert result.project_name == "myproject"
        assert result.version == "2.1.0"
        assert result.author == "Test Author"
        assert result.description is not None
        assert len(result.dependencies) > 0
    
    def test_detect_framework_none(self, mock_project_dir):
        """测试检测无框架项目"""
        analyzer = PythonAnalyzer()
        framework = analyzer._detect_framework(mock_project_dir)
        assert framework == FrameworkType.NONE
    
    def test_detect_framework_django(self, temp_dir):
        """测试检测Django框架"""
        django_dir = temp_dir / "django_project"
        django_dir.mkdir()
        (django_dir / "manage.py").write_text("# Django manage.py")
        (django_dir / "settings.py").write_text("# Django settings")
        
        analyzer = PythonAnalyzer()
        framework = analyzer._detect_framework(django_dir)
        assert framework == FrameworkType.DJANGO
    
    def test_detect_framework_flask(self, temp_dir):
        """测试检测Flask框架"""
        flask_dir = temp_dir / "flask_project"
        flask_dir.mkdir()
        
        # 创建包含Flask导入的文件
        (flask_dir / "app.py").write_text("""
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return 'Hello, World!'
""")
        
        analyzer = PythonAnalyzer()
        framework = analyzer._detect_framework(flask_dir)
        assert framework == FrameworkType.FLASK
    
    def test_detect_framework_fastapi(self, temp_dir):
        """测试检测FastAPI框架"""
        fastapi_dir = temp_dir / "fastapi_project"
        fastapi_dir.mkdir()
        
        # 创建包含FastAPI导入的文件
        (fastapi_dir / "main.py").write_text("""
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}
""")
        
        analyzer = PythonAnalyzer()
        framework = analyzer._detect_framework(fastapi_dir)
        assert framework == FrameworkType.FASTAPI
    
    def test_parse_setup_py(self, complex_project_dir):
        """测试解析setup.py文件"""
        analyzer = PythonAnalyzer()
        setup_info = analyzer._parse_setup_py(complex_project_dir / "setup.py")
        
        assert setup_info['name'] == 'myproject'
        assert setup_info['version'] == '2.1.0'
        assert setup_info['author'] == 'Test Author'
        assert 'description' in setup_info
    
    def test_parse_pyproject_toml(self, complex_project_dir):
        """测试解析pyproject.toml文件"""
        analyzer = PythonAnalyzer()
        
        # 检查是否有pyproject.toml文件
        pyproject_file = complex_project_dir / "pyproject.toml"
        if pyproject_file.exists():
            project_info = analyzer._parse_pyproject_toml(pyproject_file)
            assert project_info is not None
    
    def test_parse_requirements(self, complex_project_dir):
        """测试解析requirements.txt文件"""
        analyzer = PythonAnalyzer()
        deps = analyzer._parse_requirements(complex_project_dir / "requirements.txt")
        
        assert len(deps) > 0
        # 验证依赖项结构
        for dep in deps:
            assert hasattr(dep, 'name')
            assert hasattr(dep, 'version')
    
    def test_detect_apis_in_code(self, complex_project_dir):
        """测试检测代码中的API"""
        analyzer = PythonAnalyzer()
        apis = analyzer._detect_apis(complex_project_dir)
        
        # 根据示例代码，应该能检测到API相关的方法
        assert isinstance(apis, list)
    
    def test_analyze_code_quality(self, complex_project_dir):
        """测试代码质量分析"""
        analyzer = PythonAnalyzer()
        metrics = analyzer._analyze_code_quality(complex_project_dir)
        
        assert hasattr(metrics, 'total_files')
        assert hasattr(metrics, 'total_lines')
        assert hasattr(metrics, 'code_lines')
        assert metrics.total_files > 0
        assert metrics.total_lines > 0
    
    def test_extract_modules(self, complex_project_dir):
        """测试提取模块信息"""
        analyzer = PythonAnalyzer()
        modules = analyzer._extract_modules(complex_project_dir)
        
        assert isinstance(modules, list)
        assert len(modules) > 0
        
        # 验证模块结构
        for module in modules:
            assert hasattr(module, 'name')
            assert hasattr(module, 'file_count')
    
    def test_analyze_nonexistent_project(self, temp_dir):
        """测试分析不存在的项目"""
        analyzer = PythonAnalyzer()
        nonexistent_path = temp_dir / "nonexistent"
        
        result = analyzer.analyze(nonexistent_path)
        assert result is None
    
    def test_analyze_empty_project(self, temp_dir):
        """测试分析空项目"""
        empty_dir = temp_dir / "empty"
        empty_dir.mkdir()
        
        analyzer = PythonAnalyzer()
        result = analyzer.analyze(empty_dir)
        
        # 根据实现决定是返回None还是基本的分析结果
        if result is not None:
            assert result.project_name == "empty"


class TestAnalyzerRegistry:
    """分析器注册表测试类"""
    
    def test_init(self):
        """测试注册表初始化"""
        registry = AnalyzerRegistry()
        assert registry is not None
        assert hasattr(registry, '_analyzers')
    
    def test_register_analyzer(self):
        """测试注册分析器"""
        registry = AnalyzerRegistry()
        analyzer = PythonAnalyzer()
        
        registry.register(analyzer)
        assert len(registry._analyzers) >= 1
    
    def test_get_suitable_analyzer(self, mock_project_dir):
        """测试获取合适的分析器"""
        registry = AnalyzerRegistry()
        registry.register(PythonAnalyzer())
        
        analyzer = registry.get_suitable_analyzer(mock_project_dir)
        assert analyzer is not None
        assert isinstance(analyzer, PythonAnalyzer)
    
    def test_get_suitable_analyzer_none(self, temp_dir):
        """测试没有合适分析器的情况"""
        registry = AnalyzerRegistry()
        
        # 创建一个无法识别的项目
        unknown_dir = temp_dir / "unknown"
        unknown_dir.mkdir()
        (unknown_dir / "unknown.xyz").write_text("unknown content")
        
        analyzer = registry.get_suitable_analyzer(unknown_dir)
        assert analyzer is None
    
    def test_analyze_project(self, mock_project_dir):
        """测试分析项目"""
        registry = AnalyzerRegistry()
        registry.register(PythonAnalyzer())
        
        result = registry.analyze_project(mock_project_dir)
        assert isinstance(result, ProjectAnalysisResult)
    
    def test_analyze_project_no_analyzer(self, temp_dir):
        """测试无分析器可用的情况"""
        registry = AnalyzerRegistry()
        
        unknown_dir = temp_dir / "unknown"
        unknown_dir.mkdir()
        
        result = registry.analyze_project(unknown_dir)
        assert result is None
    
    def test_list_analyzers(self):
        """测试列出分析器"""
        registry = AnalyzerRegistry()
        registry.register(PythonAnalyzer())
        
        analyzers = registry.list_analyzers()
        assert len(analyzers) >= 1
        assert 'PythonAnalyzer' in analyzers
    
    def test_global_registry(self):
        """测试全局注册表"""
        global_registry = get_registry()
        assert global_registry is not None
        assert isinstance(global_registry, AnalyzerRegistry)
    
    def test_auto_register_analyzers(self):
        """测试自动注册分析器"""
        from gendocs.analyzers.registry import auto_register_analyzers
        
        registry = AnalyzerRegistry()
        auto_register_analyzers(registry)
        
        analyzers = registry.list_analyzers()
        assert 'PythonAnalyzer' in analyzers


class TestProjectAnalysisResult:
    """项目分析结果测试类"""
    
    def test_init(self):
        """测试分析结果初始化"""
        result = ProjectAnalysisResult(
            project_name="test",
            project_type=ProjectType.LIBRARY,
            framework=FrameworkType.NONE
        )
        
        assert result.project_name == "test"
        assert result.project_type == ProjectType.LIBRARY
        assert result.framework == FrameworkType.NONE
    
    def test_to_dict(self, sample_analysis_result):
        """测试转换为字典"""
        result_dict = sample_analysis_result.to_dict()
        
        assert isinstance(result_dict, dict)
        assert 'project_name' in result_dict
        assert 'project_type' in result_dict
        assert 'framework' in result_dict
        assert result_dict['project_name'] == "test-project"
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            'project_name': 'test-from-dict',
            'project_type': 'library',
            'framework': 'none',
            'version': '1.0.0'
        }
        
        # 假设有from_dict方法
        if hasattr(ProjectAnalysisResult, 'from_dict'):
            result = ProjectAnalysisResult.from_dict(data)
            assert result.project_name == 'test-from-dict'
    
    def test_equality(self):
        """测试相等性比较"""
        result1 = ProjectAnalysisResult(
            project_name="test",
            project_type=ProjectType.LIBRARY,
            framework=FrameworkType.NONE
        )
        
        result2 = ProjectAnalysisResult(
            project_name="test",
            project_type=ProjectType.LIBRARY,
            framework=FrameworkType.NONE
        )
        
        # 如果实现了__eq__方法
        if hasattr(ProjectAnalysisResult, '__eq__'):
            assert result1 == result2
    
    def test_str_representation(self, sample_analysis_result):
        """测试字符串表示"""
        str_repr = str(sample_analysis_result)
        assert isinstance(str_repr, str)
        assert sample_analysis_result.project_name in str_repr