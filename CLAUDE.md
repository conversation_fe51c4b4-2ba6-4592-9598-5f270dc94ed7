# CLAUDE.md

此文件为Claude Code (claude.ai/code) 在此代码库中工作提供指导。

## 常用命令

### 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 配置AI服务（可选）
export GENDOCS_AI_API_KEY="your-api-key"
cp gendocs.example.yaml gendocs.yaml  # 编辑配置文件

# 运行应用（TUI界面）
python main.py

# 企业级文档生成（第二阶段新功能）
python -c "
import asyncio
from src.gendocs.generators import EnterpriseDocumentManager
manager = EnterpriseDocumentManager()
asyncio.run(manager.generate_all_documents('/path/to/project'))
"

# 项目分析（第二阶段新功能）
python -c "
from src.gendocs.analyzers.registry import get_registry
registry = get_registry()
result = registry.analyze_project('/path/to/project')
print(f'项目类型: {result.project_type.value if result else \"未知\"}')
"

# 文档备份功能
python -c "
from src.gendocs.backup import BackupManager
backup_manager = BackupManager()
backup_info = backup_manager.create_backup('/path/to/project', '测试备份')
print(f'备份ID: {backup_info.backup_id}')
"

# 测试AI配置
python -c "from src.gendocs.ai import AIProviderFactory; from src.gendocs.config import ConfigManager; print('AI配置测试成功')"
```

### 代码质量检查
```bash
# 代码风格检查
pylint src/
flake8 src/
mypy src/

# 代码格式化
black src/

# 测试（目前项目中没有测试文件）
pytest  # 如果有测试文件的话
```

### 系统要求检查
```bash
# 检查所有系统依赖是否满足
python -c "from src.gendocs import check_dependencies; check_dependencies()"
```

## 架构概览

GenDocs是一个智能项目文档生成工具，支持多编程语言分析和AI增强的企业级文档生成。架构采用分层模块化设计：

### 核心组件

1. **项目分析系统** (`src/gendocs/analyzers/`) **[第二阶段新增]**
   - **分析器注册表** (`registry.py`)：管理多语言项目分析器
   - **Python分析器** (`python_analyzer.py`)：深度分析Python项目结构、依赖、API等
   - **部署检测器** (`deployment_detector.py`)：检测Docker、云平台、CI/CD配置
   - **基础分析器** (`base.py`)：定义统一的分析接口和数据结构
   - 支持框架检测：Django、Flask、FastAPI、React、Vue等
   - 自动识别项目类型、技术栈、架构模式

2. **企业级文档生成系统** (`src/gendocs/generators/enterprise/`) **[第二阶段新增]**
   - **企业文档管理器** (`enterprise_manager.py`)：统一管理所有企业级生成器
   - **基础文档生成器**：
     - `OverviewGenerator`：项目概览文档（增强版README）
     - `ArchitectureGenerator`：系统架构文档
     - `APIGenerator`：API接口文档
     - `DevelopmentGenerator`：开发指南文档
     - `DeploymentGenerator`：部署文档
   - **AI增强生成器**：
     - `UserStoryGenerator`：用户故事地图
     - `ADRGenerator`：架构决策记录
     - `RunbookGenerator`：运维操作手册
     - `ChangelogGenerator`：变更日志
   - 支持文档备份、并发生成、智能内容增强

3. **文档备份系统** (`src/gendocs/backup/`) **[第二阶段新增]**
   - **备份管理器** (`manager.py`)：提供文档备份、恢复、版本管理
   - **备份数据模型** (`models.py`)：定义备份策略、清单、配置
   - 支持时间戳备份、增量备份、归档压缩
   - 自动清理旧备份，防止存储空间浪费

4. **配置管理系统** (`src/gendocs/config/`) **[第一阶段重构]**
   - **配置管理器** (`manager.py`)：多层级配置加载和管理
   - **配置数据模型** (`models.py`)：完整的配置数据结构
   - 支持环境变量解析：`${VAR_NAME}`语法
   - 配置优先级：项目配置 > 用户配置 > 默认配置

5. **AI集成系统** (`src/gendocs/ai/`) **[第一阶段重构]**
   - **AI提供商工厂** (`factory.py`)：支持多种AI服务商
   - **OpenAI兼容提供商** (`providers/`)：统一的API接口
   - **提示词管理器** (`prompts.py`)：管理各种文档生成提示词
   - 支持OpenAI、DeepSeek、通义千问、Claude等
   - 异步API调用，支持重试和错误处理

6. **模板系统** (`templates/`) **[扩展]**
   - **基础模板** (`base/`)：项目概览、架构、API、开发、部署
   - **AI增强模板** (`ai_enhanced/`)：用户故事、ADR、运维手册
   - 使用Jinja2模板引擎，支持动态内容生成

7. **传统生成器系统** (`src/gendocs/generators/`) **[保持兼容]**
   - 原有生成器：`ApiGenerator`, `DiagramGenerator`, `StructureGenerator`等
   - 统一接口：继承`BaseGenerator`并实现`_generate()`方法
   - 支持AST解析、并发处理和内存优化

8. **用户界面系统** (`src/gendocs/ui/`)
   - 使用Textual库的现代TUI界面 (`cli_ui.py`)
   - 文件浏览器、模块选择和交互控件
   - 通过`main.py`提供主入口

### 关键设计模式

**第一阶段设计模式**：
- **基于AST的分析**：所有Python代码分析使用AST解析而非导入检查
- **工厂模式**：AI提供商工厂支持多种AI服务的动态创建
- **模板方法模式**：生成器基类定义通用流程，子类实现具体逻辑
- **策略模式**：不同的AI提供商实现相同的接口
- **并发处理**：多个生成器使用ThreadPoolExecutor提升性能
- **内存优化**：大文件(>10MB)使用mmap，小文件使用标准读取
- **缓存机制**：对耗时操作(类型提示、文件描述)使用LRU缓存

**第二阶段新增设计模式**：
- **注册表模式**：分析器注册表管理多语言项目分析器
- **企业生成器架构**：基于`EnterpriseGenerator`的层次化文档生成
- **备份策略模式**：支持多种备份策略（时间戳、增量、归档）
- **智能项目检测**：自动识别项目类型、框架、技术栈
- **并发文档生成**：使用信号量控制的异步文档生成
- **AI条件生成**：根据AI可用性智能选择生成策略
- **模块化配置管理**：支持环境变量解析和多层级配置

### 模块结构

**核心系统模块**：
- `src/gendocs/analyzers/` - 项目分析系统 **[第二阶段新增]**
- `src/gendocs/backup/` - 文档备份系统 **[第二阶段新增]**
- `src/gendocs/generators/enterprise/` - 企业级生成器 **[第二阶段新增]**
- `src/gendocs/config/` - 配置管理系统 **[重构优化]**
- `src/gendocs/ai/` - AI集成系统 **[重构优化]**

**传统模块**：
- `src/gendocs/generators/` - 传统生成器系统
  - `base.py` - 定义生成器接口的抽象基类
  - `api.py` - 从文档字符串和签名生成API文档
  - `diagram.py` - 使用pyreverse生成UML类图
  - `structure.py` - 项目结构文档
  - `dependency.py` - 内部/外部依赖分析
  - `quality.py` - 代码质量指标和分析
  - `enterprise_manager.py` - 企业文档管理器 **[第二阶段新增]**

**详细模块说明**：

**分析器系统** (`src/gendocs/analyzers/`)：
- `base.py` - 分析器基类和数据模型
- `python_analyzer.py` - Python项目深度分析
- `deployment_detector.py` - 部署配置检测
- `registry.py` - 分析器注册和管理

**备份系统** (`src/gendocs/backup/`)：
- `models.py` - 备份数据模型和策略
- `manager.py` - 备份管理器和操作接口

**企业生成器** (`src/gendocs/generators/enterprise/`)：
- `base.py` - 企业生成器基类
- `overview_generator.py` - 项目概览文档生成器
- `architecture_generator.py` - 架构文档生成器
- `api_generator.py` - API文档生成器
- `development_generator.py` - 开发指南生成器
- `deployment_generator.py` - 部署文档生成器
- `user_story_generator.py` - 用户故事地图生成器 (AI增强)
- `adr_generator.py` - 架构决策记录生成器 (AI增强)
- `runbook_generator.py` - 运维手册生成器 (AI增强)
- `changelog_generator.py` - 变更日志生成器

**配置系统** (`src/gendocs/config/`)：
- `manager.py` - 多层级配置管理
- `models.py` - 完整配置数据模型

**AI系统** (`src/gendocs/ai/`)：
- `factory.py` - AI提供商工厂
- `base.py` - AI接口基类
- `prompts.py` - 提示词管理
- `providers/openai_provider.py` - OpenAI兼容提供商

**模板系统** (`templates/`)：
- `base/` - 基础文档模板（项目概览、架构、API、开发、部署）
- `ai_enhanced/` - AI增强模板（用户故事、ADR、运维手册）

### 依赖要求

- **核心依赖**：pylint (pyreverse), graphviz, Jinja2, PyYAML, aiohttp
- **AI集成**：支持OpenAI兼容API的任何AI服务商
- **UI依赖**：textual, pytermgui
- **质量工具**：mypy, flake8, radon, black (可选但推荐)
- **系统要求**：Python >= 3.8, Graphviz (系统级安装)

### 入口点

- 控制台脚本：`gendocs=gendocs.__main__:main`
- 直接执行：`python -m gendocs` 或 `python main.py`
- 编程调用：从`src.gendocs.generators`导入生成器

## 重要注意事项

### 第一阶段完成功能 ✅
**已完成的核心架构重构**：
- 灵活的配置管理系统（支持YAML配置和环境变量）
- OpenAI兼容的AI接口架构（支持多种AI服务商）
- 重构的生成器基类（支持AI增强和多语言扩展）
- 完整的文档模板系统（基础模板 + AI增强模板）

### 第二阶段完成功能 ✅
**企业级文档生成系统**：
- ✅ 项目智能分析器（Python项目深度分析）
- ✅ 部署配置检测器（Docker、云平台、CI/CD）
- ✅ 文档备份系统（版本管理、自动清理）
- ✅ 9个企业级文档生成器（基础+AI增强）
- ✅ 并发文档生成和统一管理
- ✅ 主入口集成和系统初始化

**支持的文档类型**：
- 项目概览（PROJECT_OVERVIEW.md）- 增强版README
- 系统架构（ARCHITECTURE.md）- 技术架构文档
- API文档（API_DOCS.md）- 接口说明文档
- 开发指南（DEVELOPMENT_GUIDE.md）- 开发环境和规范
- 部署文档（DEPLOYMENT.md）- 部署配置和指南
- 用户故事地图（USER_STORY_MAP.md）- AI生成
- 架构决策记录（ADR.md）- AI生成
- 运维手册（RUNBOOK.md）- AI生成
- 变更日志（CHANGELOG.md）- 基于现有内容增强

### AI服务配置
- 支持OpenAI、DeepSeek、通义千问、Claude等兼容OpenAI格式的API
- 配置文件：复制`gendocs.example.yaml`为`gendocs.yaml`并配置
- 环境变量：设置`GENDOCS_AI_API_KEY`等
- AI增强生成器需要AI支持，基础生成器可独立运行

### 项目分析支持
- **当前支持**：Python项目（Django、Flask、FastAPI等框架）
- **计划支持**：JavaScript/TypeScript、Java（第三阶段）
- 自动识别项目类型、技术栈、架构模式、API接口
- 支持复杂项目结构和多模块分析

### 使用建议
1. **首次使用**：运行`python main.py`启动TUI界面
2. **企业文档生成**：使用`EnterpriseDocumentManager`生成全套文档
3. **AI功能**：配置AI服务以获得最佳文档质量
4. **备份管理**：系统自动备份现有文档，支持恢复
5. **并发生成**：支持多个文档并发生成，提升效率
- AI增强文档：用户故事地图、ADR决策记录、运维手册

### Graphviz系统依赖
- 类图生成需要系统级安装Graphviz
- 从 https://graphviz.org/download/ 下载安装
- 确保`dot`命令在系统PATH中可用
- 可用`check_dependencies()`函数检查是否正确安装

### 输出目录结构
生成的文档保存在项目的`docs`目录下：
```
docs/
├── project_overview.md    # 项目概览（README风格）
├── architecture.md        # 架构设计文档
├── api_docs.md           # API接口文档
├── dev_guide.md          # 开发指南
├── deployment.md         # 部署文档
├── user_story_map.md     # 用户故事地图（AI增强）
├── adr.md               # 架构决策记录（AI增强）
├── runbook.md           # 运维手册（AI增强）
├── diagrams/            # 类图和依赖图
└── api/                 # 详细API文档
```

### 下一阶段规划
- **第二阶段**：企业级文档生成器实现
- **第三阶段**：JavaScript/TypeScript和Java多语言支持