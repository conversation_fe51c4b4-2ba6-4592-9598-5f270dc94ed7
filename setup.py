from setuptools import setup, find_packages

setup(
    name="gendocs",
    version="1.0.0",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "pylint>=3.3.6",
        "graphviz>=0.20.1",
        "Jinja2>=3.1.6",
    ],
    python_requires=">=3.8",
    # 系统依赖信息
    package_data={
        "": ["README.md"],  # 包含README文件
    },
    # 命令行入口点
    entry_points={
        "console_scripts": [
            "gendocs=gendocs.__main__:main",
        ],
    },
    # 元数据
    author="Your Name",
    author_email="<EMAIL>",
    description="一个自动生成项目文档和类图的工具",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    keywords="documentation, class-diagram, uml",
    url="https://github.com/yourusername/gendocs",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
) 