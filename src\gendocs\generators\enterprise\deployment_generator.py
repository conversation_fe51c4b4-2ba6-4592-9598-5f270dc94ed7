"""
部署文档生成器

生成部署文档，包括部署指南、环境配置、监控等。
"""

from pathlib import Path
from typing import Dict, Any

from .base import EnterpriseGenerator


class DeploymentGenerator(EnterpriseGenerator):
    """部署文档生成器"""
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/deployment.md.j2"
        self.output_filename = "DEPLOYMENT.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成部署文档"""
        try:
            self.logger.info(f"开始生成部署文档: {project_path}")
            
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            deploy_context = self._build_deployment_context(enterprise_context)
            
            base_content = self.render_template(self.template_name, deploy_context)
            
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "deployment_guide_enhancement",
                    **deploy_context
                )
                if enhanced_content:
                    base_content = enhanced_content
            
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            if self.validate_output(final_output_path):
                self.logger.info(f"部署文档生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成部署文档失败: {e}")
            return False
    
    def _build_deployment_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建部署特定上下文"""
        analysis_result = context.get('analysis_result')
        
        deploy_context = context.copy()
        deploy_context.update({
            'deployment_options': self._analyze_deployment_options(analysis_result),
            'environment_config': self._build_environment_config(analysis_result),
            'docker_setup': self._build_docker_setup(analysis_result),
            'monitoring': self._build_monitoring_guide(analysis_result),
            'security': self._build_security_config(analysis_result)
        })
        
        return deploy_context
    
    def _analyze_deployment_options(self, analysis_result) -> list:
        """分析部署选项"""
        options = []
        
        if analysis_result and analysis_result.deployment_config:
            deploy_config = analysis_result.deployment_config
            
            if deploy_config.get('docker', {}).get('detected'):
                options.append({
                    'name': 'Docker部署',
                    'description': '使用Docker容器化部署',
                    'pros': ['环境一致性', '易于扩展', '隔离性好'],
                    'cons': ['学习成本', '资源开销']
                })
            
            if deploy_config.get('traditional', {}).get('detected'):
                options.append({
                    'name': '传统部署',
                    'description': '直接在服务器上部署',
                    'pros': ['简单直接', '性能好'],
                    'cons': ['环境依赖', '扩展困难']
                })
        
        return options
    
    def _build_environment_config(self, analysis_result) -> dict:
        """构建环境配置"""
        return {
            'development': {
                'database': 'SQLite/PostgreSQL',
                'debug': True,
                'logging': 'DEBUG'
            },
            'staging': {
                'database': 'PostgreSQL',
                'debug': False,
                'logging': 'INFO'
            },
            'production': {
                'database': 'PostgreSQL',
                'debug': False,
                'logging': 'WARNING'
            }
        }
    
    def _build_docker_setup(self, analysis_result) -> dict:
        """构建Docker配置"""
        return {
            'dockerfile_example': '''FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]''',
            'docker_compose_example': '''version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=0
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=mydb
      - POSTGRES_PASSWORD=password'''
        }
    
    def _build_monitoring_guide(self, analysis_result) -> dict:
        """构建监控指南"""
        return {
            'metrics': ['CPU使用率', '内存使用率', '响应时间', '错误率'],
            'tools': ['Prometheus', 'Grafana', 'ELK Stack'],
            'alerts': ['服务停机', '响应时间过长', '错误率过高']
        }
    
    def _build_security_config(self, analysis_result) -> dict:
        """构建安全配置"""
        return {
            'ssl': '配置HTTPS证书',
            'firewall': '配置防火墙规则',
            'backup': '定期数据备份',
            'monitoring': '安全监控和告警'
        }