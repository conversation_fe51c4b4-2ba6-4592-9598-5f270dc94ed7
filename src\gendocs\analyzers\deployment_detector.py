"""
部署配置检测器

检测项目的部署配置，包括Docker、传统服务器部署等。
"""

import json
import logging
import re
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)


class DeploymentDetector:
    """部署配置检测器
    
    检测和分析项目的部署配置信息。
    """
    
    def __init__(self):
        """初始化部署检测器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def detect_deployment_config(self, project_path: Path) -> Dict[str, Any]:
        """检测部署配置
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            部署配置信息字典
        """
        deployment_info = {
            "docker": self._detect_docker_config(project_path),
            "traditional": self._detect_traditional_deployment(project_path),
            "cloud": self._detect_cloud_deployment(project_path),
            "ci_cd": self._detect_ci_cd_config(project_path),
            "recommendations": []
        }
        
        # 生成部署建议
        deployment_info["recommendations"] = self._generate_recommendations(deployment_info)
        
        return deployment_info
    
    def _detect_docker_config(self, project_path: Path) -> Dict[str, Any]:
        """检测Docker配置"""
        docker_config = {
            "detected": False,
            "files": {},
            "images": [],
            "services": {},
            "networks": [],
            "volumes": []
        }
        
        # 检测Docker相关文件
        docker_files = {
            "dockerfile": ["Dockerfile", "dockerfile"],
            "docker_compose": ["docker-compose.yml", "docker-compose.yaml"],
            "docker_compose_override": ["docker-compose.override.yml", "docker-compose.override.yaml"],
            "docker_compose_prod": ["docker-compose.prod.yml", "docker-compose.production.yml"],
            "dockerignore": [".dockerignore"]
        }
        
        for file_type, filenames in docker_files.items():
            for filename in filenames:
                file_path = project_path / filename
                if file_path.exists():
                    docker_config["detected"] = True
                    docker_config["files"][file_type] = str(file_path.relative_to(project_path))
                    
                    # 解析文件内容
                    if file_type == "dockerfile":
                        docker_config.update(self._parse_dockerfile(file_path))
                    elif file_type.startswith("docker_compose"):
                        compose_info = self._parse_docker_compose(file_path)
                        docker_config["services"].update(compose_info.get("services", {}))
                        docker_config["networks"].extend(compose_info.get("networks", []))
                        docker_config["volumes"].extend(compose_info.get("volumes", []))
        
        return docker_config
    
    def _parse_dockerfile(self, dockerfile: Path) -> Dict[str, Any]:
        """解析Dockerfile"""
        info = {
            "base_image": None,
            "exposed_ports": [],
            "working_dir": None,
            "entry_point": None,
            "cmd": None,
            "environment": {},
            "copy_instructions": [],
            "run_commands": []
        }
        
        try:
            with open(dockerfile, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 解析FROM指令
                if line.upper().startswith('FROM'):
                    info["base_image"] = line.split()[1] if len(line.split()) > 1 else None
                
                # 解析EXPOSE指令
                elif line.upper().startswith('EXPOSE'):
                    ports = line.split()[1:]
                    info["exposed_ports"].extend(ports)
                
                # 解析WORKDIR指令
                elif line.upper().startswith('WORKDIR'):
                    info["working_dir"] = line.split()[1] if len(line.split()) > 1 else None
                
                # 解析ENTRYPOINT指令
                elif line.upper().startswith('ENTRYPOINT'):
                    info["entry_point"] = ' '.join(line.split()[1:])
                
                # 解析CMD指令
                elif line.upper().startswith('CMD'):
                    info["cmd"] = ' '.join(line.split()[1:])
                
                # 解析ENV指令
                elif line.upper().startswith('ENV'):
                    env_parts = line.split()[1:]
                    if len(env_parts) >= 2:
                        key, value = env_parts[0], ' '.join(env_parts[1:])
                        info["environment"][key] = value
                
                # 解析COPY/ADD指令
                elif line.upper().startswith(('COPY', 'ADD')):
                    info["copy_instructions"].append(line)
                
                # 解析RUN指令
                elif line.upper().startswith('RUN'):
                    info["run_commands"].append(' '.join(line.split()[1:]))
                    
        except Exception as e:
            self.logger.warning(f"解析Dockerfile失败: {e}")
        
        return info
    
    def _parse_docker_compose(self, compose_file: Path) -> Dict[str, Any]:
        """解析docker-compose文件"""
        info = {
            "services": {},
            "networks": [],
            "volumes": []
        }
        
        try:
            with open(compose_file, 'r', encoding='utf-8') as f:
                compose_data = yaml.safe_load(f)
            
            if not isinstance(compose_data, dict):
                return info
            
            # 解析服务
            services = compose_data.get('services', {})
            for service_name, service_config in services.items():
                info["services"][service_name] = {
                    "image": service_config.get('image'),
                    "build": service_config.get('build'),
                    "ports": service_config.get('ports', []),
                    "environment": service_config.get('environment', {}),
                    "volumes": service_config.get('volumes', []),
                    "depends_on": service_config.get('depends_on', []),
                    "networks": service_config.get('networks', [])
                }
            
            # 解析网络
            networks = compose_data.get('networks', {})
            info["networks"] = list(networks.keys())
            
            # 解析卷
            volumes = compose_data.get('volumes', {})
            info["volumes"] = list(volumes.keys())
            
        except Exception as e:
            self.logger.warning(f"解析docker-compose文件失败: {e}")
        
        return info
    
    def _detect_traditional_deployment(self, project_path: Path) -> Dict[str, Any]:
        """检测传统部署配置"""
        traditional_config = {
            "detected": False,
            "web_servers": [],
            "app_servers": [],
            "system_services": [],
            "config_files": [],
            "scripts": []
        }
        
        # 检测Web服务器配置
        web_server_configs = [
            "nginx.conf", "nginx.config",
            "apache.conf", "httpd.conf",
            ".htaccess"
        ]
        
        for config_file in web_server_configs:
            if (project_path / config_file).exists():
                traditional_config["detected"] = True
                if "nginx" in config_file:
                    traditional_config["web_servers"].append("Nginx")
                elif "apache" in config_file or "httpd" in config_file:
                    traditional_config["web_servers"].append("Apache")
                traditional_config["config_files"].append(config_file)
        
        # 检测应用服务器配置
        app_server_configs = [
            "gunicorn.conf.py", "gunicorn.conf",
            "uwsgi.ini", "uwsgi.conf",
            "supervisord.conf",
            "pm2.config.js", "ecosystem.config.js"
        ]
        
        for config_file in app_server_configs:
            if (project_path / config_file).exists():
                traditional_config["detected"] = True
                if "gunicorn" in config_file:
                    traditional_config["app_servers"].append("Gunicorn")
                elif "uwsgi" in config_file:
                    traditional_config["app_servers"].append("uWSGI")
                elif "supervisor" in config_file:
                    traditional_config["app_servers"].append("Supervisor")
                elif "pm2" in config_file:
                    traditional_config["app_servers"].append("PM2")
                traditional_config["config_files"].append(config_file)
        
        # 检测系统服务配置
        service_dirs = ["systemd", "init.d", "upstart"]
        for service_dir in service_dirs:
            service_path = project_path / service_dir
            if service_path.exists() and service_path.is_dir():
                traditional_config["detected"] = True
                traditional_config["system_services"].extend([
                    str(f.relative_to(project_path)) for f in service_path.rglob("*")
                    if f.is_file()
                ])
        
        # 检测部署脚本
        script_patterns = [
            "deploy.sh", "deploy.py",
            "start.sh", "start.py",
            "stop.sh", "stop.py",
            "restart.sh", "restart.py",
            "install.sh", "install.py"
        ]
        
        for script in script_patterns:
            if (project_path / script).exists():
                traditional_config["detected"] = True
                traditional_config["scripts"].append(script)
        
        return traditional_config
    
    def _detect_cloud_deployment(self, project_path: Path) -> Dict[str, Any]:
        """检测云平台部署配置"""
        cloud_config = {
            "detected": False,
            "platforms": [],
            "config_files": {},
            "services": []
        }
        
        # 云平台配置文件映射
        cloud_configs = {
            # AWS
            "aws": [
                "aws-lambda.yml", "lambda.yml",
                "cloudformation.yml", "cloudformation.yaml",
                "serverless.yml", "serverless.yaml",
                ".ebextensions", "Dockerrun.aws.json"
            ],
            # Google Cloud
            "gcp": [
                "app.yaml", "cloudbuild.yaml",
                "deployment.yaml", "service.yaml"
            ],
            # Azure
            "azure": [
                "azure-pipelines.yml",
                "docker-compose.azure.yml"
            ],
            # Kubernetes
            "kubernetes": [
                "deployment.yaml", "deployment.yml",
                "service.yaml", "service.yml",
                "ingress.yaml", "ingress.yml",
                "configmap.yaml", "configmap.yml",
                "k8s", "kubernetes"
            ],
            # Heroku
            "heroku": [
                "Procfile", "app.json",
                "heroku.yml"
            ],
            # Vercel
            "vercel": [
                "vercel.json", ".vercelignore"
            ]
        }
        
        for platform, config_files in cloud_configs.items():
            platform_detected = False
            platform_files = []
            
            for config_file in config_files:
                file_path = project_path / config_file
                if file_path.exists():
                    platform_detected = True
                    platform_files.append(config_file)
                    
                    # 特殊处理目录
                    if file_path.is_dir():
                        platform_files.extend([
                            str(f.relative_to(project_path)) 
                            for f in file_path.rglob("*") if f.is_file()
                        ])
            
            if platform_detected:
                cloud_config["detected"] = True
                cloud_config["platforms"].append(platform)
                cloud_config["config_files"][platform] = platform_files
        
        return cloud_config
    
    def _detect_ci_cd_config(self, project_path: Path) -> Dict[str, Any]:
        """检测CI/CD配置"""
        ci_cd_config = {
            "detected": False,
            "platforms": [],
            "config_files": {},
            "workflows": []
        }
        
        # CI/CD平台配置文件
        ci_cd_configs = {
            "github_actions": [
                ".github/workflows"
            ],
            "gitlab_ci": [
                ".gitlab-ci.yml"
            ],
            "jenkins": [
                "Jenkinsfile", "jenkins.yml"
            ],
            "travis": [
                ".travis.yml"
            ],
            "circle_ci": [
                ".circleci/config.yml"
            ],
            "azure_devops": [
                "azure-pipelines.yml", "azure-pipelines.yaml"
            ]
        }
        
        for platform, config_paths in ci_cd_configs.items():
            platform_detected = False
            platform_files = []
            
            for config_path in config_paths:
                path = project_path / config_path
                if path.exists():
                    platform_detected = True
                    
                    if path.is_file():
                        platform_files.append(config_path)
                    elif path.is_dir():
                        workflow_files = [
                            str(f.relative_to(project_path))
                            for f in path.rglob("*.yml") + path.rglob("*.yaml")
                        ]
                        platform_files.extend(workflow_files)
                        ci_cd_config["workflows"].extend(workflow_files)
            
            if platform_detected:
                ci_cd_config["detected"] = True
                ci_cd_config["platforms"].append(platform)
                ci_cd_config["config_files"][platform] = platform_files
        
        return ci_cd_config
    
    def _generate_recommendations(self, deployment_info: Dict[str, Any]) -> List[str]:
        """生成部署建议"""
        recommendations = []
        
        # Docker建议
        if not deployment_info["docker"]["detected"]:
            recommendations.append("建议添加Docker支持以简化部署和环境一致性")
            recommendations.append("可以创建Dockerfile和docker-compose.yml文件")
        else:
            docker_config = deployment_info["docker"]
            if "dockerfile" in docker_config["files"] and "docker_compose" not in docker_config["files"]:
                recommendations.append("建议添加docker-compose.yml以便于多服务编排")
            
            if "dockerignore" not in docker_config["files"]:
                recommendations.append("建议添加.dockerignore文件以优化Docker构建")
        
        # CI/CD建议
        if not deployment_info["ci_cd"]["detected"]:
            recommendations.append("建议设置CI/CD流水线以自动化部署流程")
            recommendations.append("可以考虑使用GitHub Actions、GitLab CI或Jenkins")
        
        # 云平台建议
        if not deployment_info["cloud"]["detected"]:
            recommendations.append("考虑使用云平台部署以获得更好的扩展性和可靠性")
            recommendations.append("可以考虑AWS、Google Cloud或Azure等云服务")
        
        # 传统部署建议
        if deployment_info["traditional"]["detected"]:
            traditional_config = deployment_info["traditional"]
            if not traditional_config["web_servers"]:
                recommendations.append("建议配置反向代理服务器如Nginx或Apache")
            
            if not traditional_config["system_services"]:
                recommendations.append("建议配置系统服务以确保应用开机自启动")
        
        return recommendations
    
    def get_deployment_summary(self, project_path: Path) -> Dict[str, Any]:
        """获取部署配置摘要
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            部署配置摘要
        """
        deployment_config = self.detect_deployment_config(project_path)
        
        summary = {
            "has_docker": deployment_config["docker"]["detected"],
            "has_traditional": deployment_config["traditional"]["detected"],
            "has_cloud": deployment_config["cloud"]["detected"],
            "has_ci_cd": deployment_config["ci_cd"]["detected"],
            "deployment_types": [],
            "recommendations_count": len(deployment_config["recommendations"])
        }
        
        # 确定部署类型
        if summary["has_docker"]:
            summary["deployment_types"].append("Docker")
        
        if summary["has_traditional"]:
            summary["deployment_types"].append("Traditional")
        
        if summary["has_cloud"]:
            cloud_platforms = deployment_config["cloud"]["platforms"]
            summary["deployment_types"].extend([p.upper() for p in cloud_platforms])
        
        if summary["has_ci_cd"]:
            summary["deployment_types"].append("CI/CD")
        
        if not summary["deployment_types"]:
            summary["deployment_types"] = ["Manual"]
        
        return summary