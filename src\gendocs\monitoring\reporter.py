"""
性能报告生成器模块

生成各种格式的性能分析报告。
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from .metrics import MetricsCollector, SystemMetrics

# 可选依赖
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    from jinja2 import Template
    HAS_JINJA2 = True
except ImportError:
    HAS_JINJA2 = False


@dataclass
class ReportConfig:
    """报告配置"""
    output_dir: Path = Path(".gendocs/reports")
    include_charts: bool = True
    chart_width: int = 12
    chart_height: int = 8
    time_range_hours: int = 24
    
    # 报告内容配置
    include_system_metrics: bool = True
    include_function_profiles: bool = True
    include_ai_metrics: bool = True
    include_error_analysis: bool = True


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, config: ReportConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 确保输出目录存在
        self.config.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_comprehensive_report(self, 
                                    metrics_collector: MetricsCollector,
                                    profile_manager=None) -> Path:
        """生成综合性能报告
        
        Args:
            metrics_collector: 指标收集器
            profile_manager: 性能分析管理器
            
        Returns:
            报告文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_dir = self.config.output_dir / f"report_{timestamp}"
            report_dir.mkdir(exist_ok=True)
            
            self.logger.info("开始生成综合性能报告")
            
            # 收集数据
            report_data = self._collect_report_data(metrics_collector, profile_manager)
            
            # 生成JSON报告
            json_file = self._generate_json_report(report_data, report_dir)
            
            # 生成HTML报告
            html_file = self._generate_html_report(report_data, report_dir)
            
            # 生成图表
            if self.config.include_charts:
                self._generate_charts(report_data, report_dir)
            
            self.logger.info(f"综合性能报告已生成: {html_file}")
            return html_file
            
        except Exception as e:
            self.logger.error(f"生成综合性能报告失败: {e}")
            raise
    
    def _collect_report_data(self, metrics_collector: MetricsCollector, profile_manager=None) -> Dict[str, Any]:
        """收集报告数据"""
        # 基础指标
        performance_metrics = metrics_collector.get_metrics_summary()
        
        # 系统指标历史
        system_metrics = []
        if self.config.include_system_metrics:
            system_metrics = metrics_collector.get_system_metrics_history(
                duration_minutes=self.config.time_range_hours * 60
            )
        
        # 函数性能统计
        function_profiles = {}
        if self.config.include_function_profiles and profile_manager:
            function_profiles = profile_manager.get_function_profiles()
        
        # 最近指标点
        recent_metrics = metrics_collector.get_recent_metrics(
            duration_minutes=self.config.time_range_hours * 60
        )
        
        # 直方图统计
        histogram_stats = {}
        for metric_name in ['document_generation_duration', 'ai_request_duration']:
            stats = metrics_collector.get_histogram_stats(metric_name)
            if stats:
                histogram_stats[metric_name] = stats
        
        return {
            'timestamp': datetime.now(),
            'time_range_hours': self.config.time_range_hours,
            'performance_metrics': performance_metrics.to_dict(),
            'system_metrics': system_metrics,
            'function_profiles': function_profiles,
            'recent_metrics': recent_metrics,
            'histogram_stats': histogram_stats
        }
    
    def _generate_json_report(self, report_data: Dict[str, Any], report_dir: Path) -> Path:
        """生成JSON格式报告"""
        json_file = report_dir / "performance_report.json"
        
        # 序列化数据
        serializable_data = self._make_serializable(report_data)
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2, default=str)
        
        return json_file
    
    def _generate_html_report(self, report_data: Dict[str, Any], report_dir: Path) -> Path:
        """生成HTML格式报告"""
        html_file = report_dir / "performance_report.html"
        
        if not HAS_JINJA2:
            # 简单的HTML生成，不使用Jinja2
            html_content = self._generate_simple_html_report(report_data)
        else:
            html_content = self._generate_template_html_report(report_data)
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_file
    
    def _generate_simple_html_report(self, report_data: Dict[str, Any]) -> str:
        """生成简单的HTML报告（不使用模板）"""
        performance_metrics = report_data['performance_metrics']
        timestamp = report_data['timestamp']
        
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GenDocs 性能分析报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
        .header {{ background: #667eea; color: white; padding: 20px; border-radius: 8px; }}
        .metric {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>GenDocs 性能分析报告</h1>
        <p>生成时间: {timestamp}</p>
    </div>
    <div class="metric">
        <h3>文档生成</h3>
        <p>总数: {performance_metrics['document_generation']['total_generated']}</p>
        <p>成功: {performance_metrics['document_generation']['successful']}</p>
    </div>
    <div class="metric">
        <h3>AI集成</h3>
        <p>请求总数: {performance_metrics['ai_integration']['total_requests']}</p>
        <p>成功率: {performance_metrics['ai_integration']['success_rate']:.1f}%</p>
    </div>
</body>
</html>"""
        
        return html_content
    
    def _generate_template_html_report(self, report_data: Dict[str, Any]) -> str:
        """使用Jinja2模板生成HTML报告"""
        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GenDocs 性能分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 0;
            opacity: 0.9;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .metric-title {
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .metric-unit {
            font-size: 0.8em;
            color: #888;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .table tr:hover {
            background-color: #f5f5f5;
        }
        .chart-container {
            margin: 30px 0;
            text-align: center;
        }
        .chart-container img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #e0e0e0;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>GenDocs 性能分析报告</h1>
            <p>生成时间: {{ timestamp.strftime('%Y年%m月%d日 %H:%M:%S') }}</p>
            <p>时间范围: 过去 {{ time_range_hours }} 小时</p>
        </div>

        <!-- 总体指标 -->
        <div class="section">
            <h2>📊 总体性能指标</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">文档生成总数</div>
                    <div class="metric-value">{{ performance_metrics.document_generation.total_generated }}</div>
                    <div class="metric-unit">个</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">成功率</div>
                    <div class="metric-value">{{ "%.1f"|format((performance_metrics.document_generation.successful / (performance_metrics.document_generation.total_generated or 1) * 100)) }}</div>
                    <div class="metric-unit">%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">平均生成时间</div>
                    <div class="metric-value">{{ "%.2f"|format(performance_metrics.document_generation.average_time_per_doc) }}</div>
                    <div class="metric-unit">秒</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">AI请求总数</div>
                    <div class="metric-value">{{ performance_metrics.ai_integration.total_requests }}</div>
                    <div class="metric-unit">次</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">AI成功率</div>
                    <div class="metric-value">{{ "%.1f"|format(performance_metrics.ai_integration.success_rate) }}</div>
                    <div class="metric-unit">%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">峰值内存</div>
                    <div class="metric-value">{{ "%.1f"|format(performance_metrics.system_resources.peak_memory_mb) }}</div>
                    <div class="metric-unit">MB</div>
                </div>
            </div>
        </div>

        <!-- AI集成指标 -->
        {% if performance_metrics.ai_integration.total_requests > 0 %}
        <div class="section">
            <h2>🤖 AI集成性能</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">平均响应时间</div>
                    <div class="metric-value">{{ "%.3f"|format(performance_metrics.ai_integration.average_response_time) }}</div>
                    <div class="metric-unit">秒</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Token消耗</div>
                    <div class="metric-value">{{ performance_metrics.ai_integration.tokens_consumed }}</div>
                    <div class="metric-unit">个</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">成功请求</div>
                    <div class="metric-value">{{ performance_metrics.ai_integration.successful_requests }}</div>
                    <div class="metric-unit">次</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">失败请求</div>
                    <div class="metric-value">{{ performance_metrics.ai_integration.failed_requests }}</div>
                    <div class="metric-unit">次</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 函数性能统计 -->
        {% if function_profiles %}
        <div class="section">
            <h2>⚡ 函数性能统计</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>函数名</th>
                        <th>调用次数</th>
                        <th>总耗时(秒)</th>
                        <th>平均耗时(秒)</th>
                        <th>最大耗时(秒)</th>
                        <th>内存峰值(MB)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for func_name, stats in function_profiles.items() %}
                    <tr>
                        <td>{{ func_name }}</td>
                        <td>{{ stats.call_count }}</td>
                        <td>{{ "%.3f"|format(stats.total_time) }}</td>
                        <td>{{ "%.3f"|format(stats.average_time) }}</td>
                        <td>{{ "%.3f"|format(stats.max_time) }}</td>
                        <td>{{ "%.2f"|format(stats.memory_peak_mb) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- 错误统计 -->
        {% if performance_metrics.errors.total_errors > 0 %}
        <div class="section">
            <h2>❌ 错误统计</h2>
            <div class="metric-card">
                <div class="metric-title">总错误数</div>
                <div class="metric-value error">{{ performance_metrics.errors.total_errors }}</div>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>错误类型</th>
                        <th>发生次数</th>
                        <th>占比</th>
                    </tr>
                </thead>
                <tbody>
                    {% for error_type, count in performance_metrics.errors.error_breakdown.items() %}
                    <tr>
                        <td>{{ error_type }}</td>
                        <td>{{ count }}</td>
                        <td>{{ "%.1f"|format(count / performance_metrics.errors.total_errors * 100) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- 图表 -->
        {% if include_charts %}
        <div class="section">
            <h2>📈 性能趋势图表</h2>
            <div class="chart-container">
                <img src="system_metrics.png" alt="系统资源使用趋势" />
            </div>
            <div class="chart-container">
                <img src="performance_timeline.png" alt="性能时间线" />
            </div>
        </div>
        {% endif %}

        <div class="footer">
            <p>本报告由 GenDocs 性能监控系统自动生成</p>
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        html_content = template.render(
            **report_data,
            include_charts=self.config.include_charts
        )
        
        return html_content
    
    def _generate_charts(self, report_data: Dict[str, Any], report_dir: Path) -> None:
        """生成性能图表"""
        if not HAS_MATPLOTLIB:
            self.logger.warning("matplotlib未安装，跳过图表生成")
            return
            
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 生成系统资源使用图表
            self._generate_system_metrics_chart(report_data, report_dir)
            
            # 生成性能时间线图表
            self._generate_performance_timeline_chart(report_data, report_dir)
            
        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")
    
    def _generate_system_metrics_chart(self, report_data: Dict[str, Any], report_dir: Path) -> None:
        """生成系统资源使用图表"""
        system_metrics = report_data.get('system_metrics', [])
        if not system_metrics:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(self.config.chart_width, self.config.chart_height))
        fig.suptitle('系统资源使用趋势', fontsize=16, fontweight='bold')
        
        timestamps = [m.timestamp for m in system_metrics]
        
        # CPU使用率
        cpu_values = [m.cpu_percent for m in system_metrics]
        ax1.plot(timestamps, cpu_values, 'b-', linewidth=2)
        ax1.set_title('CPU使用率 (%)')
        ax1.set_ylabel('百分比')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # 内存使用率
        memory_values = [m.memory_percent for m in system_metrics]
        ax2.plot(timestamps, memory_values, 'r-', linewidth=2)
        ax2.set_title('内存使用率 (%)')
        ax2.set_ylabel('百分比')
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # 内存使用量(MB)
        memory_mb_values = [m.memory_used_mb for m in system_metrics]
        ax3.plot(timestamps, memory_mb_values, 'g-', linewidth=2)
        ax3.set_title('内存使用量 (MB)')
        ax3.set_ylabel('MB')
        ax3.grid(True, alpha=0.3)
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # 磁盘使用率
        disk_values = [m.disk_usage_percent for m in system_metrics]
        ax4.plot(timestamps, disk_values, 'm-', linewidth=2)
        ax4.set_title('磁盘使用率 (%)')
        ax4.set_ylabel('百分比')
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        plt.tight_layout()
        plt.savefig(report_dir / 'system_metrics.png', dpi=150, bbox_inches='tight')
        plt.close()
    
    def _generate_performance_timeline_chart(self, report_data: Dict[str, Any], report_dir: Path) -> None:
        """生成性能时间线图表"""
        recent_metrics = report_data.get('recent_metrics', [])
        if not recent_metrics:
            return
        
        # 按类型分组指标
        doc_generation_metrics = [m for m in recent_metrics if 'document_generation' in m.name]
        ai_request_metrics = [m for m in recent_metrics if 'ai_request' in m.name]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.config.chart_width, self.config.chart_height))
        fig.suptitle('性能时间线', fontsize=16, fontweight='bold')
        
        # 文档生成时间线
        if doc_generation_metrics:
            doc_timestamps = [m.timestamp for m in doc_generation_metrics]
            doc_values = [m.value for m in doc_generation_metrics]
            ax1.scatter(doc_timestamps, doc_values, alpha=0.6, s=30)
            ax1.set_title('文档生成耗时分布')
            ax1.set_ylabel('耗时(秒)')
            ax1.grid(True, alpha=0.3)
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # AI请求时间线
        if ai_request_metrics:
            ai_timestamps = [m.timestamp for m in ai_request_metrics]
            ai_values = [m.value for m in ai_request_metrics]
            ax2.scatter(ai_timestamps, ai_values, alpha=0.6, s=30, color='orange')
            ax2.set_title('AI请求响应时间分布')
            ax2.set_ylabel('响应时间(秒)')
            ax2.grid(True, alpha=0.3)
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        plt.tight_layout()
        plt.savefig(report_dir / 'performance_timeline.png', dpi=150, bbox_inches='tight')
        plt.close()
    
    def _make_serializable(self, data: Any) -> Any:
        """使数据可序列化"""
        if isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif isinstance(data, datetime):
            return data.isoformat()
        elif hasattr(data, 'to_dict'):
            return self._make_serializable(data.to_dict())
        elif hasattr(data, '__dict__'):
            return self._make_serializable(data.__dict__)
        else:
            return data
    
    def generate_simple_summary(self, metrics_collector: MetricsCollector) -> str:
        """生成简单的性能摘要
        
        Args:
            metrics_collector: 指标收集器
            
        Returns:
            性能摘要文本
        """
        metrics = metrics_collector.get_metrics_summary()
        
        summary_lines = [
            "GenDocs 性能摘要",
            "=" * 50,
            f"文档生成: {metrics.successful_generations}/{metrics.total_documents_generated} 成功",
            f"平均生成时间: {metrics.generation_time_seconds / max(metrics.total_documents_generated, 1):.2f} 秒",
            f"AI请求: {metrics.ai_requests_successful}/{metrics.ai_requests_total} 成功",
            f"AI平均响应时间: {metrics.ai_response_time_seconds / max(metrics.ai_requests_total, 1):.2f} 秒",
            f"Token消耗: {metrics.ai_tokens_consumed}",
            f"峰值内存: {metrics.peak_memory_usage_mb:.1f} MB",
            f"总错误数: {metrics.total_errors}",
            "=" * 50
        ]
        
        return "\n".join(summary_lines)