# 核心依赖
pylint>=3.3.6  # 用于pyreverse生成类图
graphviz>=0.20.1  # 用于生成图形
Jinja2>=3.1.6  # 用于模板渲染
PyYAML>=6.0.1  # YAML配置文件解析
aiohttp>=3.11.0  # 异步HTTP客户端 (用于AI API调用)

# 开发依赖（可选）
pytest>=8.3.5  # 测试框架
black>=25.1.0  # 代码格式化
flake8>=7.2.0  # 代码检查
mypy>=1.15.0  # 类型检查
coverage>=7.8.0  # 测试覆盖率

aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
annotated-types==0.7.0
APScheduler==3.11.0
astor==0.8.1
astroid==3.3.9
attrs==25.3.0
autopep8==2.3.2
babel==2.17.0
bandit==1.8.3
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
cryptography==44.0.2
customtkinter==5.2.2
darkdetect==0.8.0
dill==0.3.9
docopt==0.6.2
flake8==7.2.0
frozenlist==1.5.0
gprof2dot==2024.6.6
idna==3.10
iniconfig==2.1.0
isort==6.0.1
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
Mako==1.3.10
mando==0.7.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mccabe==0.7.0
mdurl==0.1.2
more-itertools==10.6.0
MouseInfo==0.1.3
msal==1.32.0
multidict==6.4.3
mypy==1.15.0
mypy-extensions==1.0.0
packaging==24.2
# Editable install with no version control (password_manager==1.0.0)
-e c:\password_manager
pathspec==0.12.1
pbr==6.1.1
pdoc==15.0.2
pdoc3==0.11.6
pillow==11.1.0
pipreqs==0.4.13
platformdirs==4.3.7
pluggy==1.5.0
polib==1.2.0
propcache==0.3.1
PyAutoGUI==0.9.54
pycodestyle==2.13.0
pycparser==2.22
pydantic==2.11.2
pydantic_core==2.33.1
pyflakes==3.3.2
PyGetWindow==0.0.9
Pygments==2.19.1
PyJWT==2.10.1
PyMsgBox==1.0.9
pynput==1.8.1
pyotp==2.9.0
pyperclip==1.9.0
PyRect==0.2.0
PyScreeze==1.0.1
pytest-asyncio==0.26.0
pytest-cov==6.1.1
python-dateutil==2.9.0.post0
python-gettext==5.0
pytweening==1.2.0
pywin32==310
pywin32-ctypes==0.2.3
PyYAML==6.0.2
qrcode==8.1
radon==6.0.1
regex==2024.11.6
requests==2.32.3
rich==14.0.0
screeninfo==0.8.1
setuptools==78.1.0
six==1.17.0
stevedore==5.4.1
tenacity==9.1.2
tiktoken==0.9.0
tomlkit==0.13.2
typing-inspection==0.4.0
typing_extensions==4.13.1
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.3.0
yarg==0.1.10
yarl==1.19.0
textual>=0.1.18
pytermgui>=7.1.0
