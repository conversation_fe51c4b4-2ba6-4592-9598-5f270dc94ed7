"""
文档生成工具主入口
"""

import asyncio
import logging
from pathlib import Path

from src.gendocs.ui.cli_ui import run_ui
from src.gendocs.config.manager import ConfigManager
from src.gendocs.analyzers.registry import auto_register_analyzers
from src.gendocs.ai.factory import AIProviderFactory


async def initialize_system():
    """初始化系统组件"""
    try:
        # 自动注册分析器
        auto_register_analyzers()
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        config_manager.load_config()
        
        # 初始化AI提供商工厂
        ai_factory = AIProviderFactory()
        
        logging.info("系统组件初始化完成")
        return True
        
    except Exception as e:
        logging.error(f"系统初始化失败: {e}")
        return False


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("启动GenDocs文档生成工具")
    
    # 初始化系统
    if not asyncio.run(initialize_system()):
        logger.error("系统初始化失败，退出程序")
        return
    
    # 启动UI
    try:
        run_ui()
    except KeyboardInterrupt:
        logger.info("用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
    finally:
        logger.info("GenDocs程序结束")


if __name__ == "__main__":
    main() 