"""
gendocs - 智能项目文档生成工具

支持多语言项目分析和AI增强的企业级文档生成。
"""

import logging
import sys
from pathlib import Path
from typing import List, Optional

__version__ = "0.2.0"

# 导出核心组件
from .config import ConfigManager
from .analyzers.registry import get_registry, auto_register_analyzers
from .generators import EnterpriseDocumentManager
from .ai import AIProviderFactory
from .backup import BackupManager

def check_dependencies() -> bool:
    """检查系统依赖是否满足
    
    Returns:
        是否满足所有依赖要求
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(message)s"
    )
    
    all_satisfied = True
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        logging.error("⚠️ 需要Python 3.8或更高版本")
        all_satisfied = False
        
    # 检查必需的Python包
    required_packages = {
        "pylint": "2.17.0",
        "pytest": "7.0.0",
        "sphinx": "4.0.0",
        "mkdocs": "1.4.0"
    }
    
    for package, min_version in required_packages.items():
        version = get_package_version(package)
        if not version:
            logging.error(f"⚠️ 缺少必需的包: {package}>={min_version}")
            all_satisfied = False
        elif version < min_version:
            logging.error(f"⚠️ {package} 版本过低: {version} < {min_version}")
            all_satisfied = False
            
    # 检查Graphviz
    if not is_tool_available("dot"):
        logging.error("⚠️ 缺少Graphviz，请从 https://graphviz.org/download/ 下载安装")
        all_satisfied = False
        
    return all_satisfied 