"""
批量处理系统单元测试
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from gendocs.batch.processor import BatchProcessor, BatchJobConfig, BatchJobStatus
from gendocs.batch.queue import JobQueue, Job, JobStatus, JobPriority
from gendocs.batch.monitor import BatchMonitor, JobMetrics
from gendocs.batch.scheduler import JobScheduler, ScheduleConfig


class TestJob:
    """作业类测试"""
    
    def test_init(self):
        """测试作业初始化"""
        job = Job(
            job_id="test_job_1",
            project_path="/test/project",
            output_path="/test/output",
            config={"test": "config"}
        )
        
        assert job.job_id == "test_job_1"
        assert job.project_path == Path("/test/project")
        assert job.output_path == Path("/test/output")
        assert job.status == JobStatus.PENDING
        assert job.priority == JobPriority.MEDIUM
        assert job.config == {"test": "config"}
    
    def test_init_with_all_params(self):
        """测试带所有参数的作业初始化"""
        job = Job(
            job_id="test_job_2",
            project_path="/test/project",
            output_path="/test/output",
            config={"test": "config"},
            priority=JobPriority.HIGH,
            dependencies=["dep1", "dep2"]
        )
        
        assert job.priority == JobPriority.HIGH
        assert job.dependencies == ["dep1", "dep2"]
    
    def test_to_dict(self):
        """测试作业转换为字典"""
        job = Job(
            job_id="test_job",
            project_path="/test/project",
            output_path="/test/output",
            config={"test": "config"}
        )
        
        job_dict = job.to_dict()
        
        assert isinstance(job_dict, dict)
        assert job_dict['job_id'] == "test_job"
        assert job_dict['status'] == JobStatus.PENDING.value
        assert job_dict['priority'] == JobPriority.MEDIUM.value
    
    def test_from_dict(self):
        """测试从字典创建作业"""
        job_data = {
            'job_id': 'test_job',
            'project_path': '/test/project',
            'output_path': '/test/output',
            'config': {'test': 'config'},
            'status': 'running',
            'priority': 'high'
        }
        
        job = Job.from_dict(job_data)
        
        assert job.job_id == 'test_job'
        assert job.status == JobStatus.RUNNING
        assert job.priority == JobPriority.HIGH
    
    def test_update_status(self):
        """测试更新作业状态"""
        job = Job("test_job", "/test/project", "/test/output", {})
        
        job.update_status(JobStatus.RUNNING)
        assert job.status == JobStatus.RUNNING
        assert job.started_at is not None
        
        job.update_status(JobStatus.COMPLETED)
        assert job.status == JobStatus.COMPLETED
        assert job.completed_at is not None
    
    def test_duration_calculation(self):
        """测试作业持续时间计算"""
        job = Job("test_job", "/test/project", "/test/output", {})
        
        # 未开始的作业
        assert job.duration is None
        
        # 运行中的作业
        job.update_status(JobStatus.RUNNING)
        duration = job.duration
        assert duration is not None
        assert duration >= 0
        
        # 已完成的作业
        job.update_status(JobStatus.COMPLETED)
        duration = job.duration
        assert duration is not None
        assert duration >= 0


class TestJobQueue:
    """作业队列测试"""
    
    def test_init(self):
        """测试作业队列初始化"""
        queue = JobQueue()
        assert queue.max_size == 1000
        assert queue.is_empty() is True
        assert queue.size() == 0
    
    def test_add_job(self):
        """测试添加作业"""
        queue = JobQueue()
        job = Job("test_job", "/test/project", "/test/output", {})
        
        queue.add_job(job)
        
        assert queue.size() == 1
        assert queue.is_empty() is False
        assert queue.contains_job("test_job") is True
    
    def test_get_next_job(self):
        """测试获取下一个作业"""
        queue = JobQueue()
        
        # 添加不同优先级的作业
        job_low = Job("low_job", "/test", "/out", {}, priority=JobPriority.LOW)
        job_high = Job("high_job", "/test", "/out", {}, priority=JobPriority.HIGH)
        job_medium = Job("medium_job", "/test", "/out", {}, priority=JobPriority.MEDIUM)
        
        queue.add_job(job_low)
        queue.add_job(job_high)
        queue.add_job(job_medium)
        
        # 应该按优先级顺序返回
        next_job = queue.get_next_job()
        assert next_job.job_id == "high_job"
        
        next_job = queue.get_next_job()
        assert next_job.job_id == "medium_job"
        
        next_job = queue.get_next_job()
        assert next_job.job_id == "low_job"
    
    def test_get_job_by_id(self):
        """测试根据ID获取作业"""
        queue = JobQueue()
        job = Job("test_job", "/test/project", "/test/output", {})
        
        queue.add_job(job)
        
        retrieved_job = queue.get_job_by_id("test_job")
        assert retrieved_job == job
        
        # 测试不存在的作业
        assert queue.get_job_by_id("nonexistent") is None
    
    def test_remove_job(self):
        """测试移除作业"""
        queue = JobQueue()
        job = Job("test_job", "/test/project", "/test/output", {})
        
        queue.add_job(job)
        assert queue.size() == 1
        
        removed_job = queue.remove_job("test_job")
        assert removed_job == job
        assert queue.size() == 0
        assert queue.contains_job("test_job") is False
    
    def test_get_jobs_by_status(self):
        """测试根据状态获取作业"""
        queue = JobQueue()
        
        job1 = Job("job1", "/test", "/out", {})
        job2 = Job("job2", "/test", "/out", {})
        job3 = Job("job3", "/test", "/out", {})
        
        job1.update_status(JobStatus.RUNNING)
        job2.update_status(JobStatus.PENDING)
        job3.update_status(JobStatus.RUNNING)
        
        queue.add_job(job1)
        queue.add_job(job2)
        queue.add_job(job3)
        
        running_jobs = queue.get_jobs_by_status(JobStatus.RUNNING)
        assert len(running_jobs) == 2
        
        pending_jobs = queue.get_jobs_by_status(JobStatus.PENDING)
        assert len(pending_jobs) == 1
    
    def test_clear_completed_jobs(self):
        """测试清理已完成作业"""
        queue = JobQueue()
        
        job1 = Job("job1", "/test", "/out", {})
        job2 = Job("job2", "/test", "/out", {})
        job3 = Job("job3", "/test", "/out", {})
        
        job1.update_status(JobStatus.COMPLETED)
        job2.update_status(JobStatus.RUNNING)
        job3.update_status(JobStatus.FAILED)
        
        queue.add_job(job1)
        queue.add_job(job2)
        queue.add_job(job3)
        
        cleared_count = queue.clear_completed_jobs()
        assert cleared_count == 1
        assert queue.size() == 2
        assert not queue.contains_job("job1")
    
    def test_max_size_limit(self):
        """测试队列大小限制"""
        queue = JobQueue(max_size=2)
        
        job1 = Job("job1", "/test", "/out", {})
        job2 = Job("job2", "/test", "/out", {})
        job3 = Job("job3", "/test", "/out", {})
        
        queue.add_job(job1)
        queue.add_job(job2)  # 应该成功
        
        # 第三个作业应该失败或替换最旧的作业
        with pytest.raises(Exception) or queue.add_job(job3):
            pass
    
    def test_thread_safety(self):
        """测试线程安全"""
        import threading
        
        queue = JobQueue()
        results = []
        
        def add_jobs():
            for i in range(10):
                job = Job(f"job_{i}", "/test", "/out", {})
                queue.add_job(job)
                results.append(f"added_{i}")
        
        # 创建多个线程同时添加作业
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=add_jobs)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有作业都被添加
        assert queue.size() == 30


class TestBatchProcessor:
    """批量处理器测试"""
    
    def test_init(self):
        """测试批量处理器初始化"""
        config = BatchJobConfig(max_workers=2, timeout=60)
        processor = BatchProcessor(config)
        
        assert processor.config == config
        assert processor.max_workers == 2
        assert hasattr(processor, 'job_queue')
        assert hasattr(processor, 'monitor')
    
    @pytest.mark.asyncio
    async def test_add_job(self, mock_project_dir, temp_dir):
        """测试添加作业"""
        config = BatchJobConfig()
        processor = BatchProcessor(config)
        
        job_id = await processor.add_job(
            project_path=mock_project_dir,
            output_path=temp_dir / "output",
            config={"test": "config"}
        )
        
        assert isinstance(job_id, str)
        assert processor.job_queue.contains_job(job_id)
    
    @pytest.mark.asyncio
    async def test_process_jobs(self, mock_project_dir, temp_dir):
        """测试处理作业"""
        config = BatchJobConfig(max_workers=1)
        processor = BatchProcessor(config)
        
        # Mock文档管理器
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            # 添加作业
            job_id = await processor.add_job(
                project_path=mock_project_dir,
                output_path=temp_dir / "output",
                config={"generators": ["overview"]}
            )
            
            # 开始处理
            await processor.start_processing()
            
            # 等待作业完成
            await asyncio.sleep(0.1)
            
            # 停止处理
            await processor.stop_processing()
            
            # 验证作业状态
            job = processor.job_queue.get_job_by_id(job_id)
            assert job.status in [JobStatus.COMPLETED, JobStatus.RUNNING]
    
    def test_get_job_status(self, mock_project_dir, temp_dir):
        """测试获取作业状态"""
        config = BatchJobConfig()
        processor = BatchProcessor(config)
        
        # 添加作业
        job_id = asyncio.run(processor.add_job(
            project_path=mock_project_dir,
            output_path=temp_dir / "output",
            config={}
        ))
        
        status = processor.get_job_status(job_id)
        assert status == JobStatus.PENDING
    
    def test_cancel_job(self, mock_project_dir, temp_dir):
        """测试取消作业"""
        config = BatchJobConfig()
        processor = BatchProcessor(config)
        
        # 添加作业
        job_id = asyncio.run(processor.add_job(
            project_path=mock_project_dir,
            output_path=temp_dir / "output",
            config={}
        ))
        
        # 取消作业
        result = processor.cancel_job(job_id)
        assert result is True
        
        # 验证作业状态
        job = processor.job_queue.get_job_by_id(job_id)
        assert job.status == JobStatus.CANCELLED
    
    def test_get_queue_status(self):
        """测试获取队列状态"""
        config = BatchJobConfig()
        processor = BatchProcessor(config)
        
        status = processor.get_queue_status()
        
        assert isinstance(status, dict)
        assert 'total_jobs' in status
        assert 'pending_jobs' in status
        assert 'running_jobs' in status
        assert 'completed_jobs' in status
        assert 'failed_jobs' in status


class TestBatchMonitor:
    """批量监控器测试"""
    
    def test_init(self):
        """测试监控器初始化"""
        monitor = BatchMonitor()
        assert monitor is not None
        assert hasattr(monitor, 'metrics')
    
    def test_record_job_start(self):
        """测试记录作业开始"""
        monitor = BatchMonitor()
        job = Job("test_job", "/test", "/out", {})
        
        monitor.record_job_start(job)
        
        metrics = monitor.get_current_metrics()
        assert metrics.jobs_started == 1
        assert metrics.jobs_running == 1
    
    def test_record_job_completion(self):
        """测试记录作业完成"""
        monitor = BatchMonitor()
        job = Job("test_job", "/test", "/out", {})
        
        # 先记录开始，再记录完成
        monitor.record_job_start(job)
        monitor.record_job_completion(job, True)
        
        metrics = monitor.get_current_metrics()
        assert metrics.jobs_completed == 1
        assert metrics.jobs_successful == 1
        assert metrics.jobs_running == 0
    
    def test_record_job_failure(self):
        """测试记录作业失败"""
        monitor = BatchMonitor()
        job = Job("test_job", "/test", "/out", {})
        
        monitor.record_job_start(job)
        monitor.record_job_completion(job, False)
        
        metrics = monitor.get_current_metrics()
        assert metrics.jobs_completed == 1
        assert metrics.jobs_failed == 1
        assert metrics.jobs_successful == 0
    
    def test_get_processing_statistics(self):
        """测试获取处理统计"""
        monitor = BatchMonitor()
        
        # 模拟多个作业的处理
        for i in range(5):
            job = Job(f"job_{i}", "/test", "/out", {})
            monitor.record_job_start(job)
            
            # 3个成功，2个失败
            success = i < 3
            monitor.record_job_completion(job, success)
        
        stats = monitor.get_processing_statistics()
        
        assert stats['total_jobs'] == 5
        assert stats['successful_jobs'] == 3
        assert stats['failed_jobs'] == 2
        assert stats['success_rate'] == 0.6
    
    def test_get_performance_metrics(self):
        """测试获取性能指标"""
        monitor = BatchMonitor()
        
        # 添加一些作业处理时间
        import time
        for i in range(3):
            job = Job(f"job_{i}", "/test", "/out", {})
            monitor.record_job_start(job)
            time.sleep(0.01)  # 模拟处理时间
            monitor.record_job_completion(job, True)
        
        metrics = monitor.get_performance_metrics()
        
        assert 'average_processing_time' in metrics
        assert 'total_processing_time' in metrics
        assert metrics['total_processing_time'] > 0
    
    def test_reset_metrics(self):
        """测试重置指标"""
        monitor = BatchMonitor()
        
        # 添加一些数据
        job = Job("test_job", "/test", "/out", {})
        monitor.record_job_start(job)
        monitor.record_job_completion(job, True)
        
        # 重置前验证有数据
        metrics = monitor.get_current_metrics()
        assert metrics.jobs_completed > 0
        
        # 重置指标
        monitor.reset_metrics()
        
        # 重置后验证数据清零
        metrics = monitor.get_current_metrics()
        assert metrics.jobs_completed == 0


class TestJobScheduler:
    """作业调度器测试"""
    
    def test_init(self):
        """测试调度器初始化"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        assert scheduler.config == config
        assert hasattr(scheduler, 'scheduled_jobs')
    
    def test_schedule_job(self):
        """测试调度作业"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        schedule_id = scheduler.schedule_job(
            project_path="/test/project",
            output_path="/test/output",
            cron_expression="0 0 * * *",  # 每天午夜
            config={"test": "config"}
        )
        
        assert isinstance(schedule_id, str)
        assert len(scheduler.scheduled_jobs) == 1
    
    def test_get_scheduled_jobs(self):
        """测试获取已调度作业"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        # 添加几个调度作业
        scheduler.schedule_job("/test1", "/out1", "0 0 * * *", {})
        scheduler.schedule_job("/test2", "/out2", "0 12 * * *", {})
        
        scheduled_jobs = scheduler.get_scheduled_jobs()
        assert len(scheduled_jobs) == 2
    
    def test_cancel_scheduled_job(self):
        """测试取消调度作业"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        schedule_id = scheduler.schedule_job("/test", "/out", "0 0 * * *", {})
        
        result = scheduler.cancel_scheduled_job(schedule_id)
        assert result is True
        assert len(scheduler.scheduled_jobs) == 0
    
    def test_get_next_run_time(self):
        """测试获取下次运行时间"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        schedule_id = scheduler.schedule_job("/test", "/out", "0 0 * * *", {})
        
        next_run = scheduler.get_next_run_time(schedule_id)
        assert next_run is not None
    
    @pytest.mark.asyncio
    async def test_check_and_trigger_jobs(self):
        """测试检查并触发作业"""
        config = ScheduleConfig()
        scheduler = JobScheduler(config)
        
        # Mock批量处理器
        mock_processor = Mock()
        mock_processor.add_job = AsyncMock(return_value="job_id")
        
        # 设置处理器
        scheduler.set_processor(mock_processor)
        
        # 添加一个应该立即触发的作业（每分钟运行）
        scheduler.schedule_job("/test", "/out", "* * * * *", {})
        
        # 检查并触发
        triggered_jobs = await scheduler.check_and_trigger_jobs()
        
        # 根据时间可能触发或不触发
        assert isinstance(triggered_jobs, list)


class TestBatchIntegration:
    """批量处理集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_batch_processing(self, complex_project_dir, temp_dir):
        """测试端到端批量处理"""
        config = BatchJobConfig(max_workers=2, timeout=30)
        processor = BatchProcessor(config)
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            # 添加多个作业
            job_ids = []
            for i in range(3):
                job_id = await processor.add_job(
                    project_path=complex_project_dir,
                    output_path=temp_dir / f"output_{i}",
                    config={"generators": ["overview"]}
                )
                job_ids.append(job_id)
            
            # 开始处理
            await processor.start_processing()
            
            # 等待一段时间让作业处理
            await asyncio.sleep(0.5)
            
            # 停止处理
            await processor.stop_processing()
            
            # 验证所有作业都被处理
            for job_id in job_ids:
                job = processor.job_queue.get_job_by_id(job_id)
                assert job.status in [JobStatus.COMPLETED, JobStatus.RUNNING, JobStatus.FAILED]
    
    @pytest.mark.asyncio
    async def test_batch_with_monitoring(self, mock_project_dir, temp_dir):
        """测试带监控的批量处理"""
        config = BatchJobConfig(max_workers=1)
        processor = BatchProcessor(config)
        monitor = processor.monitor
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            # 添加作业
            job_id = await processor.add_job(
                project_path=mock_project_dir,
                output_path=temp_dir / "output",
                config={}
            )
            
            # 处理作业
            await processor.start_processing()
            await asyncio.sleep(0.1)
            await processor.stop_processing()
            
            # 验证监控数据
            metrics = monitor.get_current_metrics()
            assert metrics.jobs_started >= 1
            
            stats = monitor.get_processing_statistics()
            assert stats['total_jobs'] >= 1
    
    def test_batch_error_handling(self, mock_project_dir, temp_dir):
        """测试批量处理错误处理"""
        config = BatchJobConfig(max_workers=1)
        processor = BatchProcessor(config)
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            # Mock生成器抛出异常
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(side_effect=Exception("Test error"))
            mock_manager.return_value = mock_instance
            
            # 添加作业
            job_id = asyncio.run(processor.add_job(
                project_path=mock_project_dir,
                output_path=temp_dir / "output",
                config={}
            ))
            
            # 处理作业（应该优雅地处理错误）
            async def run_test():
                await processor.start_processing()
                await asyncio.sleep(0.1)
                await processor.stop_processing()
                
                # 验证作业状态
                job = processor.job_queue.get_job_by_id(job_id)
                assert job.status == JobStatus.FAILED
            
            asyncio.run(run_test())
    
    @pytest.mark.asyncio
    async def test_concurrent_job_processing(self, mock_project_dir, temp_dir):
        """测试并发作业处理"""
        config = BatchJobConfig(max_workers=3)
        processor = BatchProcessor(config)
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            # 添加多个作业
            job_ids = []
            for i in range(5):
                job_id = await processor.add_job(
                    project_path=mock_project_dir,
                    output_path=temp_dir / f"output_{i}",
                    config={}
                )
                job_ids.append(job_id)
            
            # 开始并发处理
            await processor.start_processing()
            await asyncio.sleep(0.5)
            await processor.stop_processing()
            
            # 验证作业都被处理
            completed_count = 0
            for job_id in job_ids:
                job = processor.job_queue.get_job_by_id(job_id)
                if job.status == JobStatus.COMPLETED:
                    completed_count += 1
            
            # 至少应该有一些作业完成
            assert completed_count > 0