# 变更日志

本项目的所有重要变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本控制遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 待发布的新功能

### 变更
- 现有功能的变更

### 废弃
- 即将移除的功能

### 移除
- 已移除的功能

### 修复
- 问题修复

### 安全
- 安全相关的修复

{% if existing_content %}
{{ existing_content }}
{% else %}
## [{{ current_version if current_version else "1.0.0" }}] - {{ release_date if release_date else "2024-01-01" }}

### 新增
- 项目初始版本
- 核心功能实现

### 变更
- 无

### 修复
- 无

### 安全
- 实施基础安全措施
{% endif %}

{% if releases and releases|length > 0 %}
{% for release in releases %}
## [{{ release.version }}] - {{ release.date }}

{% if release.added and release.added|length > 0 %}
### 新增
{% for item in release.added %}
- {{ item }}
{% endfor %}
{% endif %}

{% if release.changed and release.changed|length > 0 %}
### 变更
{% for item in release.changed %}
- {{ item }}
{% endfor %}
{% endif %}

{% if release.deprecated and release.deprecated|length > 0 %}
### 废弃
{% for item in release.deprecated %}
- {{ item }}
{% endfor %}
{% endif %}

{% if release.removed and release.removed|length > 0 %}
### 移除
{% for item in release.removed %}
- {{ item }}
{% endfor %}
{% endif %}

{% if release.fixed and release.fixed|length > 0 %}
### 修复
{% for item in release.fixed %}
- {{ item }}
{% endfor %}
{% endif %}

{% if release.security and release.security|length > 0 %}
### 安全
{% for item in release.security %}
- {{ item }}
{% endfor %}
{% endif %}

{% endfor %}
{% endif %}

## 变更类型说明

{% if categories and categories|length > 0 %}
{% for category in categories %}
- **{{ category.name }}**: {{ category.description }}
{% endfor %}
{% else %}
- **新增**: 新功能
- **变更**: 现有功能的变更
- **废弃**: 即将移除的功能
- **移除**: 已移除的功能
- **修复**: 问题修复
- **安全**: 安全相关的修复
{% endif %}

## 版本规范

本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**: 当你做了不兼容的 API 修改
- **次版本号**: 当你做了向下兼容的功能性新增
- **修订号**: 当你做了向下兼容的问题修正

## 如何贡献变更日志

1. 每次发布前更新 `[未发布]` 部分
2. 创建新版本时，将 `[未发布]` 重命名为版本号和日期
3. 在文件顶部添加新的 `[未发布]` 部分
4. 确保按时间倒序排列版本

---

*此文档由 GenDocs 自动生成于 {{ generation_date }}*