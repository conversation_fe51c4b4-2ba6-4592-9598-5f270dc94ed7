"""
AI提供商工厂

负责创建和管理AI提供商实例。
"""

import importlib
import logging
from typing import Dict, Type, Optional

from ..config.models import AIConfig, AIProvider as AIProviderEnum
from .base import AIProvider, AIGenerationError

logger = logging.getLogger(__name__)


class AIProviderFactory:
    """AI提供商工厂类
    
    根据配置创建对应的AI提供商实例。
    """
    
    # 提供商映射表
    _provider_mapping: Dict[AIProviderEnum, str] = {
        AIProviderEnum.OPENAI: "openai_provider.OpenAIProvider",
        AIProviderEnum.AZURE: "azure_provider.AzureOpenAIProvider",
        AIProviderEnum.DEEPSEEK: "deepseek_provider.DeepSeekProvider", 
        AIProviderEnum.QWEN: "qwen_provider.QwenProvider",
        AIProviderEnum.CLAUDE: "claude_provider.ClaudeProvider",
        AIProviderEnum.CUSTOM: "custom_provider.CustomProvider"
    }
    
    # 缓存已创建的提供商类
    _provider_classes: Dict[str, Type[AIProvider]] = {}
    
    @classmethod
    def create_provider(cls, ai_config: AIConfig) -> AIProvider:
        """创建AI提供商实例
        
        Args:
            ai_config: AI配置对象
            
        Returns:
            AI提供商实例
            
        Raises:
            AIGenerationError: 创建失败时抛出
        """
        try:
            provider_class = cls._get_provider_class(ai_config.provider)
            instance = provider_class(ai_config)
            
            # 验证配置
            config_issues = instance.validate_config()
            if config_issues:
                issues_str = "; ".join(config_issues)
                raise AIGenerationError(f"AI配置验证失败: {issues_str}")
            
            logger.info(f"已创建AI提供商: {ai_config.provider} ({ai_config.model})")
            return instance
            
        except Exception as e:
            logger.error(f"创建AI提供商失败: {e}")
            raise AIGenerationError(f"创建AI提供商失败: {e}") from e
    
    @classmethod
    def _get_provider_class(cls, provider_type: AIProviderEnum) -> Type[AIProvider]:
        """获取提供商类
        
        Args:
            provider_type: 提供商类型
            
        Returns:
            提供商类
            
        Raises:
            AIGenerationError: 提供商不支持时抛出
        """
        if provider_type not in cls._provider_mapping:
            raise AIGenerationError(f"不支持的AI提供商: {provider_type}")
        
        module_class = cls._provider_mapping[provider_type]
        
        # 检查缓存
        if module_class in cls._provider_classes:
            return cls._provider_classes[module_class]
        
        # 动态导入提供商类
        try:
            module_name, class_name = module_class.split(".")
            module_path = f"gendocs.ai.providers.{module_name}"
            
            module = importlib.import_module(module_path)
            provider_class = getattr(module, class_name)
            
            # 验证类是否继承自AIProvider
            if not issubclass(provider_class, AIProvider):
                raise AIGenerationError(f"提供商类必须继承自AIProvider: {class_name}")
            
            # 缓存类
            cls._provider_classes[module_class] = provider_class
            
            return provider_class
            
        except ImportError as e:
            raise AIGenerationError(f"无法导入AI提供商模块: {module_path}") from e
        except AttributeError as e:
            raise AIGenerationError(f"提供商类不存在: {class_name}") from e
    
    @classmethod
    def get_available_providers(cls) -> list[str]:
        """获取可用的AI提供商列表
        
        Returns:
            可用提供商名称列表
        """
        available = []
        
        for provider_type in cls._provider_mapping:
            try:
                cls._get_provider_class(provider_type)
                available.append(provider_type.value)
            except AIGenerationError:
                # 提供商模块不可用，跳过
                pass
        
        return available
    
    @classmethod
    async def test_provider(cls, ai_config: AIConfig) -> tuple[bool, Optional[str]]:
        """测试AI提供商连接
        
        Args:
            ai_config: AI配置对象
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            provider = cls.create_provider(ai_config)
            success = await provider.test_connection()
            
            if success:
                return True, None
            else:
                return False, "连接测试失败"
                
        except Exception as e:
            return False, str(e)
    
    @classmethod
    def register_custom_provider(
        cls, 
        name: str, 
        provider_class: Type[AIProvider]
    ) -> None:
        """注册自定义AI提供商
        
        Args:
            name: 提供商名称
            provider_class: 提供商类
            
        Raises:
            AIGenerationError: 注册失败时抛出
        """
        if not issubclass(provider_class, AIProvider):
            raise AIGenerationError("自定义提供商必须继承自AIProvider")
        
        # 添加到映射表
        custom_key = f"custom_{name}"
        cls._provider_classes[custom_key] = provider_class
        
        logger.info(f"已注册自定义AI提供商: {name}")
    
    @classmethod
    def clear_cache(cls) -> None:
        """清除提供商类缓存"""
        cls._provider_classes.clear()
        logger.debug("已清除AI提供商类缓存")