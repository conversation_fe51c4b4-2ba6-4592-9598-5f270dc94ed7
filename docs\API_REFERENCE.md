# GenDocs API参考文档

## 概述

本文档提供了GenDocs所有公共API的详细说明，包括核心类、方法、配置选项和使用示例。

## 配置管理 API

### ConfigManager

项目配置管理的核心类。

```python
from gendocs.config import ConfigManager

# 创建配置管理器实例
config_manager = ConfigManager()

# 加载配置文件
config_manager.load_config("config.yaml")

# 获取配置值
provider = config_manager.get_config_value("ai.provider")
temperature = config_manager.get_config_value("ai.temperature", default=0.7)

# 设置配置值
config_manager.set_config_value("ai.model", "gpt-4")

# 更新配置
config_manager.update_config({
    "ai": {"temperature": 0.8},
    "generation": {"output_dir": "custom_docs"}
})

# 保存配置
config_manager.save_config("updated_config.yaml")

# 验证配置
is_valid = config_manager.validate_config()
```

#### 方法参考

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `load_config(config_path)` | `config_path: Path` | `bool` | 加载配置文件 |
| `get_config_value(key, default=None)` | `key: str, default: Any` | `Any` | 获取配置值 |
| `set_config_value(key, value)` | `key: str, value: Any` | `None` | 设置配置值 |
| `update_config(config_dict)` | `config_dict: dict` | `None` | 批量更新配置 |
| `save_config(config_path)` | `config_path: Path` | `None` | 保存配置到文件 |
| `validate_config()` | - | `bool` | 验证配置有效性 |
| `reset_to_defaults()` | - | `None` | 重置为默认配置 |
| `to_dict()` | - | `dict` | 获取配置字典 |

## 项目分析 API

### AnalyzerRegistry

项目分析器的注册和管理中心。

```python
from gendocs.analyzers import get_registry

# 获取全局分析器注册表
registry = get_registry()

# 分析项目
analysis_result = registry.analyze_project("/path/to/project")

# 获取适合的分析器
analyzer = registry.get_suitable_analyzer("/path/to/project")

# 列出所有注册的分析器
analyzers = registry.list_analyzers()
```

### ProjectAnalysisResult

项目分析结果数据模型。

```python
# 分析结果属性
print(f"项目名称: {analysis_result.project_name}")
print(f"项目类型: {analysis_result.project_type}")
print(f"框架类型: {analysis_result.framework}")
print(f"版本: {analysis_result.version}")
print(f"作者: {analysis_result.author}")
print(f"描述: {analysis_result.description}")
print(f"依赖数量: {len(analysis_result.dependencies)}")

# 转换为字典
result_dict = analysis_result.to_dict()
```

#### 属性参考

| 属性 | 类型 | 说明 |
|------|------|------|
| `project_name` | `str` | 项目名称 |
| `project_type` | `ProjectType` | 项目类型（库/应用/框架等） |
| `framework` | `FrameworkType` | 使用的框架 |
| `version` | `str` | 项目版本 |
| `author` | `str` | 项目作者 |
| `description` | `str` | 项目描述 |
| `dependencies` | `List[Dependency]` | 依赖列表 |
| `modules` | `List[Module]` | 模块列表 |
| `apis` | `List[API]` | API接口列表 |
| `tech_stack` | `TechStack` | 技术栈信息 |
| `code_quality` | `CodeQualityMetrics` | 代码质量指标 |

## 文档生成 API

### EnterpriseDocumentManager

企业级文档生成管理器。

```python
from gendocs.generators import EnterpriseDocumentManager
from gendocs.config import ConfigManager

# 创建文档管理器
config_manager = ConfigManager()
doc_manager = EnterpriseDocumentManager(config_manager)

# 生成所有文档
results = await doc_manager.generate_all_documents(
    project_path="/path/to/project",
    output_path="/path/to/docs"
)

# 生成指定类型的文档
results = await doc_manager.generate_all_documents(
    project_path="/path/to/project",
    output_path="/path/to/docs",
    selected_generators=["overview", "api", "architecture"]
)

# 获取可用的生成器
generators = doc_manager.get_available_generators()
```

#### 可用生成器

| 生成器名称 | 生成内容 | 输出文件 |
|------------|----------|----------|
| `overview` | 项目概览 | `README.md` |
| `api` | API文档 | `api/modules.md` |
| `architecture` | 架构文档 | `architecture/overview.md` |
| `deployment` | 部署指南 | `deployment/guide.md` |
| `development` | 开发指南 | `development/setup.md` |
| `changelog` | 变更日志 | `CHANGELOG.md` |
| `runbook` | 运行手册 | `operations/runbook.md` |
| `adr` | 架构决策记录 | `architecture/decisions/adr-*.md` |
| `user_story` | 用户故事映射 | `requirements/user_stories.md` |

### 自定义生成器

创建自定义文档生成器：

```python
from gendocs.generators.enterprise.base import EnterpriseGenerator

class CustomGenerator(EnterpriseGenerator):
    """自定义文档生成器"""
    
    template_name = "custom_template.md.j2"
    
    async def generate(self, project_path: Path, 
                      output_path: Path, context: dict) -> bool:
        """生成自定义文档"""
        try:
            # 获取项目分析结果
            analysis_result = self.get_project_analysis(project_path)
            
            # 构建上下文
            template_context = self.build_enterprise_context(
                project_path, analysis_result
            )
            template_context.update(context)
            
            # 渲染模板
            content = self.render_template(
                self.get_template_path(self.template_name),
                template_context
            )
            
            # AI增强（可选）
            if self.ai_provider:
                content = await self.enhance_with_ai_insights(
                    content, "custom_enhancement", template_context
                )
            
            # 写入文件
            output_path.parent.mkdir(parents=True, exist_ok=True)
            output_path.write_text(content, encoding='utf-8')
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成自定义文档失败: {e}")
            return False
```

## AI集成 API

### AI提供商工厂

```python
from gendocs.ai import get_ai_provider

# 获取AI提供商实例
ai_provider = get_ai_provider("openai", {
    "api_key": "your-api-key",
    "model": "gpt-3.5-turbo",
    "temperature": 0.7
})

# 生成内容
content = await ai_provider.generate_content(
    prompt="生成项目概览文档",
    max_tokens=2000,
    temperature=0.8
)
```

### 自定义AI提供商

```python
from gendocs.ai.base import AIProviderBase

class CustomAIProvider(AIProviderBase):
    """自定义AI提供商"""
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.api_endpoint = config.get("api_endpoint")
        self.api_key = config.get("api_key")
    
    async def generate_content(self, prompt: str, **kwargs) -> str:
        """生成内容的实现"""
        # 实现自定义AI调用逻辑
        headers = {"Authorization": f"Bearer {self.api_key}"}
        payload = {
            "prompt": prompt,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        # 发送请求并处理响应
        # ...
        
        return generated_content
    
    def is_available(self) -> bool:
        """检查提供商是否可用"""
        return bool(self.api_key and self.api_endpoint)
```

## 批量处理 API

### BatchProcessor

批量处理多个项目的文档生成。

```python
from gendocs.batch import BatchProcessor, BatchJobConfig

# 创建批量处理器
config = BatchJobConfig(
    max_workers=4,
    timeout=300,
    queue_size=100
)
processor = BatchProcessor(config)

# 添加作业
job_id = await processor.add_job(
    project_path="/path/to/project",
    output_path="/path/to/docs",
    config={"generators": ["overview", "api"]}
)

# 开始处理
await processor.start_processing()

# 获取作业状态
status = processor.get_job_status(job_id)

# 取消作业
processor.cancel_job(job_id)

# 获取队列状态
queue_status = processor.get_queue_status()

# 停止处理
await processor.stop_processing()
```

### Job和JobQueue

```python
from gendocs.batch import Job, JobQueue, JobStatus, JobPriority

# 创建作业
job = Job(
    job_id="unique_job_id",
    project_path="/path/to/project",
    output_path="/path/to/output",
    config={"generators": ["overview"]},
    priority=JobPriority.HIGH
)

# 创建队列
queue = JobQueue(max_size=1000)

# 添加作业到队列
queue.add_job(job)

# 获取下一个作业
next_job = queue.get_next_job()

# 根据状态获取作业
running_jobs = queue.get_jobs_by_status(JobStatus.RUNNING)
```

## 性能监控 API

### PerformanceMonitor

系统性能监控和指标收集。

```python
from gendocs.monitoring import get_performance_monitor

# 获取性能监控器
monitor = get_performance_monitor()

# 启动监控
monitor.start_monitoring()

# 获取当前状态
status = monitor.get_current_status()

# 设置性能阈值
monitor.set_performance_threshold('cpu_percent', 80.0)

# 注册警报回调
def alert_callback(alert_info):
    print(f"性能警报: {alert_info}")

monitor.register_alert_callback('high_cpu', alert_callback)

# 生成性能报告
report_file = monitor.generate_report_now()

# 停止监控
monitor.stop_monitoring()
```

### MetricsCollector

手动收集性能指标。

```python
from gendocs.monitoring import MetricsCollector

collector = MetricsCollector()

# 记录文档生成
collector.record_document_generation(
    success=True,
    duration=2.5,
    document_type="overview"
)

# 记录AI请求
collector.record_ai_request(
    success=True,
    response_time=1.5,
    tokens_used=1000,
    provider="openai"
)

# 记录错误
collector.record_error("config_error", "Invalid configuration")

# 获取指标摘要
metrics = collector.get_metrics_summary()

# 导出指标
collector.export_metrics("/path/to/metrics.json")
```

## 错误处理 API

### ErrorHandler

统一的错误处理机制。

```python
from gendocs.utils import ErrorHandler, GenDocsError, ErrorSeverity

# 创建错误处理器
error_handler = ErrorHandler()

# 处理错误
error = GenDocsError(
    "配置文件无效",
    error_code="CONFIG_ERROR",
    severity=ErrorSeverity.HIGH
)

# 带恢复策略的错误处理
def recovery_strategy():
    return "使用默认配置"

result = error_handler.handle_error(error, recovery_strategy)

# 使用上下文管理器
with error_handler.error_context("document_generation", "/path/to/project"):
    # 在此上下文中的错误会自动添加上下文信息
    pass

# 获取错误统计
stats = error_handler.get_error_statistics()
```

### 自定义错误类型

```python
from gendocs.utils import GenDocsError, ErrorSeverity

class CustomError(GenDocsError):
    """自定义错误类型"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            error_code="CUSTOM_ERROR",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
```

## 日志记录 API

### LoggerManager

高级日志管理功能。

```python
from gendocs.utils import LoggerManager, LogConfig, LogLevel

# 创建日志配置
log_config = LogConfig(
    level=LogLevel.DEBUG,
    enable_file_logging=True,
    log_file_path="gendocs.log",
    enable_json_logging=True,
    enable_colored_output=True
)

# 创建日志管理器
logger_manager = LoggerManager(log_config)

# 获取日志器
logger = logger_manager.get_logger("my_module")

# 记录日志
logger.info("处理开始", extra={"project": "example"})
logger.warning("配置项缺失", extra={"key": "ai.api_key"})
logger.error("生成失败", extra={"error": "API限制"})
```

## 配置验证 API

### 配置验证器

```python
from gendocs.utils.validators import validate_config, ValidationResult

# 验证配置
config = {
    "ai": {
        "provider": "openai",
        "temperature": 0.7
    },
    "generation": {
        "output_dir": "docs"
    }
}

result = validate_config(config)

if result.is_valid:
    print("配置有效")
else:
    print("配置错误:")
    for error in result.errors:
        print(f"  - {error}")
    
    print("警告:")
    for warning in result.warnings:
        print(f"  - {warning}")
```

## CLI集成 API

### 程序化CLI调用

```python
from gendocs.cli.main import cli
from click.testing import CliRunner

# 创建CLI运行器
runner = CliRunner()

# 调用CLI命令
result = runner.invoke(cli, [
    'generate',
    '/path/to/project',
    '--output', '/path/to/docs',
    '--generators', 'overview,api'
])

print(f"退出码: {result.exit_code}")
print(f"输出: {result.output}")
```

## 使用示例

### 完整的文档生成流程

```python
import asyncio
from pathlib import Path
from gendocs.config import ConfigManager
from gendocs.generators import EnterpriseDocumentManager
from gendocs.monitoring import get_performance_monitor

async def generate_docs_example():
    """完整的文档生成示例"""
    
    # 1. 配置管理
    config_manager = ConfigManager()
    config_manager.load_config("gendocs.yaml")
    
    # 2. 启动监控
    monitor = get_performance_monitor()
    monitor.start_monitoring()
    
    try:
        # 3. 创建文档管理器
        doc_manager = EnterpriseDocumentManager(config_manager)
        
        # 4. 生成文档
        results = await doc_manager.generate_all_documents(
            project_path=Path("/path/to/project"),
            output_path=Path("/path/to/docs"),
            selected_generators=["overview", "api", "architecture"]
        )
        
        # 5. 检查结果
        successful_generators = [gen for gen, success in results.items() if success]
        print(f"成功生成: {successful_generators}")
        
        # 6. 生成监控报告
        report_file = monitor.generate_report_now()
        print(f"监控报告: {report_file}")
        
    finally:
        # 7. 停止监控
        monitor.stop_monitoring()

# 运行示例
asyncio.run(generate_docs_example())
```

### 批量处理示例

```python
import asyncio
from gendocs.batch import BatchProcessor, BatchJobConfig

async def batch_processing_example():
    """批量处理示例"""
    
    # 创建配置
    config = BatchJobConfig(max_workers=4, timeout=300)
    processor = BatchProcessor(config)
    
    # 项目列表
    projects = [
        ("/path/to/project1", "/path/to/docs1"),
        ("/path/to/project2", "/path/to/docs2"),
        ("/path/to/project3", "/path/to/docs3")
    ]
    
    try:
        # 启动处理器
        await processor.start_processing()
        
        # 添加作业
        job_ids = []
        for project_path, output_path in projects:
            job_id = await processor.add_job(
                project_path=project_path,
                output_path=output_path,
                config={"generators": ["overview", "api"]}
            )
            job_ids.append(job_id)
            print(f"添加作业: {job_id}")
        
        # 等待处理完成
        while True:
            status = processor.get_queue_status()
            if status['pending_jobs'] == 0 and status['running_jobs'] == 0:
                break
            await asyncio.sleep(1)
        
        # 检查结果
        final_status = processor.get_queue_status()
        print(f"处理完成: {final_status}")
        
    finally:
        # 停止处理器
        await processor.stop_processing()

# 运行示例
asyncio.run(batch_processing_example())
```

---

本API参考文档提供了GenDocs所有主要功能的详细使用说明，帮助开发者快速集成和使用GenDocs的各项功能。