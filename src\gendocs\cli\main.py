"""
CLI主入口模块

提供命令行解析和主要命令分发功能。
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Optional

from ..config import ConfigManager
from ..analyzers.registry import auto_register_analyzers
from ..utils import setup_logging, LoggerConfig, LogLevel, get_error_handler
from .commands import GenerateCommand, AnalyzeCommand, BackupCommand, ConfigCommand, BatchCommand, MonitorCommand


def setup_cli_logging(verbose: bool = False, quiet: bool = False) -> None:
    """设置CLI日志配置
    
    Args:
        verbose: 是否启用详细日志
        quiet: 是否启用静默模式
    """
    # 确定日志级别
    if quiet:
        level = LogLevel.WARNING
    elif verbose:
        level = LogLevel.DEBUG
    else:
        level = LogLevel.INFO
    
    # 创建日志配置
    log_config = LoggerConfig(
        level=level,
        enable_file_logging=True,
        enable_console_logging=True,
        console_level=level,
        log_file_path=Path(".gendocs/logs/cli.log"),
        extra_fields={'component': 'cli'}
    )
    
    # 设置日志系统
    setup_logging(log_config)


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器
    
    Returns:
        配置好的参数解析器
    """
    parser = argparse.ArgumentParser(
        prog='gendocs',
        description='GenDocs - 智能项目文档生成工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  gendocs generate /path/to/project              # 生成全部企业级文档
  gendocs generate . --generators overview,api   # 只生成概览和API文档
  gendocs analyze /path/to/project               # 分析项目结构
  gendocs backup create /path/to/project         # 创建文档备份
  gendocs config init                            # 初始化配置文件
  gendocs config template --auto-detect          # 自动生成配置模板
  gendocs batch --discover --max-concurrent 5    # 批量处理发现的项目
  gendocs monitor start --enable-profiling       # 启动性能监控

更多信息请访问: https://github.com/your-org/gendocs
        """
    )
    
    # 全局选项
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细输出'
    )
    
    parser.add_argument(
        '-q', '--quiet',
        action='store_true',
        help='静默模式，只显示警告和错误'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='GenDocs 0.2.0'
    )
    
    parser.add_argument(
        '--config',
        type=Path,
        help='指定配置文件路径'
    )
    
    # 子命令
    subparsers = parser.add_subparsers(
        dest='command',
        help='可用命令',
        metavar='COMMAND'
    )
    
    # generate 命令
    generate_parser = subparsers.add_parser(
        'generate',
        help='生成项目文档',
        description='生成企业级项目文档'
    )
    GenerateCommand.setup_parser(generate_parser)
    
    # analyze 命令
    analyze_parser = subparsers.add_parser(
        'analyze',
        help='分析项目结构',
        description='分析项目结构和技术栈'
    )
    AnalyzeCommand.setup_parser(analyze_parser)
    
    # backup 命令
    backup_parser = subparsers.add_parser(
        'backup',
        help='文档备份管理',
        description='管理项目文档备份'
    )
    BackupCommand.setup_parser(backup_parser)
    
    # config 命令
    config_parser = subparsers.add_parser(
        'config',
        help='配置管理',
        description='管理GenDocs配置'
    )
    ConfigCommand.setup_parser(config_parser)
    
    # batch 命令
    batch_parser = subparsers.add_parser(
        'batch',
        help='批量处理项目',
        description='批量处理多个项目的文档生成'
    )
    BatchCommand.setup_parser(batch_parser)
    
    # monitor 命令
    monitor_parser = subparsers.add_parser(
        'monitor',
        help='性能监控',
        description='性能监控和统计分析'
    )
    MonitorCommand.setup_parser(monitor_parser)
    
    return parser


async def async_main(args: argparse.Namespace) -> int:
    """异步主函数
    
    Args:
        args: 解析后的命令行参数
        
    Returns:
        退出码
    """
    error_handler = get_error_handler()
    logger = logging.getLogger("gendocs.cli")
    
    try:
        # 初始化系统组件
        logger.info("初始化GenDocs系统组件")
        auto_register_analyzers()
        
        # 初始化配置管理器
        logger.debug("初始化配置管理器")
        config_manager = ConfigManager()
        if args.config:
            config_manager.load_config(args.config)
        else:
            config_manager.load_config()
        
        # 添加操作上下文
        from ..utils.error_handler import ErrorContext
        from datetime import datetime
        
        operation_context = ErrorContext(
            operation=f"cli_{args.command}",
            component="cli",
            parameters=vars(args),
            timestamp=datetime.now()
        )
        
        # 根据命令执行相应操作
        logger.info(f"执行命令: {args.command}")
        
        if args.command == 'generate':
            command = GenerateCommand(config_manager)
            return await command.execute(args)
        
        elif args.command == 'analyze':
            command = AnalyzeCommand(config_manager)
            return await command.execute(args)
        
        elif args.command == 'backup':
            command = BackupCommand(config_manager)
            return await command.execute(args)
        
        elif args.command == 'config':
            command = ConfigCommand(config_manager)
            return await command.execute(args)
        
        elif args.command == 'batch':
            command = BatchCommand(config_manager)
            return await command.execute(args)
        
        elif args.command == 'monitor':
            command = MonitorCommand(config_manager)
            return await command.execute(args)
        
        else:
            error_msg = "请指定一个命令。使用 'gendocs --help' 查看帮助。"
            logger.error(error_msg)
            print(f"错误: {error_msg}")
            return 1
    
    except KeyboardInterrupt:
        logger.info("用户取消操作")
        print("\n操作已取消")
        return 130
    
    except Exception as e:
        # 使用错误处理器处理异常
        from ..utils.error_handler import ErrorContext
        from datetime import datetime
        
        error_context = ErrorContext(
            operation="cli_execution",
            component="main",
            parameters=vars(args) if 'args' in locals() else {},
            timestamp=datetime.now()
        )
        
        error_handler.handle_error(e, error_context, attempt_recovery=False)
        
        if args.verbose:
            import traceback
            traceback.print_exc()
        
        return 1


def main(argv: Optional[List[str]] = None) -> int:
    """CLI主入口函数
    
    Args:
        argv: 命令行参数列表，默认使用sys.argv
        
    Returns:
        退出码
    """
    try:
        parser = create_parser()
        args = parser.parse_args(argv)
        
        # 设置日志
        setup_cli_logging(args.verbose, args.quiet)
        
        # 检查是否指定了命令
        if not args.command:
            parser.print_help()
            return 1
        
        # 运行异步主函数
        return asyncio.run(async_main(args))
        
    except Exception as e:
        # 最顶层的错误处理
        print(f"GenDocs启动失败: {e}")
        if '--verbose' in (argv or sys.argv):
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())