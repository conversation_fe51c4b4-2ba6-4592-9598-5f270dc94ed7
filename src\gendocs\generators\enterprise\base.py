"""
企业级文档生成器基类

定义企业级文档生成器的通用功能和接口。
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, List

from ..base import BaseGenerator
from ...analyzers.base import ProjectAnalysisResult
from ...backup import BackupManager, BackupConfig
from ...monitoring import get_metrics_collector, monitor_performance


class EnterpriseGenerator(BaseGenerator):
    """企业级文档生成器基类
    
    为企业级文档生成提供通用功能和接口。
    """
    
    def __init__(self, config_manager=None, ai_provider=None):
        """初始化企业级生成器
        
        Args:
            config_manager: 配置管理器
            ai_provider: AI提供商
        """
        super().__init__(config_manager, ai_provider)
        self.backup_manager = self._init_backup_manager()
    
    def _init_backup_manager(self) -> Optional[BackupManager]:
        """初始化备份管理器"""
        try:
            backup_config = BackupConfig()
            if self.config_manager:
                # 从配置中获取备份设置
                config_data = self.config_manager.get_config()
                backup_settings = config_data.get('backup', {})
                
                if backup_settings:
                    backup_config.enabled = backup_settings.get('enabled', True)
                    backup_config.max_backups = backup_settings.get('max_backups', 10)
                    backup_config.compress = backup_settings.get('compress', False)
                    
                    backup_dir = backup_settings.get('backup_dir')
                    if backup_dir:
                        backup_config.backup_dir = Path(backup_dir)
            
            return BackupManager(backup_config)
        except Exception as e:
            self.logger.warning(f"无法初始化备份管理器: {e}")
            return None
    
    @monitor_performance("enterprise_generate_with_backup")
    async def generate_with_backup(self, 
                                 project_path: Path,
                                 output_path: Path,
                                 context: Dict[str, Any],
                                 backup_description: Optional[str] = None) -> bool:
        """生成文档并备份现有文档
        
        Args:
            project_path: 项目路径
            output_path: 输出路径
            context: 上下文数据
            backup_description: 备份描述
            
        Returns:
            是否生成成功
        """
        try:
            # 1. 备份现有文档
            if self.backup_manager and output_path.exists():
                self.logger.info("备份现有文档...")
                backup_info = self.backup_manager.create_backup(
                    project_path, 
                    backup_description or f"生成{self.__class__.__name__}前的备份"
                )
                self.logger.info(f"备份完成: {backup_info.backup_id}")
            
            # 2. 生成新文档
            success = await self.generate(project_path, output_path, context)
            
            # 3. 清理旧备份
            if self.backup_manager and success:
                cleaned_count = self.backup_manager.cleanup_old_backups(project_path)
                if cleaned_count > 0:
                    self.logger.info(f"清理了 {cleaned_count} 个旧备份")
            
            return success
            
        except Exception as e:
            self.logger.error(f"生成文档失败: {e}")
            return False
    
    def get_project_analysis(self, project_path: Path) -> Optional[ProjectAnalysisResult]:
        """获取项目分析结果
        
        Args:
            project_path: 项目路径
            
        Returns:
            项目分析结果
        """
        try:
            # 尝试从分析器注册表获取分析结果
            from ...analyzers.registry import get_registry
            
            registry = get_registry()
            return registry.analyze_project(project_path)
            
        except Exception as e:
            self.logger.warning(f"无法获取项目分析结果: {e}")
            return None
    
    def build_enterprise_context(self, 
                               project_path: Path,
                               analysis_result: Optional[ProjectAnalysisResult] = None) -> Dict[str, Any]:
        """构建企业级上下文数据
        
        Args:
            project_path: 项目路径
            analysis_result: 项目分析结果
            
        Returns:
            企业级上下文数据
        """
        if not analysis_result:
            analysis_result = self.get_project_analysis(project_path)
        
        context = {
            'project_path': project_path,
            'project_name': project_path.name,
            'analysis_result': analysis_result,
            'timestamp': self._get_timestamp(),
            'generator_name': self.__class__.__name__
        }
        
        if analysis_result:
            context.update({
                'project_type': analysis_result.project_type.value,
                'framework': analysis_result.framework.value if analysis_result.framework else None,
                'tech_stack': analysis_result.tech_stack,
                'dependencies': analysis_result.dependencies,
                'apis': analysis_result.apis,
                'modules': analysis_result.modules,
                'architecture': analysis_result.architecture,
                'quality_metrics': analysis_result.quality_metrics,
                'deployment_config': analysis_result.deployment_config,
                'description': analysis_result.description,
                'version': analysis_result.version,
                'author': analysis_result.author,
                'license': analysis_result.license
            })
        
        return context
    
    @monitor_performance("ai_enhancement")
    async def enhance_with_ai_insights(self, 
                                     base_content: str,
                                     prompt_type: str,
                                     context: Dict[str, Any]) -> str:
        """使用AI增强文档内容
        
        Args:
            base_content: 基础内容
            prompt_type: 提示类型
            context: 上下文数据
            
        Returns:
            增强后的内容
        """
        if not self._ai_provider:
            self.logger.debug("AI提供商未初始化，使用基础内容")
            return base_content
        
        metrics_collector = get_metrics_collector()
        
        try:
            # 构建详细的AI增强上下文
            ai_context = {
                'base_content': base_content,
                'project_name': context.get('project_name', 'Unknown Project'),
                'project_type': context.get('project_type', 'unknown'),
                'framework': context.get('framework', 'unknown'),
                'generator_type': self.__class__.__name__,
                'current_date': self._get_timestamp(),
                # 添加更多上下文信息
                'tech_stack': self._format_tech_stack(context.get('tech_stack')),
                'api_count': len(context.get('apis', [])),
                'module_count': len(context.get('modules', [])),
                'main_features': self._extract_main_features(context),
                'deployment_info': self._format_deployment_info(context.get('deployment_config')),
                'architecture_pattern': self._extract_architecture_pattern(context.get('architecture'))
            }
            
            # 记录AI增强请求
            self.logger.info(f"正在使用AI增强 {prompt_type} 内容...")
            
            # 记录AI请求开始
            start_time = time.time()
            
            # 获取AI增强内容
            enhanced_content = await self.generate_ai_content(prompt_type, **ai_context)
            
            # 记录AI请求结果
            duration = time.time() - start_time
            success = enhanced_content and len(enhanced_content.strip()) > 100
            
            # 估算token使用量(简单估算)
            tokens_used = len(base_content) // 4 + len(enhanced_content or "") // 4
            
            metrics_collector.record_ai_request(
                success=success,
                response_time=duration,
                tokens_used=tokens_used,
                provider=self._ai_provider.__class__.__name__ if self._ai_provider else "unknown"
            )
            
            if success:
                self.logger.info(f"AI增强成功，内容长度: {len(enhanced_content)} 字符")
                return enhanced_content
            else:
                self.logger.warning("AI增强内容质量不足，使用基础内容")
                return base_content
                
        except Exception as e:
            # 记录AI请求失败
            duration = time.time() - start_time if 'start_time' in locals() else 0
            metrics_collector.record_ai_request(
                success=False,
                response_time=duration,
                tokens_used=0,
                provider=self._ai_provider.__class__.__name__ if self._ai_provider else "unknown"
            )
            metrics_collector.record_error("ai_enhancement_error", str(e))
            
            self.logger.error(f"AI增强失败: {e}")
            return base_content
    
    def _format_tech_stack(self, tech_stack) -> str:
        """格式化技术栈信息"""
        if not tech_stack:
            return "未知技术栈"
        
        parts = []
        if hasattr(tech_stack, 'languages') and tech_stack.languages:
            parts.append(f"语言: {', '.join(tech_stack.languages)}")
        if hasattr(tech_stack, 'frameworks') and tech_stack.frameworks:
            parts.append(f"框架: {', '.join(tech_stack.frameworks)}")
        if hasattr(tech_stack, 'databases') and tech_stack.databases:
            parts.append(f"数据库: {', '.join(tech_stack.databases)}")
        
        return '; '.join(parts) if parts else "基础技术栈"
    
    def _extract_main_features(self, context: Dict[str, Any]) -> List[str]:
        """提取主要功能特性"""
        features = []
        
        # 从API中提取功能
        apis = context.get('apis', [])
        if apis:
            features.append(f"提供{len(apis)}个API接口")
        
        # 从模块中提取功能
        modules = context.get('modules', [])
        if modules:
            features.append(f"包含{len(modules)}个功能模块")
        
        # 从框架推断功能
        framework = context.get('framework', '').lower()
        if framework == 'django':
            features.append("Web应用开发框架")
        elif framework == 'flask':
            features.append("轻量级Web服务")
        elif framework == 'fastapi':
            features.append("高性能API服务")
        
        return features
    
    def _format_deployment_info(self, deployment_config) -> str:
        """格式化部署信息"""
        if not deployment_config:
            return "传统部署"
        
        deploy_types = []
        if deployment_config.get('docker', {}).get('detected'):
            deploy_types.append("Docker容器化")
        if deployment_config.get('cloud', {}).get('detected'):
            deploy_types.append("云平台部署")
        if deployment_config.get('ci_cd', {}).get('detected'):
            deploy_types.append("CI/CD自动化")
        
        return ', '.join(deploy_types) if deploy_types else "传统部署"
    
    def _extract_architecture_pattern(self, architecture) -> str:
        """提取架构模式"""
        if not architecture:
            return "分层架构"
        
        if hasattr(architecture, 'pattern') and architecture.pattern:
            return architecture.pattern
        
        return "模块化架构"
    
    def get_document_sections(self) -> List[Dict[str, str]]:
        """获取文档章节结构
        
        Returns:
            章节结构列表，每个元素包含name和description
        """
        return [
            {'name': '概述', 'description': '文档概述和目的'},
            {'name': '核心内容', 'description': '文档的主要内容'},
            {'name': '附录', 'description': '补充信息和参考资料'}
        ]
    
    def validate_output(self, output_path: Path) -> bool:
        """验证输出文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否验证通过
        """
        try:
            if not output_path.exists():
                self.logger.error(f"输出文件不存在: {output_path}")
                return False
            
            # 检查文件大小
            file_size = output_path.stat().st_size
            if file_size == 0:
                self.logger.error(f"输出文件为空: {output_path}")
                return False
            
            # 检查文件内容
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if len(content.strip()) < 100:  # 最少100个字符
                    self.logger.warning(f"输出文件内容过少: {output_path}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证输出文件失败: {e}")
            return False