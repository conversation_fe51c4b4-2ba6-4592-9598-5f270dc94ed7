"""
命令行入口点
"""

import sys
import argparse
from pathlib import Path

from . import check_dependencies
from .config import Config
from .generators import (
    ApiGenerator,
    DiagramGenerator,
    StructureGenerator,
    DependencyGenerator,
    QualityGenerator
)

def parse_args(args=None):
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="生成项目文档")
    parser.add_argument("command", choices=["generate", "diagram"], help="要执行的命令")
    parser.add_argument("project_path", type=str, help="项目路径")
    parser.add_argument("--modules", type=str, help="要处理的模块列表，用逗号分隔")
    parser.add_argument("--skip-tests", action="store_true", help="跳过测试运行")
    parser.add_argument("--format", choices=["html", "markdown"], default="markdown", 
                      help="文档输出格式(默认: markdown)")
    parser.add_argument("--theme", type=str, help="HTML文档主题(仅在--format=html时有效)")
    parser.add_argument("--quick", action="store_true", help="快速模式，只生成基础文档")
    
    return parser.parse_args(args)

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 检查系统依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建配置
    config = Config()
    config.project_root = Path(args.project_path)
    config.docs_dir = config.project_root / "docs"
    config.update_from_args(args)
    
    # 如果没有指定模块，自动检测
    if not config.modules:
        if (config.project_root / "src").exists():
            config.modules = ["src"]
        else:
            config.modules = ["."]
    
    try:
        # 创建生成器
        generators = []
        
        if args.command == "generate":
            # 完整文档生成
            if not args.quick:
                generators.extend([
                    ApiGenerator(config),
                    DiagramGenerator(config),
                    StructureGenerator(config),
                    DependencyGenerator(config),
                    QualityGenerator(config)
                ])
            else:
                # 快速模式只生成基本文档
                generators.extend([
                    StructureGenerator(config),
                    DiagramGenerator(config)
                ])
        else:  # diagram
            # 只生成类图
            generators.append(DiagramGenerator(config))
        
        # 执行生成
        success = True
        for generator in generators:
            if not generator.generate():
                success = False
        
        if success:
            print("\n✓ 文档生成完成！")
            print(f"输出目录: {config.docs_dir}")
        else:
            print("\n⚠️ 文档生成过程中出现错误，请查看上面的日志")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 错误: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main() 