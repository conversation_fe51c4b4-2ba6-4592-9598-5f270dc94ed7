# {{ project_name }}

{{ description if description else "项目概览文档" }}

## 📋 项目信息

- **项目类型**: {{ project_type }}
- **主要语言**: {{ primary_language }}
- **创建时间**: {{ created_date if created_date else "未知" }}
- **文档生成时间**: {{ generation_date }}

## 🚀 快速开始

### 环境要求

{% if tech_stack and tech_stack.runtime %}
#### 运行环境
{% for runtime in tech_stack.runtime %}
- {{ runtime }}
{% endfor %}
{% endif %}

{% if dependencies %}
#### 主要依赖
{% for dep in dependencies[:10] %}
- {{ dep.name }}{% if dep.version %} ({{ dep.version }}){% endif %}
{% endfor %}
{% if dependencies|length > 10 %}
- ... 及其他 {{ dependencies|length - 10 }} 个依赖
{% endif %}
{% endif %}

### 安装步骤

{% if installation_steps %}
{% for step in installation_steps %}
{{ loop.index }}. {{ step }}
{% endfor %}
{% else %}
1. 克隆项目到本地
2. 安装依赖包
3. 运行项目
{% endif %}

```bash
# 示例安装命令
{% if project_type == "python" %}
pip install -r requirements.txt
python main.py
{% elif project_type == "javascript" or project_type == "typescript" %}
npm install
npm start
{% elif project_type == "java" %}
mvn install
mvn spring-boot:run
{% else %}
# 请参考项目相关文档
{% endif %}
```

## 📁 项目结构

```
{{ project_structure if project_structure else project_name + "/\n├── src/           # 源代码\n├── docs/          # 文档\n├── tests/         # 测试文件\n└── README.md      # 项目说明" }}
```

{% if main_features %}
## ✨ 主要功能

{% for feature in main_features %}
- **{{ feature.name }}**: {{ feature.description }}
{% endfor %}
{% endif %}

{% if apis and apis|length > 0 %}
## 🔌 API 接口

{% for api in apis[:5] %}
### {{ api.method }} {{ api.path }}
{{ api.description if api.description else "API接口" }}

{% endfor %}

{% if apis|length > 5 %}
> 更多API接口请查看 [API文档](./api_docs.md)
{% endif %}
{% endif %}

{% if tech_stack %}
## 🛠 技术栈

{% if tech_stack.languages %}
### 编程语言
{% for lang in tech_stack.languages %}
- {{ lang }}
{% endfor %}
{% endif %}

{% if tech_stack.frameworks %}
### 框架/库
{% for framework in tech_stack.frameworks %}
- {{ framework }}
{% endfor %}
{% endif %}

{% if tech_stack.tools %}
### 开发工具
{% for tool in tech_stack.tools %}
- {{ tool }}
{% endfor %}
{% endif %}
{% endif %}

{% if deployment_info %}
## 🚀 部署说明

{% if deployment_info.docker %}
### Docker 部署
```bash
# 构建镜像
docker build -t {{ project_name }} .

# 运行容器
docker run -p 8080:8080 {{ project_name }}
```
{% endif %}

{% if deployment_info.traditional %}
### 传统部署
{{ deployment_info.traditional.description if deployment_info.traditional.description else "请参考具体的部署文档" }}
{% endif %}
{% endif %}

## 📖 相关文档

- [架构设计](./architecture.md) - 系统架构和设计决策
- [API文档](./api_docs.md) - 接口详细说明
- [开发指南](./dev_guide.md) - 开发环境搭建和规范
- [部署文档](./deployment.md) - 部署配置和说明

{% if changelog_highlights %}
## 📝 更新日志

{% for change in changelog_highlights %}
### {{ change.version }} ({{ change.date }})
{{ change.description }}

{% endfor %}

> 完整更新日志请查看 [CHANGELOG.md](./CHANGELOG.md)
{% endif %}

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

{{ license if license else "本项目采用 MIT 许可证" }}

---

*此文档由 GenDocs 自动生成于 {{ generation_date }}*