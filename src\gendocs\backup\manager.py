"""
文档备份管理器

提供文档备份、恢复和管理功能。
"""

import fnmatch
import hashlib
import logging
import shutil
import tarfile
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any

from .models import BackupConfig, BackupInfo, BackupManifest, BackupStrategy

logger = logging.getLogger(__name__)


class BackupManager:
    """文档备份管理器
    
    负责管理项目文档的备份、恢复和版本控制。
    """
    
    def __init__(self, config: Optional[BackupConfig] = None):
        """初始化备份管理器
        
        Args:
            config: 备份配置，如果为None则使用默认配置
        """
        self.config = config or BackupConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置默认备份目录
        if not self.config.backup_dir:
            self.config.backup_dir = Path.cwd() / '.gendocs' / 'backups'
    
    def create_backup(self, project_path: Path, description: Optional[str] = None) -> BackupInfo:
        """创建项目文档备份
        
        Args:
            project_path: 项目根目录路径
            description: 备份描述
            
        Returns:
            备份信息
            
        Raises:
            BackupError: 备份失败时抛出
        """
        if not self.config.enabled:
            raise BackupError("备份功能已禁用")
        
        if not project_path.exists() or not project_path.is_dir():
            raise BackupError(f"无效的项目路径: {project_path}")
        
        try:
            self.logger.info(f"开始备份项目文档: {project_path}")
            
            # 生成备份ID和路径
            backup_id = self._generate_backup_id()
            backup_path = self._get_backup_path(project_path, backup_id)
            
            # 确保备份目录存在
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 查找需要备份的文档文件
            doc_files = self._find_documentation_files(project_path)
            
            if not doc_files:
                self.logger.warning(f"项目中未找到文档文件: {project_path}")
                
            # 创建备份
            if self.config.strategy == BackupStrategy.TIMESTAMP:
                actual_backup_path = self._create_timestamp_backup(project_path, doc_files, backup_path)
            elif self.config.strategy == BackupStrategy.ARCHIVE:
                actual_backup_path = self._create_archive_backup(project_path, doc_files, backup_path)
            else:
                actual_backup_path = self._create_full_backup(project_path, doc_files, backup_path)
            
            # 创建备份信息
            backup_info = BackupInfo(
                backup_id=backup_id,
                strategy=self.config.strategy,
                created_at=datetime.now(),
                source_path=project_path,
                backup_path=actual_backup_path,
                description=description or f"项目文档备份 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                file_count=len(doc_files),
                total_size=self._calculate_total_size(doc_files)
            )
            
            # 更新备份清单
            self._update_backup_manifest(project_path, backup_info)
            
            self.logger.info(f"备份完成: {backup_info.backup_id}")
            return backup_info
            
        except Exception as e:
            self.logger.error(f"备份失败: {e}")
            raise BackupError(f"备份失败: {e}")
    
    def restore_backup(self, backup_id: str, project_path: Path, overwrite: bool = False) -> bool:
        """恢复备份
        
        Args:
            backup_id: 备份ID
            project_path: 项目路径
            overwrite: 是否覆盖现有文件
            
        Returns:
            是否恢复成功
        """
        try:
            self.logger.info(f"开始恢复备份: {backup_id}")
            
            # 获取备份信息
            manifest = self._load_backup_manifest(project_path)
            backup_info = manifest.get_backup(backup_id)
            
            if not backup_info:
                raise BackupError(f"备份不存在: {backup_id}")
            
            if not backup_info.backup_path.exists():
                raise BackupError(f"备份文件不存在: {backup_info.backup_path}")
            
            # 恢复备份
            if backup_info.backup_path.suffix == '.tar.gz':
                self._restore_archive_backup(backup_info.backup_path, project_path, overwrite)
            else:
                self._restore_directory_backup(backup_info.backup_path, project_path, overwrite)
            
            self.logger.info(f"备份恢复完成: {backup_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"恢复备份失败: {e}")
            raise BackupError(f"恢复备份失败: {e}")
    
    def list_backups(self, project_path: Path) -> List[BackupInfo]:
        """列出项目的所有备份
        
        Args:
            project_path: 项目路径
            
        Returns:
            备份信息列表
        """
        try:
            manifest = self._load_backup_manifest(project_path)
            return sorted(manifest.backups, key=lambda b: b.created_at, reverse=True)
        except Exception as e:
            self.logger.warning(f"无法加载备份清单: {e}")
            return []
    
    def delete_backup(self, backup_id: str, project_path: Path) -> bool:
        """删除备份
        
        Args:
            backup_id: 备份ID
            project_path: 项目路径
            
        Returns:
            是否删除成功
        """
        try:
            manifest = self._load_backup_manifest(project_path)
            backup_info = manifest.get_backup(backup_id)
            
            if not backup_info:
                self.logger.warning(f"备份不存在: {backup_id}")
                return False
            
            # 删除备份文件
            if backup_info.backup_path.exists():
                if backup_info.backup_path.is_dir():
                    shutil.rmtree(backup_info.backup_path)
                else:
                    backup_info.backup_path.unlink()
            
            # 从清单中移除
            manifest.remove_backup(backup_id)
            self._save_backup_manifest(project_path, manifest)
            
            self.logger.info(f"已删除备份: {backup_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除备份失败: {e}")
            return False
    
    def cleanup_old_backups(self, project_path: Path) -> int:
        """清理旧备份
        
        Args:
            project_path: 项目路径
            
        Returns:
            清理的备份数量
        """
        try:
            manifest = self._load_backup_manifest(project_path)
            old_backups = manifest.cleanup_old_backups(self.config.max_backups)
            
            # 删除物理文件
            cleaned_count = 0
            for backup_info in old_backups:
                if backup_info.backup_path.exists():
                    if backup_info.backup_path.is_dir():
                        shutil.rmtree(backup_info.backup_path)
                    else:
                        backup_info.backup_path.unlink()
                    cleaned_count += 1
            
            # 保存更新后的清单
            if old_backups:
                self._save_backup_manifest(project_path, manifest)
            
            if cleaned_count > 0:
                self.logger.info(f"已清理 {cleaned_count} 个旧备份")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
            return 0
    
    def _generate_backup_id(self) -> str:
        """生成备份ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        return f"{timestamp}_{unique_id}"
    
    def _get_backup_path(self, project_path: Path, backup_id: str) -> Path:
        """获取备份路径"""
        project_name = project_path.name
        backup_dir = self.config.backup_dir / project_name
        
        if self.config.strategy == BackupStrategy.ARCHIVE:
            return backup_dir / f"{backup_id}.tar.gz"
        else:
            return backup_dir / backup_id
    
    def _find_documentation_files(self, project_path: Path) -> List[Path]:
        """查找文档文件"""
        doc_files = []
        
        for pattern in self.config.include_patterns:
            for file_path in project_path.rglob(pattern):
                if (file_path.is_file() and 
                    self._should_include_file(file_path, project_path)):
                    doc_files.append(file_path)
        
        # 去重
        return list(set(doc_files))
    
    def _should_include_file(self, file_path: Path, project_path: Path) -> bool:
        """判断是否应该包含该文件"""
        relative_path = file_path.relative_to(project_path)
        relative_str = str(relative_path)
        
        # 检查排除模式
        for pattern in self.config.exclude_patterns:
            if fnmatch.fnmatch(relative_str, pattern):
                return False
        
        # 排除隐藏目录和常见的构建目录
        excluded_dirs = {
            '.git', '.venv', '__pycache__', 'node_modules',
            '.idea', '.vscode', 'build', 'dist', 'target'
        }
        
        for parent in file_path.parents:
            if parent.name in excluded_dirs:
                return False
        
        return True
    
    def _calculate_total_size(self, files: List[Path]) -> int:
        """计算文件总大小"""
        total_size = 0
        for file_path in files:
            try:
                if file_path.exists():
                    total_size += file_path.stat().st_size
            except Exception:
                continue
        return total_size
    
    def _create_timestamp_backup(self, project_path: Path, doc_files: List[Path], backup_path: Path) -> Path:
        """创建时间戳备份"""
        backup_path.mkdir(parents=True, exist_ok=True)
        
        for file_path in doc_files:
            relative_path = file_path.relative_to(project_path)
            target_path = backup_path / relative_path
            
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(file_path, target_path)
        
        return backup_path
    
    def _create_archive_backup(self, project_path: Path, doc_files: List[Path], backup_path: Path) -> Path:
        """创建归档备份"""
        with tarfile.open(backup_path, 'w:gz') as tar:
            for file_path in doc_files:
                relative_path = file_path.relative_to(project_path)
                tar.add(file_path, arcname=relative_path)
        
        return backup_path
    
    def _create_full_backup(self, project_path: Path, doc_files: List[Path], backup_path: Path) -> Path:
        """创建完整备份"""
        return self._create_timestamp_backup(project_path, doc_files, backup_path)
    
    def _restore_directory_backup(self, backup_path: Path, project_path: Path, overwrite: bool) -> None:
        """恢复目录备份"""
        for file_path in backup_path.rglob('*'):
            if file_path.is_file():
                relative_path = file_path.relative_to(backup_path)
                target_path = project_path / relative_path
                
                if target_path.exists() and not overwrite:
                    self.logger.warning(f"文件已存在，跳过: {target_path}")
                    continue
                
                # 确保目标目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(file_path, target_path)
    
    def _restore_archive_backup(self, backup_path: Path, project_path: Path, overwrite: bool) -> None:
        """恢复归档备份"""
        with tarfile.open(backup_path, 'r:gz') as tar:
            for member in tar.getmembers():
                if member.isfile():
                    target_path = project_path / member.name
                    
                    if target_path.exists() and not overwrite:
                        self.logger.warning(f"文件已存在，跳过: {target_path}")
                        continue
                    
                    # 提取文件
                    tar.extract(member, project_path)
    
    def _load_backup_manifest(self, project_path: Path) -> BackupManifest:
        """加载备份清单"""
        manifest_path = self._get_manifest_path(project_path)
        
        if manifest_path.exists():
            return BackupManifest.load_from_file(manifest_path)
        else:
            return BackupManifest(
                project_name=project_path.name,
                project_path=project_path
            )
    
    def _save_backup_manifest(self, project_path: Path, manifest: BackupManifest) -> None:
        """保存备份清单"""
        manifest_path = self._get_manifest_path(project_path)
        manifest_path.parent.mkdir(parents=True, exist_ok=True)
        manifest.save_to_file(manifest_path)
    
    def _update_backup_manifest(self, project_path: Path, backup_info: BackupInfo) -> None:
        """更新备份清单"""
        manifest = self._load_backup_manifest(project_path)
        manifest.add_backup(backup_info)
        self._save_backup_manifest(project_path, manifest)
    
    def _get_manifest_path(self, project_path: Path) -> Path:
        """获取清单文件路径"""
        project_name = project_path.name
        return self.config.backup_dir / project_name / 'manifest.json'


class BackupError(Exception):
    """备份错误异常"""
    pass