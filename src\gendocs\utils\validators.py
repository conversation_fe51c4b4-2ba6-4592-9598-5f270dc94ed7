"""
验证器模块

提供项目、配置和数据的验证功能。
"""

import re
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Type
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .error_handler import ValidationError, ErrorContext


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    
    def add_error(self, message: str) -> None:
        """添加错误"""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str) -> None:
        """添加警告"""
        self.warnings.append(message)
    
    def merge(self, other: 'ValidationResult') -> 'ValidationResult':
        """合并验证结果"""
        return ValidationResult(
            is_valid=self.is_valid and other.is_valid,
            errors=self.errors + other.errors,
            warnings=self.warnings + other.warnings
        )


class BaseValidator(ABC):
    """验证器基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def validate(self, data: Any) -> ValidationResult:
        """验证数据
        
        Args:
            data: 要验证的数据
            
        Returns:
            验证结果
        """
        pass
    
    def _create_error_context(self, 
                            operation: str,
                            data: Any,
                            field_name: str = "") -> ErrorContext:
        """创建错误上下文"""
        from datetime import datetime
        
        return ErrorContext(
            operation=operation,
            component=self.__class__.__name__,
            parameters={'field_name': field_name, 'data_type': type(data).__name__},
            timestamp=datetime.now()
        )


class ProjectValidator(BaseValidator):
    """项目验证器"""
    
    def __init__(self):
        super().__init__()
        self.required_patterns = [
            "*.py",      # Python文件
            "setup.py",  # 安装脚本
            "pyproject.toml",  # 项目配置
            "requirements.txt",  # 依赖文件
            "README.*",  # 说明文档
        ]
        
        self.recommended_patterns = [
            "LICENSE*",  # 许可证
            ".gitignore",  # Git忽略文件
            "tests/",    # 测试目录
            "docs/",     # 文档目录
        ]
    
    def validate(self, project_path: Union[str, Path]) -> ValidationResult:
        """验证项目结构
        
        Args:
            project_path: 项目路径
            
        Returns:
            验证结果
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[])
        
        if isinstance(project_path, str):
            project_path = Path(project_path)
        
        # 检查路径是否存在
        if not project_path.exists():
            result.add_error(f"项目路径不存在: {project_path}")
            return result
        
        if not project_path.is_dir():
            result.add_error(f"项目路径不是目录: {project_path}")
            return result
        
        # 验证基本结构
        self._validate_basic_structure(project_path, result)
        
        # 验证Python项目特征
        self._validate_python_project(project_path, result)
        
        # 验证代码质量
        self._validate_code_quality(project_path, result)
        
        # 验证文档完整性
        self._validate_documentation(project_path, result)
        
        return result
    
    def _validate_basic_structure(self, project_path: Path, result: ValidationResult) -> None:
        """验证基本项目结构"""
        # 检查是否为空目录
        if not any(project_path.iterdir()):
            result.add_error("项目目录为空")
            return
        
        # 检查是否有Python文件
        python_files = list(project_path.rglob("*.py"))
        if not python_files:
            result.add_warning("未找到Python文件")
        elif len(python_files) < 3:
            result.add_warning("Python文件数量较少，可能不是完整项目")
        
        # 检查必需文件
        found_required = False
        for pattern in self.required_patterns:
            if list(project_path.glob(pattern)):
                found_required = True
                break
        
        if not found_required:
            result.add_warning("未找到常见的项目标识文件 (setup.py, pyproject.toml, requirements.txt 等)")
    
    def _validate_python_project(self, project_path: Path, result: ValidationResult) -> None:
        """验证Python项目特征"""
        # 检查setup.py或pyproject.toml
        has_setup_py = (project_path / "setup.py").exists()
        has_pyproject_toml = (project_path / "pyproject.toml").exists()
        
        if not has_setup_py and not has_pyproject_toml:
            result.add_warning("未找到 setup.py 或 pyproject.toml 文件")
        
        # 检查requirements.txt或environment.yml
        has_requirements = (project_path / "requirements.txt").exists()
        has_env_yml = (project_path / "environment.yml").exists()
        has_pipfile = (project_path / "Pipfile").exists()
        
        if not any([has_requirements, has_env_yml, has_pipfile]):
            result.add_warning("未找到依赖管理文件 (requirements.txt, environment.yml, Pipfile)")
        
        # 检查__init__.py文件
        init_files = list(project_path.rglob("__init__.py"))
        if not init_files:
            result.add_warning("未找到 __init__.py 文件，可能不是Python包")
    
    def _validate_code_quality(self, project_path: Path, result: ValidationResult) -> None:
        """验证代码质量"""
        python_files = list(project_path.rglob("*.py"))
        
        if not python_files:
            return
        
        # 统计代码行数
        total_lines = 0
        empty_files = 0
        large_files = []
        
        for py_file in python_files[:50]:  # 限制检查文件数量
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    line_count = len([line for line in lines if line.strip()])
                    
                    if line_count == 0:
                        empty_files += 1
                    elif line_count > 1000:
                        large_files.append(py_file.name)
                    
                    total_lines += line_count
                    
            except (UnicodeDecodeError, PermissionError):
                continue
        
        # 警告和建议
        if empty_files > len(python_files) * 0.3:
            result.add_warning(f"发现 {empty_files} 个空的Python文件")
        
        if large_files:
            result.add_warning(f"发现超大文件 (>1000行): {', '.join(large_files[:3])}")
        
        if total_lines < 100:
            result.add_warning("代码量较少，可能是演示项目或模板")
    
    def _validate_documentation(self, project_path: Path, result: ValidationResult) -> None:
        """验证文档完整性"""
        # 检查README文件
        readme_files = list(project_path.glob("README*"))
        if not readme_files:
            result.add_warning("未找到 README 文件")
        
        # 检查LICENSE文件
        license_files = list(project_path.glob("LICENSE*"))
        if not license_files:
            result.add_warning("未找到 LICENSE 文件")
        
        # 检查docs目录
        docs_dir = project_path / "docs"
        if not docs_dir.exists():
            result.add_warning("未找到 docs 文档目录")
        elif not any(docs_dir.iterdir()):
            result.add_warning("docs 目录为空")


class ConfigValidator(BaseValidator):
    """配置验证器"""
    
    def __init__(self):
        super().__init__()
        self.required_fields = {
            'ai': dict,
            'generation': dict,
        }
        
        self.ai_required_fields = {
            'provider': str,
            'model': str,
        }
        
        self.generation_required_fields = {
            'output_dir': str,
        }
    
    def validate(self, config: Dict[str, Any]) -> ValidationResult:
        """验证配置数据
        
        Args:
            config: 配置字典
            
        Returns:
            验证结果
        """
        result = ValidationResult(is_valid=True, errors=[], warnings=[])
        
        # 验证顶层必需字段
        self._validate_required_fields(config, self.required_fields, result)
        
        # 验证AI配置
        if 'ai' in config:
            self._validate_ai_config(config['ai'], result)
        
        # 验证生成配置
        if 'generation' in config:
            self._validate_generation_config(config['generation'], result)
        
        # 验证备份配置
        if 'backup' in config:
            self._validate_backup_config(config['backup'], result)
        
        return result
    
    def _validate_required_fields(self, 
                                 data: Dict[str, Any], 
                                 required_fields: Dict[str, Type],
                                 result: ValidationResult,
                                 prefix: str = "") -> None:
        """验证必需字段"""
        for field_name, field_type in required_fields.items():
            full_field_name = f"{prefix}.{field_name}" if prefix else field_name
            
            if field_name not in data:
                result.add_error(f"缺少必需字段: {full_field_name}")
                continue
            
            field_value = data[field_name]
            if not isinstance(field_value, field_type):
                result.add_error(f"字段类型错误: {full_field_name} 应为 {field_type.__name__}")
    
    def _validate_ai_config(self, ai_config: Dict[str, Any], result: ValidationResult) -> None:
        """验证AI配置"""
        # 验证必需字段
        self._validate_required_fields(ai_config, self.ai_required_fields, result, "ai")
        
        # 验证provider
        if 'provider' in ai_config:
            valid_providers = ['openai', 'deepseek', 'qwen', 'claude']
            if ai_config['provider'] not in valid_providers:
                result.add_warning(f"未知的AI提供商: {ai_config['provider']}")
        
        # 验证API密钥
        if 'api_key' in ai_config:
            api_key = ai_config['api_key']
            if isinstance(api_key, str):
                if api_key.startswith('${') and api_key.endswith('}'):
                    # 环境变量格式，检查是否有默认值
                    import os
                    env_var = api_key[2:-1]
                    if not os.getenv(env_var):
                        result.add_warning(f"环境变量 {env_var} 未设置")
                elif len(api_key) < 10:
                    result.add_warning("API密钥长度过短，可能无效")
        else:
            result.add_warning("未配置API密钥")
        
        # 验证数值范围
        if 'temperature' in ai_config:
            temp = ai_config['temperature']
            if not isinstance(temp, (int, float)) or not 0 <= temp <= 2:
                result.add_error("temperature 必须是 0-2 之间的数值")
        
        if 'max_tokens' in ai_config:
            max_tokens = ai_config['max_tokens']
            if not isinstance(max_tokens, int) or max_tokens < 1:
                result.add_error("max_tokens 必须是正整数")
    
    def _validate_generation_config(self, gen_config: Dict[str, Any], result: ValidationResult) -> None:
        """验证生成配置"""
        # 验证必需字段
        self._validate_required_fields(gen_config, self.generation_required_fields, result, "generation")
        
        # 验证并发作业数
        if 'concurrent_jobs' in gen_config:
            concurrent_jobs = gen_config['concurrent_jobs']
            if not isinstance(concurrent_jobs, int) or concurrent_jobs < 1 or concurrent_jobs > 20:
                result.add_warning("concurrent_jobs 建议设置为 1-20 之间的整数")
        
        # 验证生成器列表
        if 'default_generators' in gen_config:
            generators = gen_config['default_generators']
            if not isinstance(generators, list):
                result.add_error("default_generators 必须是列表")
            else:
                valid_generators = [
                    'overview', 'architecture', 'api_docs', 'installation',
                    'deployment', 'security', 'changelog', 'troubleshooting'
                ]
                for gen in generators:
                    if gen not in valid_generators:
                        result.add_warning(f"未知的生成器: {gen}")
    
    def _validate_backup_config(self, backup_config: Dict[str, Any], result: ValidationResult) -> None:
        """验证备份配置"""
        # 验证最大备份数
        if 'max_backups' in backup_config:
            max_backups = backup_config['max_backups']
            if not isinstance(max_backups, int) or max_backups < 1:
                result.add_error("max_backups 必须是正整数")
        
        # 验证策略
        if 'strategy' in backup_config:
            strategy = backup_config['strategy']
            valid_strategies = ['timestamp', 'sequence']
            if strategy not in valid_strategies:
                result.add_error(f"无效的备份策略: {strategy}")


class DataValidator(BaseValidator):
    """数据验证器"""
    
    def validate(self, data: Any) -> ValidationResult:
        """验证通用数据"""
        result = ValidationResult(is_valid=True, errors=[], warnings=[])
        
        # 这里可以添加通用的数据验证逻辑
        if data is None:
            result.add_error("数据不能为None")
        
        return result
    
    def validate_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    def validate_url(self, url: str) -> bool:
        """验证URL格式"""
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, url))
    
    def validate_path(self, path: Union[str, Path]) -> bool:
        """验证路径格式"""
        try:
            Path(path)
            return True
        except (TypeError, ValueError):
            return False
    
    def validate_version(self, version: str) -> bool:
        """验证版本号格式 (语义化版本)"""
        pattern = r'^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$'
        return bool(re.match(pattern, version))


def validate_project(project_path: Union[str, Path]) -> ValidationResult:
    """验证项目的便捷函数
    
    Args:
        project_path: 项目路径
        
    Returns:
        验证结果
    """
    validator = ProjectValidator()
    return validator.validate(project_path)


def validate_config(config: Dict[str, Any]) -> ValidationResult:
    """验证配置的便捷函数
    
    Args:
        config: 配置字典
        
    Returns:
        验证结果
    """
    validator = ConfigValidator()
    return validator.validate(config)


def validate_and_raise(validator: BaseValidator, 
                      data: Any,
                      operation: str = "validation") -> None:
    """验证数据，如果失败则抛出异常
    
    Args:
        validator: 验证器实例
        data: 要验证的数据
        operation: 操作名称
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    result = validator.validate(data)
    
    if not result.is_valid:
        error_message = f"验证失败: {'; '.join(result.errors)}"
        context = validator._create_error_context(operation, data)
        raise ValidationError(error_message, context=context)