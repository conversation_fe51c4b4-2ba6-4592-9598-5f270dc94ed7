"""
项目概览文档生成器

生成项目概览文档，包括项目介绍、快速开始、功能特性等。
"""

from pathlib import Path
from typing import Dict, Any, List

from .base import EnterpriseGenerator


class OverviewGenerator(EnterpriseGenerator):
    """项目概览文档生成器
    
    生成全面的项目概览文档，相当于增强版的README。
    """
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/project_overview.md.j2"
        self.output_filename = "PROJECT_OVERVIEW.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成项目概览文档
        
        Args:
            project_path: 项目路径
            output_path: 输出路径
            context: 上下文数据
            
        Returns:
            是否生成成功
        """
        try:
            self.logger.info(f"开始生成项目概览文档: {project_path}")
            
            # 构建企业级上下文
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            # 构建概览特定上下文
            overview_context = self._build_overview_context(enterprise_context)
            
            # 渲染基础模板
            base_content = self.render_template(self.template_name, overview_context)
            
            # AI增强内容
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "project_overview_enhancement",
                    **overview_context
                )
                if enhanced_content:
                    base_content = self._merge_ai_content(base_content, enhanced_content)
            
            # 写入文件
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            # 验证输出
            if self.validate_output(final_output_path):
                self.logger.info(f"项目概览文档生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成项目概览文档失败: {e}")
            return False
    
    def _build_overview_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建概览特定上下文"""
        analysis_result = context.get('analysis_result')
        
        overview_context = context.copy()
        overview_context.update({
            'badges': self._generate_badges(context),
            'features': self._extract_features(analysis_result),
            'quick_start': self._generate_quick_start(analysis_result),
            'installation': self._generate_installation_guide(analysis_result),
            'usage_examples': self._generate_usage_examples(analysis_result),
            'project_structure': self._generate_project_structure(analysis_result),
            'contributing': self._generate_contributing_guide(context),
            'roadmap': self._generate_roadmap(analysis_result),
            'faq': self._generate_faq(analysis_result)
        })
        
        return overview_context
    
    def _generate_badges(self, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """生成项目徽章"""
        badges = []
        
        # 基础徽章
        project_type = context.get('project_type', '').lower()
        if project_type == 'python':
            badges.append({
                'name': 'Python',
                'url': 'https://img.shields.io/badge/python-3.8+-blue.svg',
                'alt': 'Python Version'
            })
        elif project_type in ['javascript', 'typescript']:
            badges.append({
                'name': 'Node.js',
                'url': 'https://img.shields.io/badge/node.js-16+-green.svg',
                'alt': 'Node.js Version'
            })
        
        # 框架徽章
        framework = context.get('framework', '').lower()
        if framework == 'django':
            badges.append({
                'name': 'Django',
                'url': 'https://img.shields.io/badge/django-4.0+-092E20.svg',
                'alt': 'Django Version'
            })
        elif framework == 'flask':
            badges.append({
                'name': 'Flask',
                'url': 'https://img.shields.io/badge/flask-2.0+-000000.svg',
                'alt': 'Flask Version'
            })
        elif framework == 'fastapi':
            badges.append({
                'name': 'FastAPI',
                'url': 'https://img.shields.io/badge/fastapi-0.70+-009688.svg',
                'alt': 'FastAPI Version'
            })
        elif framework == 'react':
            badges.append({
                'name': 'React',
                'url': 'https://img.shields.io/badge/react-18+-61DAFB.svg',
                'alt': 'React Version'
            })
        
        # 许可证徽章
        license_name = context.get('license')
        if license_name:
            badges.append({
                'name': 'License',
                'url': f'https://img.shields.io/badge/license-{license_name}-blue.svg',
                'alt': f'{license_name} License'
            })
        
        return badges
    
    def _extract_features(self, analysis_result) -> List[Dict[str, str]]:
        """提取项目特性"""
        features = []
        
        if not analysis_result:
            return features
        
        # 从框架推断特性
        if analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            if framework == 'django':
                features.extend([
                    {'name': 'Web应用框架', 'description': '基于Django的Web应用开发'},
                    {'name': 'ORM数据库', 'description': '内置ORM支持多种数据库'},
                    {'name': '管理后台', 'description': '自动生成的管理界面'}
                ])
            elif framework == 'flask':
                features.extend([
                    {'name': '轻量级框架', 'description': '基于Flask的轻量级Web应用'},
                    {'name': '灵活扩展', 'description': '模块化设计，易于扩展'},
                    {'name': 'RESTful API', 'description': '支持RESTful API开发'}
                ])
            elif framework == 'fastapi':
                features.extend([
                    {'name': '高性能API', 'description': '基于FastAPI的高性能Web API'},
                    {'name': '自动文档', 'description': '自动生成API文档'},
                    {'name': '类型检查', 'description': '基于Python类型提示的数据验证'}
                ])
        
        # 从依赖推断特性
        if analysis_result.dependencies:
            dep_names = {dep.name.lower() for dep in analysis_result.dependencies}
            
            if 'redis' in dep_names:
                features.append({'name': '缓存支持', 'description': '使用Redis进行数据缓存'})
            
            if any(db in dep_names for db in ['psycopg2', 'pymongo', 'mysql-connector-python']):
                features.append({'name': '数据库集成', 'description': '支持多种数据库存储'})
            
            if 'celery' in dep_names:
                features.append({'name': '异步任务', 'description': '使用Celery处理后台任务'})
            
            if 'pytest' in dep_names:
                features.append({'name': '测试覆盖', 'description': '完整的单元测试覆盖'})
        
        # 从API推断特性
        if analysis_result.apis:
            features.append({
                'name': 'API接口',
                'description': f'提供{len(analysis_result.apis)}个API接口'
            })
        
        return features
    
    def _generate_quick_start(self, analysis_result) -> List[Dict[str, str]]:
        """生成快速开始指南"""
        steps = []
        
        if not analysis_result:
            return [
                {'step': '1', 'title': '克隆项目', 'command': 'git clone <repository-url>'},
                {'step': '2', 'title': '安装依赖', 'command': '# 参考安装指南'},
                {'step': '3', 'title': '运行项目', 'command': '# 参考使用说明'}
            ]
        
        project_type = analysis_result.project_type.value.lower()
        
        if project_type == 'python':
            steps = [
                {'step': '1', 'title': '克隆项目', 'command': 'git clone <repository-url>'},
                {'step': '2', 'title': '创建虚拟环境', 'command': 'python -m venv venv\nsource venv/bin/activate  # Linux/Mac\nvenv\\Scripts\\activate     # Windows'},
                {'step': '3', 'title': '安装依赖', 'command': 'pip install -r requirements.txt'},
                {'step': '4', 'title': '运行项目', 'command': self._get_python_run_command(analysis_result)}
            ]
        elif project_type in ['javascript', 'typescript']:
            steps = [
                {'step': '1', 'title': '克隆项目', 'command': 'git clone <repository-url>'},
                {'step': '2', 'title': '安装依赖', 'command': 'npm install'},
                {'step': '3', 'title': '运行项目', 'command': 'npm start'}
            ]
        
        return steps
    
    def _get_python_run_command(self, analysis_result) -> str:
        """获取Python项目运行命令"""
        if analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            if framework == 'django':
                return 'python manage.py runserver'
            elif framework == 'flask':
                return 'python app.py'
            elif framework == 'fastapi':
                return 'uvicorn main:app --reload'
        
        return 'python main.py'
    
    def _generate_installation_guide(self, analysis_result) -> Dict[str, Any]:
        """生成安装指南"""
        guide = {
            'requirements': [],
            'steps': [],
            'verification': []
        }
        
        if not analysis_result:
            return guide
        
        project_type = analysis_result.project_type.value.lower()
        
        if project_type == 'python':
            guide['requirements'] = ['Python 3.8+', 'pip']
            guide['steps'] = [
                '克隆仓库到本地',
                '创建Python虚拟环境',
                '激活虚拟环境',
                '安装项目依赖',
                '配置环境变量（如需要）',
                '运行数据库迁移（如需要）'
            ]
            guide['verification'] = [
                'python --version',
                'pip list',
                self._get_python_run_command(analysis_result)
            ]
        
        return guide
    
    def _generate_usage_examples(self, analysis_result) -> List[Dict[str, str]]:
        """生成使用示例"""
        examples = []
        
        if not analysis_result or not analysis_result.apis:
            return examples
        
        # 从API生成使用示例
        for api in analysis_result.apis[:3]:  # 只取前3个API作为示例
            example = {
                'title': f'{api.method} {api.path}',
                'description': api.description or f'{api.method}请求示例',
                'code': self._generate_api_example(api)
            }
            examples.append(example)
        
        return examples
    
    def _generate_api_example(self, api) -> str:
        """生成API使用示例代码"""
        if api.method.upper() == 'GET':
            return f"""# GET请求示例
curl -X GET "{api.path}"

# Python requests示例
import requests
response = requests.get("{api.path}")
print(response.json())"""
        
        elif api.method.upper() == 'POST':
            return f"""# POST请求示例
curl -X POST "{api.path}" \\
  -H "Content-Type: application/json" \\
  -d '{{"key": "value"}}'

# Python requests示例
import requests
data = {{"key": "value"}}
response = requests.post("{api.path}", json=data)
print(response.json())"""
        
        return f"""# {api.method}请求示例
curl -X {api.method.upper()} "{api.path}\""""
    
    def _generate_project_structure(self, analysis_result) -> Dict[str, Any]:
        """生成项目结构说明"""
        structure = {
            'description': '项目目录结构说明',
            'tree': [],
            'key_files': []
        }
        
        if not analysis_result:
            return structure
        
        # 基于模块信息生成结构
        if analysis_result.modules:
            for module in analysis_result.modules:
                structure['tree'].append({
                    'path': module.path,
                    'description': module.description or '项目模块'
                })
        
        # 添加关键文件说明
        project_type = analysis_result.project_type.value.lower()
        if project_type == 'python':
            structure['key_files'] = [
                {'file': 'requirements.txt', 'description': '项目依赖列表'},
                {'file': 'setup.py', 'description': '项目安装配置'},
                {'file': 'README.md', 'description': '项目说明文档'},
                {'file': 'main.py', 'description': '主程序入口'}
            ]
        
        return structure
    
    def _generate_contributing_guide(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成贡献指南"""
        return {
            'welcome_message': '欢迎为本项目贡献代码！',
            'guidelines': [
                'Fork项目到您的GitHub账户',
                '创建新的功能分支',
                '编写清晰的提交信息',
                '添加相应的测试用例',
                '确保代码通过所有测试',
                '创建Pull Request'
            ],
            'code_style': '请遵循项目的代码风格规范',
            'testing': '请为新功能添加相应的测试',
            'documentation': '请更新相关文档'
        }
    
    def _generate_roadmap(self, analysis_result) -> List[Dict[str, str]]:
        """生成项目路线图"""
        roadmap = [
            {'version': 'v1.0.0', 'status': 'completed', 'description': '核心功能实现'},
            {'version': 'v1.1.0', 'status': 'in_progress', 'description': '功能优化和bug修复'},
            {'version': 'v2.0.0', 'status': 'planned', 'description': '重大功能更新'}
        ]
        
        # 如果有版本信息，调整路线图
        if analysis_result and analysis_result.version:
            current_version = analysis_result.version
            roadmap[0]['version'] = current_version
            roadmap[0]['status'] = 'completed'
        
        return roadmap
    
    def _generate_faq(self, analysis_result) -> List[Dict[str, str]]:
        """生成常见问题"""
        faq = [
            {
                'question': '如何报告bug？',
                'answer': '请在GitHub Issues中提交bug报告，包含详细的复现步骤。'
            },
            {
                'question': '如何请求新功能？',
                'answer': '请在GitHub Issues中提交功能请求，详细描述需求和使用场景。'
            },
            {
                'question': '项目支持哪些版本？',
                'answer': '请查看项目的requirements.txt或package.json了解版本要求。'
            }
        ]
        
        # 根据项目类型添加特定FAQ
        if analysis_result:
            project_type = analysis_result.project_type.value.lower()
            if project_type == 'python':
                faq.append({
                    'question': '如何处理依赖冲突？',
                    'answer': '建议使用虚拟环境隔离项目依赖，或使用poetry管理依赖。'
                })
        
        return faq
    
    def _merge_ai_content(self, base_content: str, ai_content: str) -> str:
        """合并AI增强内容"""
        # 简单的内容合并策略
        if ai_content and len(ai_content.strip()) > len(base_content.strip()):
            return ai_content
        return base_content
    
    def get_document_sections(self) -> List[Dict[str, str]]:
        """获取文档章节结构"""
        return [
            {'name': '项目简介', 'description': '项目概述和特性介绍'},
            {'name': '快速开始', 'description': '安装和使用指南'},
            {'name': '功能特性', 'description': '详细功能说明'},
            {'name': 'API文档', 'description': 'API接口使用说明'},
            {'name': '项目结构', 'description': '代码结构和组织'},
            {'name': '贡献指南', 'description': '如何参与项目开发'},
            {'name': '路线图', 'description': '项目发展规划'},
            {'name': '常见问题', 'description': '常见问题解答'}
        ]