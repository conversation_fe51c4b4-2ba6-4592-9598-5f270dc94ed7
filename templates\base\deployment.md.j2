# {{ project_name }} - 部署文档

## 概览

本文档描述了 {{ project_name }} 的部署配置、环境要求和操作流程。

## 部署架构

### 系统架构图

```
{{ deployment_architecture if deployment_architecture else "
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   负载均衡器    │    │   应用服务器    │    │   数据库服务器  │
│  Load Balancer  │───▶│  App Server     │───▶│  Database       │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN/静态资源  │    │   缓存服务器    │    │   备份存储      │
│   Static Files  │    │   Redis/Cache   │    │   Backup        │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
" }}
```

## 环境要求

### 硬件要求

{% if hardware_requirements %}
{% for req in hardware_requirements %}
#### {{ req.environment }}环境

- **CPU**: {{ req.cpu }}
- **内存**: {{ req.memory }}
- **存储**: {{ req.storage }}
- **网络**: {{ req.network }}
{% if req.notes %}
- **备注**: {{ req.notes }}
{% endif %}

{% endfor %}
{% else %}
#### 最小配置

- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 100Mbps

#### 推荐配置

- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps

#### 生产环境

- **CPU**: 8核心
- **内存**: 16GB RAM
- **存储**: 100GB SSD + 数据盘
- **网络**: 10Gbps
{% endif %}

### 软件要求

{% if software_requirements %}
{% for req in software_requirements %}
- **{{ req.name }}**: {{ req.version }} - {{ req.description }}
{% endfor %}
{% else %}
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8 / RHEL 8
{% if project_type == "python" %}
- **Python**: {{ python_version if python_version else "3.8+" }}
- **Web服务器**: Nginx 1.18+ / Apache 2.4+
- **WSGI服务器**: Gunicorn / uWSGI
{% elif project_type == "javascript" or project_type == "typescript" %}
- **Node.js**: {{ node_version if node_version else "16.0+" }}
- **Process Manager**: PM2
- **Web服务器**: Nginx 1.18+
{% elif project_type == "java" %}
- **JDK**: {{ java_version if java_version else "11+" }}
- **应用服务器**: Tomcat 9+ / Spring Boot内置
- **Web服务器**: Nginx 1.18+ / Apache 2.4+
{% endif %}
- **数据库**: {{ database_type if database_type else "PostgreSQL 12+ / MySQL 8.0+" }}
- **缓存**: Redis 6.0+
- **监控**: Prometheus + Grafana (可选)
{% endif %}

## Docker 部署

### Dockerfile

{% if dockerfile_content %}
```dockerfile
{{ dockerfile_content }}
```
{% else %}
```dockerfile
{% if project_type == "python" %}
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "app:app"]
{% elif project_type == "javascript" or project_type == "typescript" %}
FROM node:16-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "start"]
{% elif project_type == "java" %}
FROM openjdk:11-jre-slim

WORKDIR /app

# 复制jar文件
COPY target/{{ project_name }}-*.jar app.jar

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["java", "-jar", "app.jar"]
{% endif %}
```
{% endif %}

### Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "{{ app_port if app_port else '8000' }}:{{ app_port if app_port else '8000' }}"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/{{ project_name }}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB={{ project_name }}
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 构建和运行

```bash
# 构建镜像
docker build -t {{ project_name }}:latest .

# 使用docker-compose启动
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

## 传统服务器部署

### 环境准备

#### 1. 系统更新

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 2. 安装基础软件

```bash
{% if project_type == "python" %}
# 安装Python和pip
sudo apt install python3 python3-pip python3-venv -y

# 安装数据库
sudo apt install postgresql postgresql-contrib -y

# 安装Redis
sudo apt install redis-server -y

# 安装Nginx
sudo apt install nginx -y
{% elif project_type == "javascript" or project_type == "typescript" %}
# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install nodejs -y

# 安装PM2
sudo npm install -g pm2

# 安装数据库和其他服务
sudo apt install postgresql postgresql-contrib redis-server nginx -y
{% elif project_type == "java" %}
# 安装OpenJDK
sudo apt install openjdk-11-jdk -y

# 安装数据库和其他服务
sudo apt install postgresql postgresql-contrib redis-server nginx -y
{% endif %}
```

### 应用部署

#### 1. 创建应用用户

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash {{ project_name }}
sudo usermod -aG sudo {{ project_name }}

# 切换到应用用户
sudo su - {{ project_name }}
```

#### 2. 部署应用代码

```bash
# 克隆代码
git clone {{ repository_url if repository_url else "https://github.com/your-org/" + project_name + ".git" }}
cd {{ project_name }}

{% if project_type == "python" %}
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 收集静态文件
python manage.py collectstatic --noinput
{% elif project_type == "javascript" or project_type == "typescript" %}
# 安装依赖
npm ci --only=production

# 构建应用
npm run build
{% elif project_type == "java" %}
# 构建应用
mvn clean package -DskipTests
{% endif %}
```

#### 3. 配置系统服务

{% if project_type == "python" %}
创建 `/etc/systemd/system/{{ project_name }}.service`:

```ini
[Unit]
Description={{ project_name }} Application
After=network.target

[Service]
Type=exec
User={{ project_name }}
Group={{ project_name }}
WorkingDirectory=/home/<USER>/{{ project_name }}
Environment=PATH=/home/<USER>/{{ project_name }}/venv/bin
ExecStart=/home/<USER>/{{ project_name }}/venv/bin/gunicorn --bind 127.0.0.1:8000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```
{% elif project_type == "javascript" or project_type == "typescript" %}
创建 PM2 配置文件 `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: '{{ project_name }}',
    script: './dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log'
  }]
};
```

启动应用:
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```
{% elif project_type == "java" %}
创建 `/etc/systemd/system/{{ project_name }}.service`:

```ini
[Unit]
Description={{ project_name }} Application
After=network.target

[Service]
Type=exec
User={{ project_name }}
Group={{ project_name }}
WorkingDirectory=/home/<USER>/{{ project_name }}
ExecStart=/usr/bin/java -jar target/{{ project_name }}-1.0.0.jar
Restart=always

[Install]
WantedBy=multi-user.target
```
{% endif %}

启动服务:
```bash
sudo systemctl enable {{ project_name }}
sudo systemctl start {{ project_name }}
sudo systemctl status {{ project_name }}
```

### Nginx 配置

创建 `/etc/nginx/sites-available/{{ project_name }}`:

```nginx
server {
    listen 80;
    server_name {{ domain_name if domain_name else "your-domain.com" }};

    location / {
{% if project_type == "python" %}
        proxy_pass http://127.0.0.1:8000;
{% elif project_type == "javascript" or project_type == "typescript" %}
        proxy_pass http://127.0.0.1:3000;
{% elif project_type == "java" %}
        proxy_pass http://127.0.0.1:8080;
{% endif %}
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/{{ project_name }}/static/;
        expires 30d;
    }

    location /media/ {
        alias /home/<USER>/{{ project_name }}/media/;
        expires 30d;
    }
}
```

启用配置:
```bash
sudo ln -s /etc/nginx/sites-available/{{ project_name }} /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL/HTTPS 配置

### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d {{ domain_name if domain_name else "your-domain.com" }}

# 自动续期
sudo crontab -e
# 添加以下行:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动SSL配置

如果使用自有证书，更新Nginx配置:

```nginx
server {
    listen 443 ssl http2;
    server_name {{ domain_name if domain_name else "your-domain.com" }};

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # ... 其他配置
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name {{ domain_name if domain_name else "your-domain.com" }};
    return 301 https://$server_name$request_uri;
}
```

## 数据库配置

### PostgreSQL 设置

```bash
# 切换到postgres用户
sudo su - postgres

# 创建数据库和用户
createdb {{ project_name }}
createuser --interactive {{ project_name }}_user

# 设置密码和权限
psql -c "ALTER USER {{ project_name }}_user PASSWORD 'your_password';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE {{ project_name }} TO {{ project_name }}_user;"
```

### 数据库备份

```bash
#!/bin/bash
# 备份脚本 backup.sh

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="{{ project_name }}"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete

echo "数据库备份完成: $BACKUP_DIR/db_backup_$DATE.sql"
```

添加到定时任务:
```bash
# 每日2点执行备份
0 2 * * * /home/<USER>/backup.sh
```

## 监控和日志

### 应用监控

{% if monitoring_config %}
{{ monitoring_config }}
{% else %}
使用 Prometheus + Grafana 进行监控:

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: '{{ project_name }}'
    static_configs:
      - targets: ['localhost:{{ metrics_port if metrics_port else '9090' }}']
```
{% endif %}

### 日志管理

{% if project_type == "python" %}
配置日志轮转 `/etc/logrotate.d/{{ project_name }}`:

```
/home/<USER>/{{ project_name }}/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 0644 {{ project_name }} {{ project_name }}
    postrotate
        systemctl reload {{ project_name }}
    endscript
}
```
{% elif project_type == "javascript" or project_type == "typescript" %}
PM2 日志管理:

```bash
# 查看日志
pm2 logs {{ project_name }}

# 日志轮转
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
```
{% endif %}

## 性能优化

### 应用优化

{% if performance_optimizations %}
{% for opt in performance_optimizations %}
#### {{ opt.category }}
{{ opt.description }}

配置示例:
```
{{ opt.config }}
```
{% endfor %}
{% else %}
- **数据库连接池**: 配置合适的连接池大小
- **缓存策略**: 使用Redis缓存热点数据
- **静态资源**: 启用Gzip压缩和浏览器缓存
- **负载均衡**: 多实例部署和请求分发
{% endif %}

### Nginx 优化

```nginx
# nginx.conf 优化配置
worker_processes auto;
worker_connections 1024;

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript;
    
    client_max_body_size 10M;
}
```

## 故障排查

### 常见问题

{% if troubleshooting %}
{% for issue in troubleshooting %}
#### {{ issue.problem }}

**现象**: {{ issue.symptoms }}

**可能原因**: {{ issue.causes }}

**解决方案**: {{ issue.solutions }}

{% endfor %}
{% else %}
#### 应用无法启动

**检查步骤**:
1. 检查服务状态: `systemctl status {{ project_name }}`
2. 查看应用日志: `journalctl -u {{ project_name }} -f`
3. 检查端口占用: `netstat -tlnp | grep :{{ app_port if app_port else '8000' }}`
4. 验证配置文件: 检查环境变量和配置文件

#### 数据库连接问题

**检查步骤**:
1. 数据库服务状态: `systemctl status postgresql`
2. 连接测试: `psql -h localhost -U user -d {{ project_name }}`
3. 检查防火墙设置
4. 验证数据库权限配置

#### 性能问题

**检查步骤**:
1. 系统资源使用: `top`, `htop`, `iotop`
2. 数据库性能: 慢查询日志分析
3. 网络延迟: `ping`, `traceroute`
4. 应用性能分析: APM工具监控
{% endif %}

### 日志分析

```bash
# 查看应用日志
sudo journalctl -u {{ project_name }} -f

# 查看Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 搜索特定错误
sudo grep -i "error" /var/log/nginx/error.log
```

## 安全配置

### 防火墙设置

```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow 22

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 检查状态
sudo ufw status
```

### 系统加固

- 定期系统更新
- 禁用不必要的服务
- 配置fail2ban防暴力破解
- 使用密钥认证替代密码登录
- 定期备份重要数据

---

*此文档由 GenDocs 自动生成于 {{ generation_date }}*