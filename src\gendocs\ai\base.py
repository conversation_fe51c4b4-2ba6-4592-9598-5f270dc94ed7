"""
AI服务提供商抽象基类

定义统一的AI接口规范。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class AIGenerationError(Exception):
    """AI生成错误"""
    pass


@dataclass
class AIRequest:
    """AI请求数据结构"""
    prompt: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    system_prompt: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


@dataclass 
class AIResponse:
    """AI响应数据结构"""
    content: str
    model: str
    tokens_used: Optional[int] = None
    finish_reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class AIProvider(ABC):
    """AI服务提供商抽象基类
    
    所有AI提供商实现都必须继承此类并实现相应方法。
    """
    
    def __init__(self, config):
        """初始化AI提供商
        
        Args:
            config: AI配置对象
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def generate_completion(self, request: AIRequest) -> AIResponse:
        """生成文本补全
        
        Args:
            request: AI请求对象
            
        Returns:
            AI响应对象
            
        Raises:
            AIGenerationError: 生成失败时抛出
        """
        pass
    
    @abstractmethod
    async def generate_structured(self, request: AIRequest, schema: Dict) -> Dict[str, Any]:
        """生成结构化输出
        
        Args:
            request: AI请求对象
            schema: 期望的输出结构schema
            
        Returns:
            结构化数据字典
            
        Raises:
            AIGenerationError: 生成失败时抛出
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> List[str]:
        """验证配置有效性
        
        Returns:
            配置问题列表，空列表表示配置正确
        """
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接是否正常
        
        Returns:
            连接是否成功
        """
        pass
    
    def _build_messages(self, request: AIRequest) -> List[Dict[str, str]]:
        """构建消息列表（OpenAI格式）
        
        Args:
            request: AI请求对象
            
        Returns:
            消息列表
        """
        messages = []
        
        # 添加系统提示词
        if request.system_prompt:
            messages.append({
                "role": "system",
                "content": request.system_prompt
            })
        
        # 添加用户提示词
        messages.append({
            "role": "user", 
            "content": request.prompt
        })
        
        return messages
    
    def _get_generation_params(self, request: AIRequest) -> Dict[str, Any]:
        """获取生成参数
        
        Args:
            request: AI请求对象
            
        Returns:
            生成参数字典
        """
        params = {
            "model": self.config.model,
            "max_tokens": request.max_tokens or self.config.max_tokens,
            "temperature": request.temperature or self.config.temperature
        }
        
        return params
    
    async def _retry_on_failure(self, func, *args, **kwargs):
        """失败重试装饰器
        
        Args:
            func: 要重试的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            AIGenerationError: 重试次数用尽后抛出
        """
        last_error = None
        
        for attempt in range(self.config.retry_attempts + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_error = e
                if attempt < self.config.retry_attempts:
                    self.logger.warning(f"第{attempt + 1}次尝试失败，正在重试: {e}")
                    # 可以添加指数退避延迟
                    import asyncio
                    await asyncio.sleep(2 ** attempt)
                else:
                    break
        
        raise AIGenerationError(f"AI生成失败，已重试{self.config.retry_attempts}次: {last_error}")


class BaseAIProvider(AIProvider):
    """基础AI提供商实现
    
    提供通用功能的默认实现。
    """
    
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        issues = []
        
        if not self.config.api_key:
            issues.append("API密钥未设置")
        
        if not self.config.base_url:
            issues.append("API端点未设置")
        
        if not self.config.model:
            issues.append("模型名称未设置")
        
        return issues
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            test_request = AIRequest(
                prompt="Hello",
                max_tokens=10,
                temperature=0.1
            )
            
            response = await self.generate_completion(test_request)
            return bool(response.content)
            
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False