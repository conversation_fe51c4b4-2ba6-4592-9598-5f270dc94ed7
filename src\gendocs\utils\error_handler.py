"""
错误处理模块

提供统一的错误处理、异常定义和错误恢复机制。
"""

import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable, Type
from dataclasses import dataclass
from enum import Enum
import json


class ErrorSeverity(Enum):
    """错误严重级别"""
    LOW = "low"          # 轻微错误，不影响主要功能
    MEDIUM = "medium"    # 中等错误，影响部分功能
    HIGH = "high"        # 严重错误，影响主要功能
    CRITICAL = "critical" # 关键错误，系统无法继续运行


@dataclass
class ErrorContext:
    """错误上下文信息"""
    operation: str                    # 操作名称
    component: str                   # 组件名称
    parameters: Dict[str, Any]       # 操作参数
    timestamp: datetime              # 错误发生时间
    user_id: Optional[str] = None    # 用户ID
    session_id: Optional[str] = None # 会话ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'operation': self.operation,
            'component': self.component,
            'parameters': self.parameters,
            'timestamp': self.timestamp.isoformat(),
            'user_id': self.user_id,
            'session_id': self.session_id
        }


class GenDocsError(Exception):
    """GenDocs基础异常类"""
    
    def __init__(self, 
                 message: str,
                 error_code: str = "GENDOCS_ERROR",
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[ErrorContext] = None,
                 cause: Optional[Exception] = None):
        """初始化GenDocs异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            severity: 错误严重级别
            context: 错误上下文
            cause: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context
        self.cause = cause
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context.to_dict() if self.context else None,
            'cause': str(self.cause) if self.cause else None,
            'traceback': traceback.format_exc() if self.cause else None
        }


class ConfigurationError(GenDocsError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = "", **kwargs):
        super().__init__(
            message=message,
            error_code="CONFIG_ERROR",
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.config_key = config_key


class GenerationError(GenDocsError):
    """文档生成错误"""
    
    def __init__(self, message: str, generator_name: str = "", **kwargs):
        super().__init__(
            message=message,
            error_code="GENERATION_ERROR",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.generator_name = generator_name


class ValidationError(GenDocsError):
    """验证错误"""
    
    def __init__(self, message: str, field_name: str = "", **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            severity=ErrorSeverity.LOW,
            **kwargs
        )
        self.field_name = field_name


class AIError(GenDocsError):
    """AI相关错误"""
    
    def __init__(self, message: str, provider: str = "", **kwargs):
        super().__init__(
            message=message,
            error_code="AI_ERROR",
            severity=ErrorSeverity.MEDIUM,
            **kwargs
        )
        self.provider = provider


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, log_errors: bool = True, error_log_file: Optional[Path] = None):
        """初始化错误处理器
        
        Args:
            log_errors: 是否记录错误日志
            error_log_file: 错误日志文件路径
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.log_errors = log_errors
        self.error_log_file = error_log_file
        
        # 错误统计
        self._error_counts: Dict[str, int] = {}
        self._error_history: List[Dict[str, Any]] = []
        
        # 错误处理器映射
        self._error_handlers: Dict[Type[Exception], Callable] = {}
        
        # 恢复策略
        self._recovery_strategies: Dict[str, Callable] = {}
        
        # 设置默认错误处理器
        self._setup_default_handlers()
    
    def _setup_default_handlers(self) -> None:
        """设置默认错误处理器"""
        self.register_error_handler(ConfigurationError, self._handle_configuration_error)
        self.register_error_handler(GenerationError, self._handle_generation_error)
        self.register_error_handler(ValidationError, self._handle_validation_error)
        self.register_error_handler(AIError, self._handle_ai_error)
    
    def register_error_handler(self, 
                             exception_type: Type[Exception], 
                             handler: Callable[[Exception], Any]) -> None:
        """注册错误处理器
        
        Args:
            exception_type: 异常类型
            handler: 处理函数
        """
        self._error_handlers[exception_type] = handler
        self.logger.debug(f"已注册错误处理器: {exception_type.__name__}")
    
    def register_recovery_strategy(self, 
                                 error_code: str, 
                                 strategy: Callable[[], Any]) -> None:
        """注册错误恢复策略
        
        Args:
            error_code: 错误代码
            strategy: 恢复策略函数
        """
        self._recovery_strategies[error_code] = strategy
        self.logger.debug(f"已注册恢复策略: {error_code}")
    
    def handle_error(self, 
                    error: Exception, 
                    context: Optional[ErrorContext] = None,
                    attempt_recovery: bool = True) -> Optional[Any]:
        """处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            attempt_recovery: 是否尝试错误恢复
            
        Returns:
            处理结果或恢复结果
        """
        # 统计错误
        error_type = type(error).__name__
        self._error_counts[error_type] = self._error_counts.get(error_type, 0) + 1
        
        # 创建错误记录
        error_record = {
            'error_type': error_type,
            'message': str(error),
            'timestamp': datetime.now().isoformat(),
            'context': context.to_dict() if context else None,
            'traceback': traceback.format_exc()
        }
        
        # 特殊处理GenDocs错误
        if isinstance(error, GenDocsError):
            error_record.update(error.to_dict())
        
        self._error_history.append(error_record)
        
        # 记录错误日志
        if self.log_errors:
            self._log_error(error, error_record)
        
        # 查找特定错误处理器
        handler = self._find_error_handler(type(error))
        if handler:
            try:
                result = handler(error)
                self.logger.info(f"错误已被处理器处理: {error_type}")
                return result
            except Exception as handler_error:
                self.logger.error(f"错误处理器执行失败: {handler_error}")
        
        # 尝试错误恢复
        if attempt_recovery and isinstance(error, GenDocsError):
            recovery_result = self._attempt_recovery(error)
            if recovery_result is not None:
                return recovery_result
        
        # 根据错误严重级别决定是否重新抛出
        if isinstance(error, GenDocsError):
            if error.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                raise error
        
        return None
    
    def _find_error_handler(self, exception_type: Type[Exception]) -> Optional[Callable]:
        """查找错误处理器"""
        # 直接匹配
        if exception_type in self._error_handlers:
            return self._error_handlers[exception_type]
        
        # 基类匹配
        for error_type, handler in self._error_handlers.items():
            if issubclass(exception_type, error_type):
                return handler
        
        return None
    
    def _attempt_recovery(self, error: GenDocsError) -> Optional[Any]:
        """尝试错误恢复
        
        Args:
            error: GenDocs错误
            
        Returns:
            恢复结果
        """
        strategy = self._recovery_strategies.get(error.error_code)
        if strategy:
            try:
                self.logger.info(f"尝试恢复错误: {error.error_code}")
                result = strategy()
                self.logger.info(f"错误恢复成功: {error.error_code}")
                return result
            except Exception as recovery_error:
                self.logger.error(f"错误恢复失败: {recovery_error}")
        
        return None
    
    def _log_error(self, error: Exception, error_record: Dict[str, Any]) -> None:
        """记录错误日志"""
        # 控制台日志
        if isinstance(error, GenDocsError):
            log_level = self._get_log_level(error.severity)
            self.logger.log(log_level, f"GenDocs错误: {error.message}")
        else:
            self.logger.error(f"未处理异常: {error}")
        
        # 文件日志
        if self.error_log_file:
            try:
                self.error_log_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(self.error_log_file, 'a', encoding='utf-8') as f:
                    json.dump(error_record, f, ensure_ascii=False)
                    f.write('\n')
                    
            except Exception as log_error:
                self.logger.error(f"写入错误日志文件失败: {log_error}")
    
    def _get_log_level(self, severity: ErrorSeverity) -> int:
        """根据错误严重级别获取日志级别"""
        mapping = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        return mapping.get(severity, logging.ERROR)
    
    def _handle_configuration_error(self, error: ConfigurationError) -> None:
        """处理配置错误"""
        self.logger.error(f"配置错误: {error.message}")
        if error.config_key:
            self.logger.error(f"相关配置项: {error.config_key}")
        
        # 提供配置建议
        self.logger.info("建议: 请检查配置文件或运行 'gendocs config init' 初始化配置")
    
    def _handle_generation_error(self, error: GenerationError) -> None:
        """处理文档生成错误"""
        self.logger.error(f"文档生成错误: {error.message}")
        if error.generator_name:
            self.logger.error(f"相关生成器: {error.generator_name}")
        
        # 提供生成建议
        self.logger.info("建议: 请检查项目结构或尝试使用不同的生成器")
    
    def _handle_validation_error(self, error: ValidationError) -> None:
        """处理验证错误"""
        self.logger.warning(f"验证错误: {error.message}")
        if error.field_name:
            self.logger.warning(f"相关字段: {error.field_name}")
    
    def _handle_ai_error(self, error: AIError) -> None:
        """处理AI错误"""
        self.logger.error(f"AI服务错误: {error.message}")
        if error.provider:
            self.logger.error(f"AI提供商: {error.provider}")
        
        # 提供AI配置建议
        self.logger.info("建议: 请检查AI配置、API密钥或网络连接")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息
        
        Returns:
            错误统计数据
        """
        return {
            'error_counts': dict(self._error_counts),
            'total_errors': len(self._error_history),
            'recent_errors': self._error_history[-10:],  # 最近10个错误
            'most_frequent_errors': sorted(
                self._error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]  # 最频繁的5种错误
        }
    
    def clear_error_history(self) -> None:
        """清除错误历史"""
        self._error_history.clear()
        self._error_counts.clear()
        self.logger.info("错误历史已清除")
    
    def export_error_report(self, output_file: Path) -> None:
        """导出错误报告
        
        Args:
            output_file: 输出文件路径
        """
        try:
            report_data = {
                'export_time': datetime.now().isoformat(),
                'statistics': self.get_error_statistics(),
                'error_history': self._error_history
            }
            
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"错误报告已导出: {output_file}")
            
        except Exception as e:
            self.logger.error(f"导出错误报告失败: {e}")


# 全局错误处理器实例
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例
    
    Returns:
        错误处理器实例
    """
    global _global_error_handler
    if _global_error_handler is None:
        error_log_file = Path(".gendocs/logs/errors.jsonl")
        _global_error_handler = ErrorHandler(error_log_file=error_log_file)
    return _global_error_handler


def handle_error(error: Exception, 
                context: Optional[ErrorContext] = None,
                attempt_recovery: bool = True) -> Optional[Any]:
    """全局错误处理函数
    
    Args:
        error: 异常对象
        context: 错误上下文
        attempt_recovery: 是否尝试错误恢复
        
    Returns:
        处理结果
    """
    return get_error_handler().handle_error(error, context, attempt_recovery)


def safe_execute(func: Callable, 
                *args, 
                context: Optional[ErrorContext] = None,
                default_return: Any = None,
                **kwargs) -> Any:
    """安全执行函数，自动处理异常
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        context: 错误上下文
        default_return: 默认返回值
        **kwargs: 关键字参数
        
    Returns:
        函数执行结果或默认返回值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handle_error(e, context)
        return default_return