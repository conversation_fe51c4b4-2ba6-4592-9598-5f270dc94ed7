"""
工具函数模块
"""

import os
import re
import sys
import ast
import json
import shutil
import logging
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor

# 配置日志记录器
logger = logging.getLogger(__name__)

class PathUtils:
    """路径处理工具类"""
    
    @staticmethod
    @lru_cache(maxsize=1024)
    def normalize_path(path: Union[str, Path]) -> Path:
        """标准化路径
        
        将字符串或Path对象转换为绝对路径的Path对象
        使用缓存避免重复计算
        
        Args:
            path: 路径字符串或Path对象
            
        Returns:
            标准化后的Path对象
        """
        if isinstance(path, str):
            path = Path(path)
        return path.absolute()
        
    @staticmethod
    def is_subpath(path: Path, parent: Path) -> bool:
        """检查路径是否是另一个路径的子路径
        
        Args:
            path: 要检查的路径
            parent: 父路径
            
        Returns:
            是否是子路径
        """
        try:
            path.relative_to(parent)
            return True
        except ValueError:
            return False
            
    @staticmethod
    def get_relative_path(path: Path, base: Path) -> Path:
        """获取相对路径
        
        如果无法获取相对路径，则返回绝对路径
        
        Args:
            path: 目标路径
            base: 基准路径
            
        Returns:
            相对路径
        """
        try:
            return path.relative_to(base)
        except ValueError:
            return path.absolute()
            
class FileUtils:
    """文件处理工具类"""
    
    # 文件大小阈值（字节）
    LARGE_FILE_THRESHOLD = 10 * 1024 * 1024  # 10MB
    
    # 文本文件扩展名
    TEXT_EXTENSIONS = {
        '.txt', '.md', '.rst', '.json', '.yaml', '.yml',
        '.py', '.js', '.ts', '.jsx', '.tsx', '.java',
        '.c', '.cpp', '.h', '.hpp', '.cs', '.go',
        '.html', '.css', '.scss', '.less', '.xml',
        '.conf', '.cfg', '.ini', '.properties'
    }
    
    @staticmethod
    def is_text_file(path: Path) -> bool:
        """检查是否是文本文件
        
        Args:
            path: 文件路径
            
        Returns:
            是否是文本文件
        """
        if not path.is_file():
            return False
            
        # 检查扩展名
        if path.suffix.lower() in FileUtils.TEXT_EXTENSIONS:
            return True
            
        # 尝试读取文件头部检查是否是二进制文件
        try:
            with open(path, 'rb') as f:
                chunk = f.read(1024)
                return not bool(chunk.translate(None, bytearray({7,8,9,10,12,13,27} | set(range(0x20, 0x100)))))
        except Exception:
            return False
            
    @staticmethod
    def safe_read_file(path: Path, encoding: str = 'utf-8') -> Optional[str]:
        """安全地读取文件内容
        
        处理编码错误和大文件
        
        Args:
            path: 文件路径
            encoding: 文件编码
            
        Returns:
            文件内容，如果读取失败则返回None
        """
        if not path.is_file():
            return None
            
        try:
            # 检查文件大小
            size = path.stat().st_size
            
            # 大文件使用内存映射
            if size > FileUtils.LARGE_FILE_THRESHOLD:
                import mmap
                with open(path, 'rb') as f:
                    with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                        return mm.read().decode(encoding)
            
            # 小文件直接读取
            with open(path, 'r', encoding=encoding) as f:
                return f.read()
                
        except UnicodeDecodeError:
            # 尝试其他编码
            for enc in ['utf-8-sig', 'gbk', 'latin1']:
                try:
                    with open(path, 'r', encoding=enc) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
                    
        except Exception as e:
            logger.warning(f"读取文件 {path} 失败: {str(e)}")
            
        return None
        
    @staticmethod
    def safe_write_file(path: Path, content: str, encoding: str = 'utf-8') -> bool:
        """安全地写入文件内容
        
        自动创建目录，处理编码错误
        
        Args:
            path: 文件路径
            content: 文件内容
            encoding: 文件编码
            
        Returns:
            是否写入成功
        """
        try:
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
                
            return True
            
        except Exception as e:
            logger.error(f"写入文件 {path} 失败: {str(e)}")
            return False
            
class CodeUtils:
    """代码处理工具类"""
    
    @staticmethod
    @lru_cache(maxsize=1024)
    def get_docstring(content: str) -> Optional[str]:
        """获取代码的文档字符串
        
        使用缓存避免重复解析
        
        Args:
            content: 代码内容
            
        Returns:
            文档字符串，如果没有则返回None
        """
        try:
            tree = ast.parse(content, mode='exec')
            return ast.get_docstring(tree)
        except Exception:
            return None
            
    @staticmethod
    def extract_imports(content: str) -> List[Tuple[str, Set[str]]]:
        """提取代码中的导入语句
        
        Args:
            content: 代码内容
            
        Returns:
            导入语句列表，每项为(模块名, {导入项集合})
        """
        imports = []
        try:
            tree = ast.parse(content, mode='exec')
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append((name.name, {name.asname or name.name}))
                elif isinstance(node, ast.ImportFrom) and node.module:
                    names = {n.asname or n.name for n in node.names}
                    imports.append((node.module, names))
                    
        except Exception:
            pass
            
        return imports
        
    @staticmethod
    def format_code(content: str, style: str = 'black') -> Optional[str]:
        """格式化代码
        
        Args:
            content: 代码内容
            style: 格式化工具
            
        Returns:
            格式化后的代码，如果失败则返回None
        """
        try:
            if style == 'black':
                import black
                return black.format_str(content, mode=black.FileMode())
            elif style == 'autopep8':
                import autopep8
                return autopep8.fix_code(content)
            else:
                return content
        except Exception:
            return None
            
class ProcessUtils:
    """进程处理工具类"""
    
    @staticmethod
    def run_command(
        cmd: Union[str, List[str]],
        cwd: Optional[Union[str, Path]] = None,
        env: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = False
    ) -> Tuple[bool, str]:
        """运行命令
        
        Args:
            cmd: 命令字符串或列表
            cwd: 工作目录
            env: 环境变量
            timeout: 超时时间（秒）
            shell: 是否使用shell执行
            
        Returns:
            (是否成功, 输出信息)
        """
        try:
            # 准备命令
            if isinstance(cmd, str) and not shell:
                cmd = cmd.split()
                
            # 准备环境变量
            if env:
                env = {**os.environ, **env}
                
            # 执行命令
            result = subprocess.run(
                cmd,
                cwd=cwd,
                env=env,
                shell=shell,
                timeout=timeout,
                capture_output=True,
                text=True
            )
            
            # 检查结果
            if result.returncode == 0:
                return True, result.stdout
            else:
                return False, f"错误: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, f"命令执行超时: {timeout}秒"
        except Exception as e:
            return False, f"命令执行失败: {str(e)}"
            
    @staticmethod
    def is_tool_available(tool: str) -> bool:
        """检查工具是否可用
        
        Args:
            tool: 工具名称
            
        Returns:
            工具是否可用
        """
        if sys.platform == "win32":
            tool += ".exe"
            
        return shutil.which(tool) is not None

def run_command(
    cmd: Union[str, List[str]], 
    cwd: Optional[Union[str, Path]] = None,
    capture_output: bool = True,
    check: bool = True,
    shell: bool = False,
    env: Optional[dict] = None
) -> Tuple[int, str, str]:
    """运行命令并返回结果
    
    Args:
        cmd: 要运行的命令
        cwd: 工作目录
        capture_output: 是否捕获输出
        check: 是否检查返回码
        shell: 是否使用shell执行
        env: 环境变量
        
    Returns:
        (返回码, 标准输出, 标准错误)的元组
    
    Raises:
        subprocess.CalledProcessError: 如果check=True且命令返回非零
    """
    if isinstance(cmd, str) and not shell:
        cmd = cmd.split()
        
    # 合并环境变量
    if env:
        env = {**os.environ, **env}
    else:
        env = os.environ
        
    try:
        proc = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=capture_output,
            check=check,
            shell=shell,
            env=env,
            text=True
        )
        return proc.returncode, proc.stdout, proc.stderr
        
    except subprocess.CalledProcessError as e:
        if check:
            raise
        return e.returncode, e.stdout or "", e.stderr or ""
        
def ensure_dir(path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建
    
    Args:
        path: 目录路径
        
    Returns:
        目录路径对象
    """
    path = Path(path)
    path.mkdir(parents=True, exist_ok=True)
    return path
    
def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
    """复制文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
    """
    src = Path(src)
    dst = Path(dst)
    
    # 确保目标目录存在
    dst.parent.mkdir(parents=True, exist_ok=True)
    
    # 复制文件
    shutil.copy2(src, dst)
    
def get_python_version() -> Tuple[int, int]:
    """获取Python版本
    
    Returns:
        (主版本号, 次版本号)的元组
    """
    return sys.version_info[:2]
    
def get_package_version(package: str) -> Optional[str]:
    """获取已安装包的版本
    
    Args:
        package: 包名
        
    Returns:
        版本号，如果未安装则返回None
    """
    try:
        import pkg_resources
        return pkg_resources.get_distribution(package).version
    except Exception:
        return None 