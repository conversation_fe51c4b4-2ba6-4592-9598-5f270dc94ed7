"""
配置数据模型

定义所有配置项的数据结构和验证规则。
"""

import os
import re
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any
from enum import Enum


class AIProvider(str, Enum):
    """支持的AI服务提供商"""
    OPENAI = "openai"
    AZURE = "azure"
    DEEPSEEK = "deepseek"
    QWEN = "qwen"
    CLAUDE = "claude"
    CUSTOM = "custom"


class OutputFormat(str, Enum):
    """支持的输出格式"""
    MARKDOWN = "markdown"
    HTML = "html"
    PDF = "pdf"


class BackupFormat(str, Enum):
    """备份格式"""
    TIMESTAMP = "timestamp"
    INCREMENTAL = "incremental"
    NONE = "none"


@dataclass
class AIConfig:
    """AI服务配置"""
    provider: AIProvider = AIProvider.OPENAI
    base_url: str = "https://api.openai.com/v1"
    api_key: str = ""
    model: str = "gpt-4o-mini"
    max_tokens: int = 4000
    temperature: float = 0.3
    timeout: int = 60
    retry_attempts: int = 3
    
    def __post_init__(self):
        """配置后处理和验证"""
        # 处理环境变量替换
        self.api_key = self._resolve_env_var(self.api_key)
        self.base_url = self._resolve_env_var(self.base_url)
        self.model = self._resolve_env_var(self.model)
        
        # 验证配置
        self._validate()
    
    def _resolve_env_var(self, value: str) -> str:
        """解析环境变量"""
        if not value:
            return value
            
        # 支持 ${VAR_NAME} 格式
        pattern = r'\$\{([^}]+)\}'
        matches = re.findall(pattern, value)
        
        for var_name in matches:
            env_value = os.getenv(var_name, "")
            value = value.replace(f"${{{var_name}}}", env_value)
        
        return value
    
    def _validate(self):
        """验证配置有效性"""
        if self.temperature < 0 or self.temperature > 2:
            raise ValueError("temperature 必须在 0-2 之间")
        
        if self.max_tokens <= 0:
            raise ValueError("max_tokens 必须大于 0")
        
        if self.timeout <= 0:
            raise ValueError("timeout 必须大于 0")
        
        if self.retry_attempts < 0:
            raise ValueError("retry_attempts 必须大于等于 0")


@dataclass
class LanguageConfig:
    """编程语言支持配置"""
    enabled: List[str] = field(default_factory=lambda: ["python"])
    detection_order: List[str] = field(default_factory=lambda: ["python"])
    
    def __post_init__(self):
        """验证语言配置"""
        if not self.enabled:
            raise ValueError("至少需要启用一种编程语言")
        
        # 确保检测顺序中的语言都已启用
        for lang in self.detection_order:
            if lang not in self.enabled:
                raise ValueError(f"检测顺序中的语言 '{lang}' 未在启用列表中")


@dataclass
class GenerationConfig:
    """文档生成配置"""
    backup_existing: bool = True
    backup_format: BackupFormat = BackupFormat.TIMESTAMP
    concurrent_jobs: int = 4
    ai_enhanced_docs: List[str] = field(default_factory=lambda: [
        "user_story_map", "adr", "runbook"
    ])
    
    def __post_init__(self):
        """验证生成配置"""
        if self.concurrent_jobs <= 0:
            raise ValueError("concurrent_jobs 必须大于 0")
        
        if self.concurrent_jobs > 16:
            raise ValueError("concurrent_jobs 不建议超过 16")


@dataclass
class OutputConfig:
    """输出配置"""
    base_dir: str = "docs"
    formats: List[OutputFormat] = field(default_factory=lambda: [OutputFormat.MARKDOWN])
    include_metadata: bool = True
    
    def get_output_path(self, project_root: Path) -> Path:
        """获取输出目录路径"""
        return project_root / self.base_dir


@dataclass
class TemplateConfig:
    """模板配置"""
    base_path: str = "templates"
    custom_path: Optional[str] = None
    
    def __post_init__(self):
        """处理模板路径"""
        if self.custom_path:
            self.custom_path = self._resolve_env_var(self.custom_path)
    
    def _resolve_env_var(self, value: str) -> str:
        """解析环境变量"""
        if not value:
            return value
            
        pattern = r'\$\{([^}]+)\}'
        matches = re.findall(pattern, value)
        
        for var_name in matches:
            env_value = os.getenv(var_name, "")
            value = value.replace(f"${{{var_name}}}", env_value)
        
        return value
    
    def get_template_paths(self, package_root: Path) -> List[Path]:
        """获取模板搜索路径"""
        paths = []
        
        # 自定义模板路径优先
        if self.custom_path:
            custom_path = Path(self.custom_path)
            if custom_path.exists():
                paths.append(custom_path)
        
        # 默认模板路径
        default_path = package_root / self.base_path
        if default_path.exists():
            paths.append(default_path)
        
        return paths


@dataclass
class GenDocsConfig:
    """GenDocs 主配置类"""
    ai: AIConfig = field(default_factory=AIConfig)
    languages: LanguageConfig = field(default_factory=LanguageConfig)
    generation: GenerationConfig = field(default_factory=GenerationConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    templates: TemplateConfig = field(default_factory=TemplateConfig)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if hasattr(value, '__dict__'):
                result[key] = value.__dict__
            else:
                result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "GenDocsConfig":
        """从字典创建配置对象"""
        config = cls()
        
        if "ai" in data:
            config.ai = AIConfig(**data["ai"])
        
        if "languages" in data:
            config.languages = LanguageConfig(**data["languages"])
        
        if "generation" in data:
            generation_data = data["generation"].copy()
            # 处理枚举类型
            if "backup_format" in generation_data:
                generation_data["backup_format"] = BackupFormat(
                    generation_data["backup_format"]
                )
            config.generation = GenerationConfig(**generation_data)
        
        if "output" in data:
            output_data = data["output"].copy()
            # 处理枚举类型
            if "formats" in output_data:
                output_data["formats"] = [
                    OutputFormat(fmt) for fmt in output_data["formats"]
                ]
            config.output = OutputConfig(**output_data)
        
        if "templates" in data:
            config.templates = TemplateConfig(**data["templates"])
        
        return config