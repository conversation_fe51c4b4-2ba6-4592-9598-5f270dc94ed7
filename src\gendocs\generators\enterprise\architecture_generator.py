"""
架构文档生成器

生成系统架构文档，包括架构概述、组件图、数据流等。
"""

from pathlib import Path
from typing import Dict, Any, List

from .base import EnterpriseGenerator


class ArchitectureGenerator(EnterpriseGenerator):
    """架构文档生成器
    
    生成详细的系统架构文档。
    """
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/architecture.md.j2"
        self.output_filename = "ARCHITECTURE.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成架构文档
        
        Args:
            project_path: 项目路径
            output_path: 输出路径
            context: 上下文数据
            
        Returns:
            是否生成成功
        """
        try:
            self.logger.info(f"开始生成架构文档: {project_path}")
            
            # 构建企业级上下文
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            # 构建架构特定上下文
            arch_context = self._build_architecture_context(enterprise_context)
            
            # 渲染基础模板
            base_content = self.render_template(self.template_name, arch_context)
            
            # AI增强内容
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "architecture_enhancement",
                    **arch_context
                )
                if enhanced_content:
                    base_content = self._merge_ai_content(base_content, enhanced_content)
            
            # 写入文件
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            # 验证输出
            if self.validate_output(final_output_path):
                self.logger.info(f"架构文档生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成架构文档失败: {e}")
            return False
    
    def _build_architecture_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建架构特定上下文"""
        analysis_result = context.get('analysis_result')
        
        arch_context = context.copy()
        arch_context.update({
            'architecture_overview': self._build_architecture_overview(analysis_result),
            'system_components': self._analyze_system_components(analysis_result),
            'data_flow': self._analyze_data_flow(analysis_result),
            'technology_stack': self._build_technology_stack(analysis_result),
            'deployment_architecture': self._analyze_deployment_architecture(analysis_result),
            'security_considerations': self._analyze_security(analysis_result),
            'scalability_patterns': self._analyze_scalability(analysis_result),
            'design_decisions': self._extract_design_decisions(analysis_result)
        })
        
        return arch_context
    
    def _build_architecture_overview(self, analysis_result) -> Dict[str, Any]:
        """构建架构概述"""
        overview = {
            'pattern': '未知',
            'description': '系统采用模块化架构设计',
            'characteristics': [],
            'benefits': []
        }
        
        if not analysis_result or not analysis_result.architecture:
            return overview
        
        arch_info = analysis_result.architecture
        
        if arch_info.pattern:
            overview['pattern'] = arch_info.pattern
            
            # 根据架构模式添加说明
            if 'MVC' in arch_info.pattern:
                overview['description'] = '系统采用MVC（Model-View-Controller）架构模式'
                overview['characteristics'] = [
                    '关注点分离',
                    '代码组织清晰',
                    '易于维护和扩展'
                ]
                overview['benefits'] = [
                    '提高代码复用性',
                    '降低组件耦合度',
                    '便于团队协作开发'
                ]
            elif 'MTV' in arch_info.pattern:
                overview['description'] = '系统采用MTV（Model-Template-View）架构模式'
                overview['characteristics'] = [
                    'Django框架标准模式',
                    '数据、表示、逻辑分离',
                    '内置ORM支持'
                ]
            elif 'API-First' in arch_info.pattern:
                overview['description'] = '系统采用API优先架构设计'
                overview['characteristics'] = [
                    'RESTful API设计',
                    '前后端分离',
                    '服务化架构'
                ]
        
        return overview
    
    def _analyze_system_components(self, analysis_result) -> List[Dict[str, Any]]:
        """分析系统组件"""
        components = []
        
        if not analysis_result:
            return components
        
        # 从架构信息中提取组件
        if analysis_result.architecture and analysis_result.architecture.layers:
            for layer in analysis_result.architecture.layers:
                components.append({
                    'name': layer.get('name', '未知组件'),
                    'type': 'layer',
                    'description': layer.get('description', ''),
                    'responsibilities': self._get_layer_responsibilities(layer.get('name', '')),
                    'dependencies': []
                })
        
        # 从模块信息中提取组件
        if analysis_result.modules:
            for module in analysis_result.modules:
                components.append({
                    'name': module.name,
                    'type': 'module',
                    'description': module.description or f'{module.name}模块',
                    'file_count': module.file_count,
                    'line_count': module.line_count,
                    'path': module.path
                })
        
        # 从技术栈中提取外部组件
        if analysis_result.tech_stack:
            tech_stack = analysis_result.tech_stack
            
            if tech_stack.databases:
                for db in tech_stack.databases:
                    components.append({
                        'name': db,
                        'type': 'database',
                        'description': f'{db}数据库',
                        'role': 'data_storage'
                    })
            
            if tech_stack.frameworks:
                for framework in tech_stack.frameworks:
                    components.append({
                        'name': framework,
                        'type': 'framework',
                        'description': f'{framework}框架',
                        'role': 'application_framework'
                    })
        
        return components
    
    def _get_layer_responsibilities(self, layer_name: str) -> List[str]:
        """获取层级职责"""
        responsibilities_map = {
            'Models': ['数据模型定义', '业务逻辑封装', '数据库交互'],
            'Views': ['请求处理', '业务逻辑协调', '响应生成'],
            'Templates': ['用户界面渲染', '数据展示', '用户交互'],
            'Controllers': ['请求路由', '业务流程控制', '数据处理'],
            'API Routes': ['API端点定义', '请求验证', '响应格式化'],
            'Business Logic': ['核心业务逻辑', '数据处理', '规则验证'],
            'Data Access': ['数据库操作', '数据持久化', '查询优化']
        }
        
        return responsibilities_map.get(layer_name, ['处理相关业务逻辑'])
    
    def _analyze_data_flow(self, analysis_result) -> Dict[str, Any]:
        """分析数据流"""
        data_flow = {
            'description': '数据在系统中的流转过程',
            'flow_steps': [],
            'data_stores': [],
            'interfaces': []
        }
        
        if not analysis_result:
            return data_flow
        
        # 基于框架分析数据流
        if analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            
            if framework == 'django':
                data_flow['flow_steps'] = [
                    {'step': 1, 'description': 'HTTP请求到达Django服务器'},
                    {'step': 2, 'description': 'URL路由匹配对应的View'},
                    {'step': 3, 'description': 'View调用Model进行数据操作'},
                    {'step': 4, 'description': 'Model与数据库交互'},
                    {'step': 5, 'description': 'Template渲染响应内容'},
                    {'step': 6, 'description': 'HTTP响应返回给客户端'}
                ]
            elif framework == 'fastapi':
                data_flow['flow_steps'] = [
                    {'step': 1, 'description': 'HTTP请求到达FastAPI应用'},
                    {'step': 2, 'description': '路径操作函数处理请求'},
                    {'step': 3, 'description': '数据验证和序列化'},
                    {'step': 4, 'description': '业务逻辑处理'},
                    {'step': 5, 'description': '数据库操作（如需要）'},
                    {'step': 6, 'description': 'JSON响应返回'}
                ]
        
        # 从技术栈中提取数据存储
        if analysis_result.tech_stack and analysis_result.tech_stack.databases:
            for db in analysis_result.tech_stack.databases:
                data_flow['data_stores'].append({
                    'name': db,
                    'type': 'database',
                    'purpose': '持久化数据存储'
                })
        
        # 从API中提取接口
        if analysis_result.apis:
            for api in analysis_result.apis[:5]:  # 只取前5个API
                data_flow['interfaces'].append({
                    'name': f'{api.method} {api.path}',
                    'type': 'REST API',
                    'description': api.description or 'API接口'
                })
        
        return data_flow
    
    def _build_technology_stack(self, analysis_result) -> Dict[str, Any]:
        """构建技术栈详情"""
        tech_stack = {
            'frontend': [],
            'backend': [],
            'database': [],
            'infrastructure': [],
            'tools': []
        }
        
        if not analysis_result or not analysis_result.tech_stack:
            return tech_stack
        
        stack_info = analysis_result.tech_stack
        
        # 分类技术栈组件
        if stack_info.languages:
            tech_stack['backend'].extend(stack_info.languages)
        
        if stack_info.frameworks:
            tech_stack['backend'].extend(stack_info.frameworks)
        
        if stack_info.databases:
            tech_stack['database'].extend(stack_info.databases)
        
        if stack_info.tools:
            tech_stack['tools'].extend(stack_info.tools)
        
        if stack_info.runtime:
            tech_stack['infrastructure'].extend(stack_info.runtime)
        
        return tech_stack
    
    def _analyze_deployment_architecture(self, analysis_result) -> Dict[str, Any]:
        """分析部署架构"""
        deployment = {
            'strategy': '传统部署',
            'environments': ['开发环境', '测试环境', '生产环境'],
            'scaling': '垂直扩展',
            'monitoring': []
        }
        
        if not analysis_result:
            return deployment
        
        # 从部署配置中分析
        if analysis_result.deployment_config:
            deploy_config = analysis_result.deployment_config
            
            if deploy_config.get('docker', {}).get('detected'):
                deployment['strategy'] = 'Docker容器化部署'
                deployment['scaling'] = '水平扩展'
            
            if deploy_config.get('cloud', {}).get('detected'):
                deployment['strategy'] = '云平台部署'
                platforms = deploy_config.get('cloud', {}).get('platforms', [])
                if platforms:
                    deployment['platforms'] = platforms
            
            if deploy_config.get('ci_cd', {}).get('detected'):
                deployment['automation'] = 'CI/CD自动化部署'
        
        return deployment
    
    def _analyze_security(self, analysis_result) -> List[Dict[str, str]]:
        """分析安全考虑"""
        security = [
            {'aspect': '身份认证', 'description': '用户身份验证和授权机制'},
            {'aspect': '数据保护', 'description': '敏感数据加密和安全传输'},
            {'aspect': '输入验证', 'description': '用户输入验证和防注入攻击'},
            {'aspect': '访问控制', 'description': '基于角色的访问控制'}
        ]
        
        if analysis_result and analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            
            if framework == 'django':
                security.extend([
                    {'aspect': 'CSRF保护', 'description': 'Django内置CSRF攻击防护'},
                    {'aspect': 'SQL注入防护', 'description': 'ORM自动防护SQL注入'},
                ])
            elif framework == 'fastapi':
                security.extend([
                    {'aspect': 'JWT认证', 'description': '基于JWT的API认证'},
                    {'aspect': '数据验证', 'description': 'Pydantic自动数据验证'},
                ])
        
        return security
    
    def _analyze_scalability(self, analysis_result) -> List[Dict[str, str]]:
        """分析可扩展性模式"""
        patterns = [
            {'pattern': '模块化设计', 'description': '代码按功能模块组织，易于扩展'},
            {'pattern': '数据库优化', 'description': '数据库查询优化和索引设计'},
            {'pattern': '缓存策略', 'description': '合理使用缓存提升性能'}
        ]
        
        if analysis_result and analysis_result.tech_stack:
            tech_stack = analysis_result.tech_stack
            
            if 'Redis' in tech_stack.databases:
                patterns.append({
                    'pattern': 'Redis缓存',
                    'description': '使用Redis进行数据缓存和会话存储'
                })
            
            if 'Celery' in tech_stack.tools:
                patterns.append({
                    'pattern': '异步任务队列',
                    'description': '使用Celery处理后台任务，提升响应性能'
                })
        
        return patterns
    
    def _extract_design_decisions(self, analysis_result) -> List[Dict[str, str]]:
        """提取设计决策"""
        decisions = []
        
        if analysis_result:
            # 框架选择决策
            if analysis_result.framework:
                framework = analysis_result.framework.value
                decisions.append({
                    'decision': f'选择{framework}框架',
                    'rationale': self._get_framework_rationale(framework),
                    'alternatives': self._get_framework_alternatives(framework),
                    'impact': '影响项目的开发效率和维护性'
                })
            
            # 数据库选择决策
            if analysis_result.tech_stack and analysis_result.tech_stack.databases:
                for db in analysis_result.tech_stack.databases:
                    decisions.append({
                        'decision': f'选择{db}数据库',
                        'rationale': self._get_database_rationale(db),
                        'impact': '影响数据存储性能和扩展性'
                    })
        
        return decisions
    
    def _get_framework_rationale(self, framework: str) -> str:
        """获取框架选择理由"""
        rationale_map = {
            'Django': '功能完整，开发效率高，社区支持好',
            'Flask': '轻量级，灵活性强，学习成本低',
            'FastAPI': '高性能，现代化，自动文档生成',
            'React': '组件化开发，生态丰富，性能优秀',
            'Vue': '渐进式框架，学习曲线平缓，开发体验好'
        }
        return rationale_map.get(framework, '符合项目需求和团队技术栈')
    
    def _get_framework_alternatives(self, framework: str) -> str:
        """获取框架替代方案"""
        alternatives_map = {
            'Django': 'Flask, FastAPI',
            'Flask': 'Django, FastAPI',
            'FastAPI': 'Django, Flask',
            'React': 'Vue, Angular',
            'Vue': 'React, Angular'
        }
        return alternatives_map.get(framework, '其他同类框架')
    
    def _get_database_rationale(self, database: str) -> str:
        """获取数据库选择理由"""
        rationale_map = {
            'PostgreSQL': '功能强大，支持复杂查询，数据一致性好',
            'MySQL': '性能优秀，使用广泛，社区支持好',
            'Redis': '高性能缓存，支持多种数据结构',
            'MongoDB': '文档型数据库，模式灵活，扩展性强'
        }
        return rationale_map.get(database, '符合数据存储需求')
    
    def _merge_ai_content(self, base_content: str, ai_content: str) -> str:
        """合并AI增强内容"""
        if ai_content and len(ai_content.strip()) > len(base_content.strip()):
            return ai_content
        return base_content
    
    def get_document_sections(self) -> List[Dict[str, str]]:
        """获取文档章节结构"""
        return [
            {'name': '架构概述', 'description': '系统架构总体介绍'},
            {'name': '系统组件', 'description': '核心组件和模块说明'},
            {'name': '数据流', 'description': '数据在系统中的流转'},
            {'name': '技术栈', 'description': '使用的技术和工具'},
            {'name': '部署架构', 'description': '部署策略和环境配置'},
            {'name': '安全考虑', 'description': '安全设计和防护措施'},
            {'name': '扩展性', 'description': '系统扩展性设计'},
            {'name': '设计决策', 'description': '重要的架构设计决策'}
        ]