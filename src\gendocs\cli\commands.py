"""
CLI命令实现模块

实现各种CLI命令的具体逻辑。
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..config import ConfigManager
from ..config.templates import ConfigTemplateGenerator, ConfigTemplateType
from ..generators import EnterpriseDocumentManager
from ..analyzers.registry import get_registry
from ..backup import BackupManager
from ..batch import BatchProcessor, BatchConfig, ProjectConfig
from ..monitoring import PerformanceMonitor, get_performance_monitor


class BaseCommand(ABC):
    """命令基类"""
    
    def __init__(self, config_manager: ConfigManager):
        """初始化命令
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @staticmethod
    @abstractmethod
    def setup_parser(parser) -> None:
        """设置命令参数解析器
        
        Args:
            parser: argparse子解析器
        """
        pass
    
    @abstractmethod
    async def execute(self, args) -> int:
        """执行命令
        
        Args:
            args: 命令行参数
            
        Returns:
            退出码
        """
        pass


class GenerateCommand(BaseCommand):
    """文档生成命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置generate命令参数"""
        parser.add_argument(
            'project_path',
            type=Path,
            nargs='?',
            default=Path.cwd(),
            help='项目路径 (默认: 当前目录)'
        )
        
        parser.add_argument(
            '-o', '--output',
            type=Path,
            help='输出目录 (默认: 项目路径下的docs目录)'
        )
        
        parser.add_argument(
            '-g', '--generators',
            type=str,
            help='指定要运行的生成器，用逗号分隔 (例如: overview,api,architecture)'
        )
        
        parser.add_argument(
            '--list-generators',
            action='store_true',
            help='列出所有可用的生成器'
        )
        
        parser.add_argument(
            '--no-backup',
            action='store_true',
            help='跳过文档备份'
        )
        
        parser.add_argument(
            '--no-ai',
            action='store_true',
            help='禁用AI增强功能'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制覆盖现有文档'
        )
    
    async def execute(self, args) -> int:
        """执行文档生成"""
        try:
            # 列出生成器
            if args.list_generators:
                await self._list_generators()
                return 0
            
            # 验证项目路径
            project_path = args.project_path.resolve()
            if not project_path.exists():
                self.logger.error(f"项目路径不存在: {project_path}")
                return 1
            
            if not project_path.is_dir():
                self.logger.error(f"项目路径不是目录: {project_path}")
                return 1
            
            # 设置输出目录
            output_path = args.output or (project_path / 'docs')
            output_path = output_path.resolve()
            
            # 检查输出目录
            if output_path.exists() and not args.force:
                if output_path.is_file():
                    self.logger.error(f"输出路径是文件而非目录: {output_path}")
                    return 1
                
                if list(output_path.glob('*.md')) and not args.force:
                    response = input(f"输出目录 {output_path} 包含文档文件，是否继续? [y/N]: ")
                    if response.lower() not in ['y', 'yes']:
                        print("操作已取消")
                        return 0
            
            # 创建企业文档管理器
            doc_manager = EnterpriseDocumentManager(self.config_manager)
            
            # 禁用AI（如果指定）
            if args.no_ai:
                doc_manager.ai_provider = None
                self.logger.info("AI功能已禁用")
            
            # 解析指定的生成器
            selected_generators = None
            if args.generators:
                selected_generators = [g.strip() for g in args.generators.split(',')]
                self.logger.info(f"将运行指定的生成器: {', '.join(selected_generators)}")
            
            # 生成文档
            self.logger.info(f"开始生成文档...")
            self.logger.info(f"项目路径: {project_path}")
            self.logger.info(f"输出路径: {output_path}")
            
            results = await doc_manager.generate_all_documents(
                project_path=project_path,
                output_dir=output_path,
                selected_generators=selected_generators
            )
            
            # 显示结果
            self._display_results(results, output_path)
            
            # 检查是否有失败的生成器
            failed_generators = [name for name, success in results.items() if not success]
            if failed_generators:
                self.logger.warning(f"以下生成器执行失败: {', '.join(failed_generators)}")
                return 1
            
            return 0
            
        except Exception as e:
            self.logger.error(f"文档生成失败: {e}")
            return 1
    
    async def _list_generators(self) -> None:
        """列出所有可用的生成器"""
        doc_manager = EnterpriseDocumentManager(self.config_manager)
        generators = doc_manager.get_available_generators()
        
        print("\n可用的文档生成器:")
        print("=" * 50)
        
        for generator in generators:
            ai_mark = " [AI]" if generator['requires_ai'] else ""
            print(f"  {generator['name']:<15} {generator['description']}{ai_mark}")
            print(f"  {'输出文件:':<15} {generator['output_filename']}")
            print()
        
        print("说明:")
        print("  [AI] - 需要AI支持的生成器")
        print("  使用 --generators 参数指定要运行的生成器")
        print("  例如: gendocs generate --generators overview,api,architecture")
    
    def _display_results(self, results: Dict[str, bool], output_path: Path) -> None:
        """显示生成结果"""
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n生成完成! ({successful}/{total} 个生成器成功)")
        print("=" * 50)
        
        for generator_name, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {generator_name:<20} {status}")
        
        print(f"\n生成的文档保存在: {output_path}")
        
        # 列出生成的文件
        doc_files = list(output_path.glob('*.md'))
        if doc_files:
            print("\n生成的文档文件:")
            for doc_file in sorted(doc_files):
                print(f"  - {doc_file.name}")


class AnalyzeCommand(BaseCommand):
    """项目分析命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置analyze命令参数"""
        parser.add_argument(
            'project_path',
            type=Path,
            nargs='?',
            default=Path.cwd(),
            help='项目路径 (默认: 当前目录)'
        )
        
        parser.add_argument(
            '--format',
            choices=['text', 'json'],
            default='text',
            help='输出格式 (默认: text)'
        )
        
        parser.add_argument(
            '--output',
            type=Path,
            help='保存分析结果到文件'
        )
        
        parser.add_argument(
            '--detail',
            action='store_true',
            help='显示详细分析信息'
        )
    
    async def execute(self, args) -> int:
        """执行项目分析"""
        try:
            # 验证项目路径
            project_path = args.project_path.resolve()
            if not project_path.exists():
                self.logger.error(f"项目路径不存在: {project_path}")
                return 1
            
            # 获取分析器注册表
            registry = get_registry()
            
            # 分析项目
            self.logger.info(f"正在分析项目: {project_path}")
            analysis_result = registry.analyze_project(project_path)
            
            if not analysis_result:
                self.logger.error("无法分析项目，可能是不支持的项目类型")
                return 1
            
            # 输出分析结果
            if args.format == 'json':
                result_dict = analysis_result.to_dict()
                result_json = json.dumps(result_dict, ensure_ascii=False, indent=2)
                
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        f.write(result_json)
                    print(f"分析结果已保存到: {args.output}")
                else:
                    print(result_json)
            else:
                self._display_analysis_text(analysis_result, args.detail)
                
                if args.output:
                    with open(args.output, 'w', encoding='utf-8') as f:
                        f.write(self._format_analysis_text(analysis_result, args.detail))
                    print(f"\n分析结果已保存到: {args.output}")
            
            return 0
            
        except Exception as e:
            self.logger.error(f"项目分析失败: {e}")
            return 1
    
    def _display_analysis_text(self, result, detail: bool = False) -> None:
        """以文本格式显示分析结果"""
        print(f"\n项目分析结果")
        print("=" * 50)
        print(f"项目名称: {result.project_name}")
        print(f"项目类型: {result.project_type.value}")
        if result.framework:
            print(f"框架: {result.framework.value}")
        if result.version:
            print(f"版本: {result.version}")
        if result.description:
            print(f"描述: {result.description}")
        
        # 技术栈
        if result.tech_stack:
            print(f"\n技术栈:")
            if result.tech_stack.languages:
                print(f"  语言: {', '.join(result.tech_stack.languages)}")
            if result.tech_stack.frameworks:
                print(f"  框架: {', '.join(result.tech_stack.frameworks)}")
            if result.tech_stack.databases:
                print(f"  数据库: {', '.join(result.tech_stack.databases)}")
        
        # 依赖
        if result.dependencies:
            print(f"\n依赖包: {len(result.dependencies)} 个")
            if detail:
                for dep in result.dependencies[:10]:
                    version_info = f" ({dep.version})" if dep.version else ""
                    dev_mark = " [dev]" if dep.is_dev_dependency else ""
                    print(f"  - {dep.name}{version_info}{dev_mark}")
                if len(result.dependencies) > 10:
                    print(f"  ... 及其他 {len(result.dependencies) - 10} 个依赖")
        
        # API接口
        if result.apis:
            print(f"\nAPI接口: {len(result.apis)} 个")
            if detail:
                for api in result.apis[:5]:
                    print(f"  - {api.method} {api.path}")
                if len(result.apis) > 5:
                    print(f"  ... 及其他 {len(result.apis) - 5} 个接口")
        
        # 模块
        if result.modules:
            print(f"\n模块: {len(result.modules)} 个")
            if detail:
                for module in result.modules[:5]:
                    print(f"  - {module.name} ({module.file_count} 文件)")
                if len(result.modules) > 5:
                    print(f"  ... 及其他 {len(result.modules) - 5} 个模块")
        
        # 质量指标
        if result.quality_metrics:
            metrics = result.quality_metrics
            print(f"\n代码质量:")
            print(f"  总文件数: {metrics.total_files}")
            print(f"  总行数: {metrics.total_lines}")
            print(f"  代码行数: {metrics.code_lines}")
            print(f"  注释行数: {metrics.comment_lines}")
            if metrics.cyclomatic_complexity:
                print(f"  圈复杂度: {metrics.cyclomatic_complexity:.2f}")
    
    def _format_analysis_text(self, result, detail: bool = False) -> str:
        """格式化分析结果为文本"""
        lines = []
        lines.append("项目分析结果")
        lines.append("=" * 50)
        lines.append(f"项目名称: {result.project_name}")
        lines.append(f"项目类型: {result.project_type.value}")
        
        if result.framework:
            lines.append(f"框架: {result.framework.value}")
        if result.version:
            lines.append(f"版本: {result.version}")
        if result.description:
            lines.append(f"描述: {result.description}")
        
        # 添加更多详细信息...
        return '\n'.join(lines)


class BackupCommand(BaseCommand):
    """文档备份命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置backup命令参数"""
        subparsers = parser.add_subparsers(dest='backup_action', help='备份操作')
        
        # create 子命令
        create_parser = subparsers.add_parser('create', help='创建备份')
        create_parser.add_argument('project_path', type=Path, help='项目路径')
        create_parser.add_argument('-m', '--message', help='备份描述信息')
        
        # list 子命令
        list_parser = subparsers.add_parser('list', help='列出备份')
        list_parser.add_argument('project_path', type=Path, help='项目路径')
        
        # restore 子命令
        restore_parser = subparsers.add_parser('restore', help='恢复备份')
        restore_parser.add_argument('project_path', type=Path, help='项目路径')
        restore_parser.add_argument('backup_id', help='备份ID')
        restore_parser.add_argument('--overwrite', action='store_true', help='覆盖现有文件')
        
        # delete 子命令
        delete_parser = subparsers.add_parser('delete', help='删除备份')
        delete_parser.add_argument('project_path', type=Path, help='项目路径')
        delete_parser.add_argument('backup_id', help='备份ID')
    
    async def execute(self, args) -> int:
        """执行备份操作"""
        try:
            backup_manager = BackupManager()
            
            if args.backup_action == 'create':
                return await self._create_backup(backup_manager, args)
            elif args.backup_action == 'list':
                return await self._list_backups(backup_manager, args)
            elif args.backup_action == 'restore':
                return await self._restore_backup(backup_manager, args)
            elif args.backup_action == 'delete':
                return await self._delete_backup(backup_manager, args)
            else:
                print("请指定备份操作: create, list, restore, delete")
                return 1
                
        except Exception as e:
            self.logger.error(f"备份操作失败: {e}")
            return 1
    
    async def _create_backup(self, backup_manager, args) -> int:
        """创建备份"""
        project_path = args.project_path.resolve()
        description = args.message or f"手动备份 - {project_path.name}"
        
        print(f"正在创建备份: {project_path}")
        backup_info = backup_manager.create_backup(project_path, description)
        
        print(f"✅ 备份创建成功!")
        print(f"备份ID: {backup_info.backup_id}")
        print(f"备份时间: {backup_info.created_at}")
        print(f"文件数量: {backup_info.file_count}")
        print(f"备份大小: {backup_info.total_size / 1024:.1f} KB")
        
        return 0
    
    async def _list_backups(self, backup_manager, args) -> int:
        """列出备份"""
        project_path = args.project_path.resolve()
        backups = backup_manager.list_backups(project_path)
        
        if not backups:
            print("没有找到备份")
            return 0
        
        print(f"\n{project_path.name} 的备份列表:")
        print("=" * 80)
        print(f"{'备份ID':<20} {'创建时间':<20} {'文件数':<8} {'大小':<10} {'描述'}")
        print("-" * 80)
        
        for backup in backups:
            size_kb = backup.total_size / 1024
            created_time = backup.created_at.strftime('%Y-%m-%d %H:%M:%S')
            description = backup.description[:30] + "..." if len(backup.description) > 30 else backup.description
            print(f"{backup.backup_id:<20} {created_time:<20} {backup.file_count:<8} {size_kb:<8.1f}KB {description}")
        
        return 0
    
    async def _restore_backup(self, backup_manager, args) -> int:
        """恢复备份"""
        project_path = args.project_path.resolve()
        backup_id = args.backup_id
        
        print(f"正在恢复备份: {backup_id}")
        success = backup_manager.restore_backup(backup_id, project_path, args.overwrite)
        
        if success:
            print("✅ 备份恢复成功!")
            return 0
        else:
            print("❌ 备份恢复失败")
            return 1
    
    async def _delete_backup(self, backup_manager, args) -> int:
        """删除备份"""
        project_path = args.project_path.resolve()
        backup_id = args.backup_id
        
        # 确认删除
        response = input(f"确定要删除备份 {backup_id}? [y/N]: ")
        if response.lower() not in ['y', 'yes']:
            print("操作已取消")
            return 0
        
        success = backup_manager.delete_backup(backup_id, project_path)
        
        if success:
            print("✅ 备份删除成功!")
            return 0
        else:
            print("❌ 备份删除失败")
            return 1


class ConfigCommand(BaseCommand):
    """配置管理命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置config命令参数"""
        subparsers = parser.add_subparsers(dest='config_action', help='配置操作')
        
        # init 子命令
        init_parser = subparsers.add_parser('init', help='初始化配置文件')
        init_parser.add_argument('--force', action='store_true', help='覆盖现有配置文件')
        
        # show 子命令
        show_parser = subparsers.add_parser('show', help='显示当前配置')
        show_parser.add_argument('--format', choices=['yaml', 'json'], default='yaml', help='输出格式')
        
        # set 子命令
        set_parser = subparsers.add_parser('set', help='设置配置项')
        set_parser.add_argument('key', help='配置键 (例如: ai.provider)')
        set_parser.add_argument('value', help='配置值')
        
        # get 子命令
        get_parser = subparsers.add_parser('get', help='获取配置项')
        get_parser.add_argument('key', help='配置键')
        
        # template 子命令
        template_parser = subparsers.add_parser('template', help='生成配置模板')
        template_parser.add_argument('--type', choices=['basic', 'web_api', 'machine_learning', 'enterprise', 'open_source'], 
                                   help='模板类型')
        template_parser.add_argument('--auto-detect', action='store_true', help='自动检测项目类型')
        template_parser.add_argument('--list', action='store_true', help='列出所有可用模板')
        template_parser.add_argument('--output', type=Path, help='输出文件路径 (默认: gendocs.yaml)')
        template_parser.add_argument('--force', action='store_true', help='覆盖现有配置文件')
    
    async def execute(self, args) -> int:
        """执行配置操作"""
        try:
            if args.config_action == 'init':
                return await self._init_config(args)
            elif args.config_action == 'show':
                return await self._show_config(args)
            elif args.config_action == 'set':
                return await self._set_config(args)
            elif args.config_action == 'get':
                return await self._get_config(args)
            elif args.config_action == 'template':
                return await self._generate_template(args)
            else:
                print("请指定配置操作: init, show, set, get, template")
                return 1
                
        except Exception as e:
            self.logger.error(f"配置操作失败: {e}")
            return 1
    
    async def _init_config(self, args) -> int:
        """初始化配置文件"""
        config_path = Path.cwd() / 'gendocs.yaml'
        
        if config_path.exists() and not args.force:
            print(f"配置文件已存在: {config_path}")
            response = input("是否覆盖? [y/N]: ")
            if response.lower() not in ['y', 'yes']:
                print("操作已取消")
                return 0
        
        # 创建默认配置
        default_config = """# GenDocs 配置文件
# 
# 更多配置选项请参考文档: https://gendocs.readthedocs.io/

# AI 配置
ai:
  enabled: true
  provider: openai  # openai, deepseek, qwen
  base_url: "https://api.openai.com/v1"
  api_key: "${GENDOCS_AI_API_KEY}"
  model: "gpt-4o-mini"
  temperature: 0.3
  max_tokens: 4000

# 备份配置
backup:
  enabled: true
  max_backups: 10
  strategy: timestamp
  compress: false

# 生成配置
generation:
  output_dir: "docs"
  concurrent_jobs: 3
  include_ai_enhanced: true

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
"""
        
        config_path.write_text(default_config, encoding='utf-8')
        print(f"✅ 配置文件已创建: {config_path}")
        print("\n请编辑配置文件以设置您的AI API密钥:")
        print(f"  - 直接编辑文件中的 api_key 字段")
        print(f"  - 或设置环境变量: export GENDOCS_AI_API_KEY=your-api-key")
        
        return 0
    
    async def _show_config(self, args) -> int:
        """显示配置"""
        config = self.config_manager.get_config()
        
        if args.format == 'json':
            print(json.dumps(config, ensure_ascii=False, indent=2))
        else:
            import yaml
            print(yaml.dump(config, allow_unicode=True, default_flow_style=False))
        
        return 0
    
    async def _set_config(self, args) -> int:
        """设置配置项"""
        # 这里需要实现配置项设置逻辑
        print(f"设置配置项 {args.key} = {args.value}")
        print("注意: 此功能需要进一步实现")
        return 0
    
    async def _get_config(self, args) -> int:
        """获取配置项"""
        config = self.config_manager.get_config()
        
        # 简单的点号分隔键查找
        keys = args.key.split('.')
        value = config
        
        try:
            for key in keys:
                value = value[key]
            print(value)
            return 0
        except (KeyError, TypeError):
            print(f"配置项不存在: {args.key}")
            return 1
    
    async def _generate_template(self, args) -> int:
        """生成配置模板"""
        template_generator = ConfigTemplateGenerator()
        
        # 列出所有模板
        if args.list:
            templates = template_generator.get_available_templates()
            print("\n可用的配置模板:")
            print("=" * 60)
            
            for template in templates:
                print(f"类型: {template['type']}")
                print(f"名称: {template['name']}")
                print(f"描述: {template['description']}")
                print(f"适用于: {', '.join(template['suitable_for'])}")
                print("-" * 40)
            
            print("\n使用示例:")
            print("  gendocs config template --type basic")
            print("  gendocs config template --auto-detect")
            print("  gendocs config template --type web_api --output my-config.yaml")
            return 0
        
        # 设置输出文件
        output_path = args.output or Path.cwd() / 'gendocs.yaml'
        
        # 检查文件是否存在
        if output_path.exists() and not args.force:
            print(f"配置文件已存在: {output_path}")
            response = input("是否覆盖? [y/N]: ")
            if response.lower() not in ['y', 'yes']:
                print("操作已取消")
                return 0
        
        # 确定模板类型
        if args.auto_detect:
            # 自动检测项目类型
            print("正在自动检测项目类型...")
            
            # 获取项目分析结果
            registry = get_registry()
            project_path = Path.cwd()
            analysis_result = registry.analyze_project(project_path)
            
            if analysis_result:
                template_type = template_generator.detect_suitable_template(
                    project_path, 
                    analysis_result.project_type,
                    analysis_result.framework
                )
                print(f"检测到项目类型: {template_type.value}")
            else:
                template_type = ConfigTemplateType.BASIC
                print("无法检测项目类型，使用基础模板")
                
        elif args.type:
            # 使用指定的模板类型
            template_type = ConfigTemplateType(args.type)
            print(f"使用指定的模板类型: {template_type.value}")
        else:
            # 默认使用基础模板
            template_type = ConfigTemplateType.BASIC
            print("使用默认的基础模板")
        
        # 生成模板内容
        try:
            project_path = Path.cwd()
            
            # 获取项目分析结果（如果可用）
            analysis_result = None
            try:
                registry = get_registry()
                analysis_result = registry.analyze_project(project_path)
            except:
                pass
            
            # 准备上下文数据
            context = {
                'generation_date': template_generator._get_timestamp() if hasattr(template_generator, '_get_timestamp') else '2024-01-01',
                'author': analysis_result.author if analysis_result and analysis_result.author else None,
                'version': analysis_result.version if analysis_result and analysis_result.version else None,
                'license': analysis_result.license if analysis_result and analysis_result.license else None,
                'description': analysis_result.description if analysis_result and analysis_result.description else None,
            }
            
            # 生成模板
            template_content = template_generator.generate_template(
                template_type=template_type,
                project_path=project_path,
                project_type=analysis_result.project_type if analysis_result else None,
                framework=analysis_result.framework if analysis_result else None,
                **context
            )
            
            # 写入文件
            output_path.write_text(template_content, encoding='utf-8')
            
            print(f"✅ 配置模板已生成: {output_path}")
            print(f"模板类型: {template_type.value}")
            print(f"文件大小: {len(template_content)} 字符")
            
            print("\n下一步:")
            print("1. 编辑配置文件设置您的AI API密钥")
            print("2. 根据项目需求调整配置参数") 
            print("3. 运行 'gendocs generate' 开始生成文档")
            
            return 0
            
        except Exception as e:
            self.logger.error(f"生成配置模板失败: {e}")
            return 1


class BatchCommand(BaseCommand):
    """批量处理命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置batch命令参数"""
        parser.add_argument(
            'root_path',
            type=Path,
            nargs='?',
            default=Path.cwd(),
            help='根目录路径，将在此目录下搜索项目 (默认: 当前目录)'
        )
        
        parser.add_argument(
            '--projects',
            type=str,
            help='项目路径列表，用逗号分隔 (例如: /path/to/proj1,/path/to/proj2)'
        )
        
        parser.add_argument(
            '--config-file',
            type=Path,
            help='批量处理配置文件路径'
        )
        
        parser.add_argument(
            '--max-concurrent',
            type=int,
            default=3,
            help='最大并发数 (默认: 3)'
        )
        
        parser.add_argument(
            '--timeout',
            type=int,
            default=600,
            help='单个项目超时时间(秒) (默认: 600)'
        )
        
        parser.add_argument(
            '--fail-fast',
            action='store_true',
            help='遇到错误时立即停止所有处理'
        )
        
        parser.add_argument(
            '--skip-existing',
            action='store_true',
            help='跳过已存在文档的项目'
        )
        
        parser.add_argument(
            '--min-files',
            type=int,
            default=1,
            help='项目最少文件数量过滤 (默认: 1)'
        )
        
        parser.add_argument(
            '--generators',
            type=str,
            help='指定要运行的生成器，用逗号分隔'
        )
        
        parser.add_argument(
            '--output-dir',
            type=str,
            default='docs',
            help='输出目录名称 (默认: docs)'
        )
        
        parser.add_argument(
            '--report-format',
            choices=['json', 'html', 'csv'],
            default='json',
            help='报告格式 (默认: json)'
        )
        
        parser.add_argument(
            '--discover',
            action='store_true',
            help='自动发现项目'
        )
        
        parser.add_argument(
            '--max-depth',
            type=int,
            default=3,
            help='项目搜索最大深度 (默认: 3)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要处理的项目，不实际执行'
        )
    
    async def execute(self, args) -> int:
        """执行批量处理"""
        try:
            # 创建批量处理配置
            batch_config = BatchConfig(
                max_concurrent=args.max_concurrent,
                fail_fast=args.fail_fast,
                timeout_seconds=args.timeout,
                skip_existing=args.skip_existing,
                min_file_count=args.min_files,
                report_format=args.report_format,
                progress_callback=self._progress_callback,
                completion_callback=self._completion_callback
            )
            
            # 获取项目列表
            if args.projects:
                # 使用指定的项目路径
                project_paths = [Path(p.strip()) for p in args.projects.split(',')]
                projects = [ProjectConfig(path=path) for path in project_paths]
            elif args.discover:
                # 自动发现项目
                projects = BatchProcessor.discover_projects(
                    root_path=args.root_path,
                    max_depth=args.max_depth
                )
                print(f"在 {args.root_path} 中发现 {len(projects)} 个项目")
            else:
                # 默认处理根路径下的直接子目录
                projects = []
                for item in args.root_path.iterdir():
                    if item.is_dir() and not item.name.startswith('.'):
                        projects.append(ProjectConfig(path=item))
            
            if not projects:
                print("没有找到要处理的项目")
                return 0
            
            # 设置项目配置
            for project in projects:
                project.output_dir = project.path / args.output_dir
                if args.generators:
                    project.generators = [g.strip() for g in args.generators.split(',')]
                if args.config_file:
                    project.config_file = args.config_file
            
            # 显示项目列表
            print(f"\n将要处理的项目 ({len(projects)} 个):")
            print("=" * 60)
            for i, project in enumerate(projects, 1):
                print(f"{i:2d}. {project.path}")
                if project.generators:
                    print(f"     生成器: {', '.join(project.generators)}")
                print(f"     输出: {project.output_dir}")
            
            # 干运行模式
            if args.dry_run:
                print("\n干运行模式，不实际执行处理")
                return 0
            
            # 确认处理
            if not self._confirm_processing(len(projects)):
                print("操作已取消")
                return 0
            
            # 创建批量处理器
            batch_processor = BatchProcessor(self.config_manager, batch_config)
            
            # 执行批量处理
            print(f"\n开始批量处理...")
            results = await batch_processor.process_projects(projects)
            
            # 显示结果摘要
            self._display_summary(results)
            
            # 检查是否有失败的项目
            failed_count = len([r for r in results if not r.success])
            if failed_count > 0:
                self.logger.warning(f"有 {failed_count} 个项目处理失败")
                return 1
            
            return 0
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return 1
    
    def _confirm_processing(self, project_count: int) -> bool:
        """确认是否继续处理"""
        response = input(f"\n确定要处理 {project_count} 个项目吗? [y/N]: ")
        return response.lower() in ['y', 'yes']
    
    async def _progress_callback(self, result) -> None:
        """进度回调"""
        status_emoji = "✅" if result.success else "❌"
        duration = f"{result.duration_seconds:.2f}s" if result.duration_seconds else "N/A"
        print(f"{status_emoji} {result.project_path.name} ({result.status.value}) - {duration}")
        
        if result.warnings:
            for warning in result.warnings:
                print(f"   ⚠️  {warning}")
        
        if result.error_message:
            print(f"   ❌ {result.error_message}")
    
    async def _completion_callback(self, results) -> None:
        """完成回调"""
        successful_count = len([r for r in results if r.success])
        total_count = len(results)
        print(f"\n批量处理完成: {successful_count}/{total_count} 项目成功")
    
    def _display_summary(self, results) -> None:
        """显示结果摘要"""
        total_count = len(results)
        successful_count = len([r for r in results if r.success])
        failed_count = len([r for r in results if not r.success])
        skipped_count = len([r for r in results if r.status.value == 'skipped'])
        
        total_duration = sum(r.duration_seconds or 0 for r in results)
        total_docs = sum(len(r.generated_docs) for r in results)
        
        print(f"\n批量处理摘要:")
        print("=" * 50)
        print(f"总项目数:     {total_count}")
        print(f"成功项目:     {successful_count}")
        print(f"失败项目:     {failed_count}")
        print(f"跳过项目:     {skipped_count}")
        print(f"生成文档:     {total_docs} 个")
        print(f"总耗时:       {total_duration:.2f} 秒")
        print(f"平均耗时:     {total_duration/total_count:.2f} 秒/项目")
        
        if failed_count > 0:
            print(f"\n失败项目列表:")
            for result in results:
                if not result.success:
                    print(f"  - {result.project_path}: {result.error_message}")


class MonitorCommand(BaseCommand):
    """性能监控命令"""
    
    @staticmethod
    def setup_parser(parser) -> None:
        """设置monitor命令参数"""
        subparsers = parser.add_subparsers(dest='monitor_action', help='监控操作')
        
        # start 子命令
        start_parser = subparsers.add_parser('start', help='启动性能监控')
        start_parser.add_argument('--interval', type=int, default=30, help='监控间隔(秒)')
        start_parser.add_argument('--enable-profiling', action='store_true', help='启用性能分析')
        start_parser.add_argument('--auto-report', type=int, default=3600, help='自动报告间隔(秒)')
        
        # stop 子命令
        stop_parser = subparsers.add_parser('stop', help='停止性能监控')
        
        # status 子命令
        status_parser = subparsers.add_parser('status', help='查看监控状态')
        
        # report 子命令
        report_parser = subparsers.add_parser('report', help='生成性能报告')
        report_parser.add_argument('--format', choices=['json', 'html'], default='html', help='报告格式')
        report_parser.add_argument('--output', type=Path, help='输出文件路径')
        
        # metrics 子命令
        metrics_parser = subparsers.add_parser('metrics', help='查看性能指标')
        metrics_parser.add_argument('--export', type=Path, help='导出指标到文件')
        metrics_parser.add_argument('--reset', action='store_true', help='重置指标')
        
        # threshold 子命令
        threshold_parser = subparsers.add_parser('threshold', help='设置性能阈值')
        threshold_parser.add_argument('metric', help='指标名称')
        threshold_parser.add_argument('value', type=float, help='阈值')
    
    async def execute(self, args) -> int:
        """执行监控操作"""
        try:
            if args.monitor_action == 'start':
                return await self._start_monitoring(args)
            elif args.monitor_action == 'stop':
                return await self._stop_monitoring(args)
            elif args.monitor_action == 'status':
                return await self._show_status(args)
            elif args.monitor_action == 'report':
                return await self._generate_report(args)
            elif args.monitor_action == 'metrics':
                return await self._show_metrics(args)
            elif args.monitor_action == 'threshold':
                return await self._set_threshold(args)
            else:
                print("请指定监控操作: start, stop, status, report, metrics, threshold")
                return 1
                
        except Exception as e:
            self.logger.error(f"监控操作失败: {e}")
            return 1
    
    async def _start_monitoring(self, args) -> int:
        """启动性能监控"""
        monitor = get_performance_monitor()
        
        # 配置监控参数
        monitor.system_metrics_interval = args.interval
        monitor.auto_report_interval = args.auto_report
        
        # 启用性能分析
        if args.enable_profiling:
            monitor.profile_manager.config.enable_profiling = True
            monitor.profile_manager.config.enable_memory_profiling = True
            print("已启用性能分析")
        
        # 注册警报回调
        def alert_callback(alert_info):
            print(f"⚠️  性能警报: {alert_info['type']}")
            print(f"   时间: {alert_info['timestamp']}")
            print(f"   数据: {alert_info['data']}")
        
        monitor.register_alert_callback('general', alert_callback)
        
        # 启动监控
        monitor.start_monitoring()
        
        print(f"✅ 性能监控已启动")
        print(f"   监控间隔: {args.interval} 秒")
        print(f"   自动报告间隔: {args.auto_report} 秒")
        print(f"   性能分析: {'已启用' if args.enable_profiling else '已禁用'}")
        
        return 0
    
    async def _stop_monitoring(self, args) -> int:
        """停止性能监控"""
        monitor = get_performance_monitor()
        monitor.stop_monitoring()
        
        print("✅ 性能监控已停止")
        return 0
    
    async def _show_status(self, args) -> int:
        """显示监控状态"""
        monitor = get_performance_monitor()
        status = monitor.get_current_status()
        
        print("\n性能监控状态:")
        print("=" * 50)
        print(f"监控状态: {'运行中' if status['monitoring_active'] else '已停止'}")
        print(f"运行时间: {status['uptime_seconds']:.0f} 秒")
        
        # 当前系统指标
        sys_metrics = status['current_system_metrics']
        if sys_metrics:
            print(f"\n当前系统资源:")
            print(f"  CPU使用率: {sys_metrics['cpu_percent']:.1f}%")
            print(f"  内存使用率: {sys_metrics['memory_percent']:.1f}%")
            print(f"  内存使用量: {sys_metrics['memory_used_mb']:.1f} MB")
        
        # 性能摘要
        perf_summary = status['performance_summary']
        print(f"\n性能摘要:")
        print(f"  文档生成: {perf_summary['documents_generated']} 个")
        print(f"  文档成功率: {perf_summary['documents_success_rate']:.1f}%")
        print(f"  AI请求: {perf_summary['ai_requests']} 次")
        print(f"  AI成功率: {perf_summary['ai_success_rate']:.1f}%")
        print(f"  总错误数: {perf_summary['total_errors']}")
        
        # 性能阈值
        print(f"\n性能阈值:")
        for metric, threshold in status['thresholds'].items():
            print(f"  {metric}: {threshold}")
        
        return 0
    
    async def _generate_report(self, args) -> int:
        """生成性能报告"""
        monitor = get_performance_monitor()
        
        try:
            if args.format == 'html':
                report_file = monitor.generate_report_now()
                print(f"✅ HTML性能报告已生成: {report_file}")
            else:
                # JSON格式的简单报告
                if args.output:
                    monitor.export_metrics(args.output)
                    print(f"✅ JSON指标数据已导出: {args.output}")
                else:
                    # 显示简单摘要
                    summary = monitor.get_performance_summary()
                    print(summary)
            
            return 0
            
        except Exception as e:
            self.logger.error(f"生成性能报告失败: {e}")
            return 1
    
    async def _show_metrics(self, args) -> int:
        """显示性能指标"""
        monitor = get_performance_monitor()
        
        if args.reset:
            monitor.reset_metrics()
            print("✅ 性能指标已重置")
            return 0
        
        if args.export:
            monitor.export_metrics(args.export)
            print(f"✅ 指标数据已导出: {args.export}")
            return 0
        
        # 显示指标摘要
        summary = monitor.get_performance_summary()
        print(summary)
        
        # 显示函数性能统计
        function_profiles = monitor.profile_manager.get_function_profiles()
        if function_profiles:
            print(f"\n函数性能统计:")
            print("=" * 80)
            print(f"{'函数名':<40} {'调用次数':<10} {'总耗时':<12} {'平均耗时':<12}")
            print("-" * 80)
            
            for func_name, stats in sorted(function_profiles.items(), 
                                         key=lambda x: x[1]['total_time'], reverse=True)[:10]:
                print(f"{func_name:<40} {stats['call_count']:<10} "
                      f"{stats['total_time']:<12.3f} {stats['average_time']:<12.3f}")
        
        return 0
    
    async def _set_threshold(self, args) -> int:
        """设置性能阈值"""
        monitor = get_performance_monitor()
        
        try:
            monitor.set_performance_threshold(args.metric, args.value)
            print(f"✅ 已设置性能阈值: {args.metric} = {args.value}")
            return 0
        except Exception as e:
            self.logger.error(f"设置性能阈值失败: {e}")
            return 1