"""
配置模板生成模块

为不同类型的项目生成标准化的配置文件模板。
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from enum import Enum

from ..analyzers.types import ProjectType, FrameworkType


class ConfigTemplateType(Enum):
    """配置模板类型"""
    BASIC = "basic"
    WEB_API = "web_api"
    MACHINE_LEARNING = "machine_learning"
    ENTERPRISE = "enterprise"
    OPEN_SOURCE = "open_source"


class ConfigTemplateGenerator:
    """配置模板生成器"""
    
    def __init__(self):
        """初始化配置模板生成器"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def generate_template(self, 
                         template_type: ConfigTemplateType,
                         project_path: Path,
                         project_type: Optional[ProjectType] = None,
                         framework: Optional[FrameworkType] = None,
                         **kwargs) -> str:
        """生成配置模板
        
        Args:
            template_type: 配置模板类型
            project_path: 项目路径
            project_type: 项目类型
            framework: 框架类型
            **kwargs: 额外参数
            
        Returns:
            配置文件内容
        """
        try:
            if template_type == ConfigTemplateType.BASIC:
                return self._generate_basic_template(project_path, **kwargs)
            elif template_type == ConfigTemplateType.WEB_API:
                return self._generate_web_api_template(project_path, framework, **kwargs)
            elif template_type == ConfigTemplateType.MACHINE_LEARNING:
                return self._generate_ml_template(project_path, **kwargs)
            elif template_type == ConfigTemplateType.ENTERPRISE:
                return self._generate_enterprise_template(project_path, framework, **kwargs)
            elif template_type == ConfigTemplateType.OPEN_SOURCE:
                return self._generate_opensource_template(project_path, **kwargs)
            else:
                return self._generate_basic_template(project_path, **kwargs)
                
        except Exception as e:
            self.logger.error(f"生成配置模板失败: {e}")
            return self._generate_basic_template(project_path, **kwargs)
    
    def _generate_basic_template(self, project_path: Path, **kwargs) -> str:
        """生成基础配置模板"""
        project_name = project_path.name
        
        return f"""# GenDocs 配置文件 - 基础模板
# 项目: {project_name}
# 生成时间: {{{{ generation_date }}}}
# 
# 更多配置选项请参考文档: https://gendocs.readthedocs.io/

# AI 配置
ai:
  enabled: true
  provider: openai  # openai, deepseek, qwen, claude
  base_url: "https://api.openai.com/v1"
  api_key: "${{GENDOCS_AI_API_KEY}}"
  model: "gpt-4o-mini"
  temperature: 0.3
  max_tokens: 4000
  timeout: 30

# 备份配置
backup:
  enabled: true
  max_backups: 10
  strategy: timestamp  # timestamp, sequence
  compress: false
  backup_dir: ".gendocs/backups"

# 生成配置
generation:
  output_dir: "docs"
  concurrent_jobs: 3
  include_ai_enhanced: true
  force_overwrite: false
  
  # 默认启用的生成器
  default_generators:
    - overview
    - architecture
    - api_docs
    - installation
    - changelog

# 文档配置
docs:
  language: "zh-CN"
  author: "{{{{ author or 'Project Team' }}}}"
  version: "{{{{ version or '1.0.0' }}}}"
  license: "{{{{ license or 'MIT' }}}}"
  
  # 项目信息
  project:
    description: "{{{{ description or '项目描述' }}}}"
    homepage: "{{{{ homepage or '' }}}}"
    repository: "{{{{ repository or '' }}}}"
    keywords: []

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ".gendocs/logs/gendocs.log"
"""
    
    def _generate_web_api_template(self, project_path: Path, framework: Optional[FrameworkType], **kwargs) -> str:
        """生成Web API项目配置模板"""
        project_name = project_path.name
        framework_name = framework.value if framework else "unknown"
        
        # 根据框架调整配置
        if framework == FrameworkType.FASTAPI:
            api_doc_format = "openapi"
            api_generators = ["api_docs", "openapi_spec", "sdk_docs"]
        elif framework == FrameworkType.DJANGO:
            api_doc_format = "drf"
            api_generators = ["api_docs", "drf_docs", "admin_guide"]
        elif framework == FrameworkType.FLASK:
            api_doc_format = "flask"
            api_generators = ["api_docs", "flask_docs", "blueprint_guide"]
        else:
            api_doc_format = "rest"
            api_generators = ["api_docs", "rest_docs"]
        
        return f"""# GenDocs 配置文件 - Web API 模板
# 项目: {project_name}
# 框架: {framework_name}
# 生成时间: {{{{ generation_date }}}}

# AI 配置 - API项目专用提示词
ai:
  enabled: true
  provider: openai
  base_url: "https://api.openai.com/v1"
  api_key: "${{GENDOCS_AI_API_KEY}}"
  model: "gpt-4o-mini"
  temperature: 0.2  # 更低的温度保证API文档的准确性
  max_tokens: 6000  # 更多token用于API文档生成
  timeout: 45

# 备份配置
backup:
  enabled: true
  max_backups: 15
  strategy: timestamp
  compress: true
  backup_dir: ".gendocs/backups"

# 生成配置 - Web API专用
generation:
  output_dir: "docs"
  concurrent_jobs: 4
  include_ai_enhanced: true
  force_overwrite: false
  
  # API项目推荐生成器
  default_generators:
    - overview
    - architecture
    - api_docs
    - installation
    - deployment
    - security
    - changelog
    - troubleshooting

# API专用配置
api:
  format: "{api_doc_format}"
  include_examples: true
  include_schemas: true
  include_authentication: true
  include_rate_limiting: true
  
  # API文档配置
  docs:
    base_url: "https://api.{project_name.lower()}.com"
    version: "v1"
    contact:
      name: "API Support"
      email: "api-support@{project_name.lower()}.com"
    
# 文档配置
docs:
  language: "zh-CN"
  author: "{{{{ author or 'API Team' }}}}"
  version: "{{{{ version or '1.0.0' }}}}"
  license: "{{{{ license or 'MIT' }}}}"
  
  project:
    description: "{{{{ description or 'Web API服务' }}}}"
    homepage: "{{{{ homepage or '' }}}}"
    repository: "{{{{ repository or '' }}}}"
    keywords: ["api", "web", "{framework_name}"]

# 部署配置
deployment:
  environments:
    - development
    - staging
    - production
  
  docker:
    enabled: true
    port: 8000
  
  monitoring:
    health_check: "/health"
    metrics: "/metrics"

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ".gendocs/logs/api-docs.log"
"""
    
    def _generate_ml_template(self, project_path: Path, **kwargs) -> str:
        """生成机器学习项目配置模板"""
        project_name = project_path.name
        
        return f"""# GenDocs 配置文件 - 机器学习模板
# 项目: {project_name}
# 类型: 机器学习
# 生成时间: {{{{ generation_date }}}}

# AI 配置 - ML项目专用
ai:
  enabled: true
  provider: openai
  base_url: "https://api.openai.com/v1"
  api_key: "${{GENDOCS_AI_API_KEY}}"
  model: "gpt-4o"  # 使用更强的模型处理复杂的ML概念
  temperature: 0.4
  max_tokens: 8000  # 更多token用于详细的ML文档
  timeout: 60

# 备份配置
backup:
  enabled: true
  max_backups: 20
  strategy: timestamp
  compress: true
  backup_dir: ".gendocs/backups"

# 生成配置 - ML专用
generation:
  output_dir: "docs"
  concurrent_jobs: 2  # 减少并发避免GPU资源冲突
  include_ai_enhanced: true
  force_overwrite: false
  
  # ML项目推荐生成器
  default_generators:
    - overview
    - model_architecture
    - data_pipeline
    - training_guide
    - evaluation_metrics
    - deployment
    - api_docs
    - changelog

# ML专用配置
ml:
  # 模型信息
  model:
    type: "{{{{ model_type or 'unknown' }}}}"
    framework: "{{{{ ml_framework or 'pytorch' }}}}"
    task: "{{{{ task_type or 'classification' }}}}"
  
  # 数据信息
  data:
    source: "{{{{ data_source or 'custom dataset' }}}}"
    format: "{{{{ data_format or 'csv' }}}}"
    size: "{{{{ dataset_size or 'unknown' }}}}"
  
  # 训练配置
  training:
    epochs: "{{{{ epochs or 'configurable' }}}}"
    batch_size: "{{{{ batch_size or 'configurable' }}}}"
    optimizer: "{{{{ optimizer or 'Adam' }}}}"
  
  # 评估指标
  metrics:
    primary: "{{{{ primary_metric or 'accuracy' }}}}"
    secondary: ["precision", "recall", "f1-score"]

# 文档配置
docs:
  language: "zh-CN"
  author: "{{{{ author or 'ML Team' }}}}"
  version: "{{{{ version or '1.0.0' }}}}"
  license: "{{{{ license or 'MIT' }}}}"
  
  project:
    description: "{{{{ description or '机器学习项目' }}}}"
    homepage: "{{{{ homepage or '' }}}}"
    repository: "{{{{ repository or '' }}}}"
    keywords: ["machine-learning", "ai", "ml"]

# 实验配置
experiments:
  tracking: true
  platform: "mlflow"  # mlflow, wandb, tensorboard
  log_dir: "experiments"
  
  # 实验文档
  docs:
    include_hyperparameters: true
    include_results: true
    include_visualizations: true

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ".gendocs/logs/ml-docs.log"
"""
    
    def _generate_enterprise_template(self, project_path: Path, framework: Optional[FrameworkType], **kwargs) -> str:
        """生成企业级项目配置模板"""
        project_name = project_path.name
        framework_name = framework.value if framework else "enterprise"
        
        return f"""# GenDocs 配置文件 - 企业级模板
# 项目: {project_name}
# 框架: {framework_name}
# 类型: 企业级应用
# 生成时间: {{{{ generation_date }}}}

# AI 配置 - 企业级专用
ai:
  enabled: true
  provider: openai
  base_url: "https://api.openai.com/v1"
  api_key: "${{GENDOCS_AI_API_KEY}}"
  model: "gpt-4o"
  temperature: 0.1  # 极低温度确保企业文档的严谨性
  max_tokens: 8000
  timeout: 60
  
  # 企业级提示词配置
  prompts:
    compliance_check: true
    security_review: true
    architecture_review: true

# 备份配置 - 企业级
backup:
  enabled: true
  max_backups: 30
  strategy: timestamp
  compress: true
  backup_dir: ".gendocs/backups"
  encryption: true  # 企业级加密备份
  retention_days: 90

# 生成配置 - 企业级全套
generation:
  output_dir: "docs"
  concurrent_jobs: 6
  include_ai_enhanced: true
  force_overwrite: false
  
  # 企业级完整生成器套装
  default_generators:
    - overview
    - architecture
    - api_docs
    - installation
    - deployment
    - security
    - compliance
    - operations
    - troubleshooting
    - changelog
    - user_guide
    - admin_guide

# 企业级专用配置
enterprise:
  # 合规性配置
  compliance:
    standards: ["ISO 27001", "SOC 2", "GDPR"]
    audit_trail: true
    document_approval: true
  
  # 安全配置
  security:
    classification: "{{{{ security_level or 'Internal' }}}}"
    access_control: true
    encryption_required: true
  
  # 运维配置
  operations:
    monitoring: true
    logging: true
    alerting: true
    sla_targets:
      availability: "99.9%"
      response_time: "< 500ms"

# 团队配置
team:
  roles:
    - name: "Tech Lead"
      responsibilities: ["architecture", "code_review"]
    - name: "DevOps Engineer"
      responsibilities: ["deployment", "monitoring"]
    - name: "Security Engineer"
      responsibilities: ["security", "compliance"]
    - name: "QA Engineer"
      responsibilities: ["testing", "quality_assurance"]

# 文档配置 - 企业级
docs:
  language: "zh-CN"
  author: "{{{{ author or 'Enterprise Team' }}}}"
  version: "{{{{ version or '1.0.0' }}}}"
  license: "{{{{ license or 'Proprietary' }}}}"
  classification: "{{{{ classification or 'Internal Use Only' }}}}"
  
  project:
    description: "{{{{ description or '企业级应用系统' }}}}"
    business_owner: "{{{{ business_owner or 'Product Owner' }}}}"
    technical_owner: "{{{{ technical_owner or 'Tech Lead' }}}}"
    cost_center: "{{{{ cost_center or 'IT Department' }}}}"

# 质量保证配置
quality:
  code_review: true
  automated_testing: true
  performance_testing: true
  security_scanning: true
  
  # 文档质量
  doc_review: true
  spell_check: true
  style_guide: "enterprise"

# 环境配置
environments:
  development:
    url: "https://dev-{project_name.lower()}.company.com"
    purpose: "开发环境"
  
  staging:
    url: "https://staging-{project_name.lower()}.company.com"
    purpose: "测试环境"
  
  production:
    url: "https://{project_name.lower()}.company.com"
    purpose: "生产环境"
    sla_tier: "Tier 1"

# 日志配置 - 企业级
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ".gendocs/logs/enterprise-docs.log"
  centralized_logging: true
  log_retention_days: 365
"""
    
    def _generate_opensource_template(self, project_path: Path, **kwargs) -> str:
        """生成开源项目配置模板"""
        project_name = project_path.name
        
        return f"""# GenDocs 配置文件 - 开源项目模板
# 项目: {project_name}
# 类型: 开源项目
# 生成时间: {{{{ generation_date }}}}

# AI 配置 - 开源友好
ai:
  enabled: true
  provider: openai
  base_url: "https://api.openai.com/v1"
  api_key: "${{GENDOCS_AI_API_KEY}}"
  model: "gpt-4o-mini"
  temperature: 0.5  # 稍高温度增加创造性
  max_tokens: 6000
  timeout: 45

# 备份配置
backup:
  enabled: true
  max_backups: 10
  strategy: timestamp
  compress: true
  backup_dir: ".gendocs/backups"
  public_safe: true  # 确保备份不包含敏感信息

# 生成配置 - 开源项目专用
generation:
  output_dir: "docs"
  concurrent_jobs: 4
  include_ai_enhanced: true
  force_overwrite: false
  
  # 开源项目推荐生成器
  default_generators:
    - overview
    - installation
    - user_guide
    - api_docs
    - contributing
    - changelog
    - license_info
    - community

# 开源专用配置
opensource:
  # 许可证信息
  license:
    type: "{{{{ license or 'MIT' }}}}"
    year: "{{{{ license_year or '2024' }}}}"
    holder: "{{{{ license_holder or 'Contributors' }}}}"
  
  # 贡献指南
  contributing:
    guidelines: true
    code_of_conduct: true
    issue_templates: true
    pr_templates: true
  
  # 社区配置
  community:
    discord: "{{{{ discord_url or '' }}}}"
    slack: "{{{{ slack_url or '' }}}}"
    forum: "{{{{ forum_url or '' }}}}"
    twitter: "{{{{ twitter_handle or '' }}}}"

# 文档配置 - 开源友好
docs:
  language: "zh-CN"
  author: "{{{{ author or 'Community Contributors' }}}}"
  version: "{{{{ version or '1.0.0' }}}}"
  license: "{{{{ license or 'MIT' }}}}"
  
  project:
    description: "{{{{ description or '开源项目' }}}}"
    homepage: "{{{{ homepage or '' }}}}"
    repository: "{{{{ repository or '' }}}}"
    demo_url: "{{{{ demo_url or '' }}}}"
    keywords: ["open-source", "community"]

# 发布配置
release:
  # 自动发布
  automated: true
  platforms: ["github", "pypi", "npm"]
  
  # 发布检查
  pre_release_checks:
    - tests_pass
    - docs_updated
    - changelog_updated
    - version_bumped

# 社区配置
community:
  # 贡献者识别
  contributors:
    auto_detect: true
    include_all_contributors: true
  
  # 问题管理
  issues:
    labels: ["bug", "enhancement", "question", "help-wanted", "good-first-issue"]
    templates: true
  
  # 拉取请求
  pull_requests:
    template: true
    auto_assign_reviewers: true

# 质量保证 - 开源标准
quality:
  # CI/CD
  continuous_integration: true
  automated_testing: true
  code_coverage: true
  
  # 代码质量
  linting: true
  formatting: true
  security_scanning: true

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: ".gendocs/logs/opensource-docs.log"
"""
    
    def get_available_templates(self) -> List[Dict[str, str]]:
        """获取可用的配置模板列表
        
        Returns:
            配置模板信息列表
        """
        return [
            {
                "type": ConfigTemplateType.BASIC.value,
                "name": "基础模板",
                "description": "适用于简单项目的基础配置",
                "suitable_for": ["small projects", "personal projects"]
            },
            {
                "type": ConfigTemplateType.WEB_API.value,
                "name": "Web API模板",
                "description": "适用于Web API和RESTful服务项目",
                "suitable_for": ["FastAPI", "Django", "Flask", "REST APIs"]
            },
            {
                "type": ConfigTemplateType.MACHINE_LEARNING.value,
                "name": "机器学习模板",
                "description": "适用于机器学习和AI项目",
                "suitable_for": ["ML models", "AI projects", "data science"]
            },
            {
                "type": ConfigTemplateType.ENTERPRISE.value,
                "name": "企业级模板",
                "description": "适用于企业级应用，包含完整的治理和合规配置",
                "suitable_for": ["enterprise applications", "corporate projects"]
            },
            {
                "type": ConfigTemplateType.OPEN_SOURCE.value,
                "name": "开源项目模板",
                "description": "适用于开源项目，包含社区和贡献配置",
                "suitable_for": ["open source projects", "community projects"]
            }
        ]
    
    def detect_suitable_template(self, 
                                project_path: Path,
                                project_type: Optional[ProjectType] = None,
                                framework: Optional[FrameworkType] = None) -> ConfigTemplateType:
        """检测适合的配置模板类型
        
        Args:
            project_path: 项目路径
            project_type: 项目类型
            framework: 框架类型
            
        Returns:
            推荐的配置模板类型
        """
        try:
            # 检查是否为机器学习项目
            if self._is_ml_project(project_path):
                return ConfigTemplateType.MACHINE_LEARNING
            
            # 检查是否为Web API项目
            if framework in [FrameworkType.FASTAPI, FrameworkType.DJANGO, FrameworkType.FLASK]:
                return ConfigTemplateType.WEB_API
            
            # 检查是否为开源项目
            if self._is_opensource_project(project_path):
                return ConfigTemplateType.OPEN_SOURCE
            
            # 检查是否为企业级项目
            if self._is_enterprise_project(project_path):
                return ConfigTemplateType.ENTERPRISE
            
            # 默认返回基础模板
            return ConfigTemplateType.BASIC
            
        except Exception as e:
            self.logger.warning(f"检测配置模板类型失败: {e}")
            return ConfigTemplateType.BASIC
    
    def _is_ml_project(self, project_path: Path) -> bool:
        """检测是否为机器学习项目"""
        ml_indicators = [
            "requirements.txt",
            "environment.yml",
            "Pipfile"
        ]
        
        ml_keywords = [
            "tensorflow", "torch", "pytorch", "sklearn", "pandas",
            "numpy", "jupyter", "notebook", "model", "train"
        ]
        
        # 检查文件
        for indicator in ml_indicators:
            file_path = project_path / indicator
            if file_path.exists():
                try:
                    content = file_path.read_text(encoding='utf-8').lower()
                    if any(keyword in content for keyword in ml_keywords):
                        return True
                except:
                    continue
        
        # 检查目录结构
        ml_dirs = ["models", "notebooks", "data", "experiments"]
        ml_dir_count = sum(1 for dir_name in ml_dirs if (project_path / dir_name).exists())
        
        return ml_dir_count >= 2
    
    def _is_opensource_project(self, project_path: Path) -> bool:
        """检测是否为开源项目"""
        opensource_indicators = [
            "LICENSE", "LICENSE.txt", "LICENSE.md",
            "CONTRIBUTING.md", "CODE_OF_CONDUCT.md",
            ".github"
        ]
        
        return any((project_path / indicator).exists() for indicator in opensource_indicators)
    
    def _is_enterprise_project(self, project_path: Path) -> bool:
        """检测是否为企业级项目"""
        enterprise_indicators = [
            "docker-compose.yml", "Dockerfile",
            "kubernetes", "k8s",
            "terraform", "ansible",
            "monitoring", "observability"
        ]
        
        # 检查文件和目录
        for indicator in enterprise_indicators:
            if (project_path / indicator).exists():
                return True
        
        # 检查复杂的目录结构
        complex_dirs = ["src", "tests", "docs", "scripts", "deploy", "config"]
        complex_dir_count = sum(1 for dir_name in complex_dirs if (project_path / dir_name).exists())
        
        return complex_dir_count >= 4
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳
        
        Returns:
            格式化的时间戳字符串
        """
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")