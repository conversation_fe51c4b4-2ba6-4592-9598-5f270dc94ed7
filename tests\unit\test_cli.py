"""
CLI命令行接口单元测试
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from click.testing import CliRunner

# 假设CLI命令在这些模块中定义
from gendocs.cli.main import cli, generate_docs, init_config
from gendocs.cli.commands.batch import batch_command
from gendocs.cli.commands.monitor import monitor_command
from gendocs.cli.commands.config import config_command


class TestMainCLI:
    """主CLI测试"""
    
    def test_cli_group_exists(self):
        """测试CLI组存在"""
        assert cli is not None
        assert hasattr(cli, 'commands')
    
    def test_help_command(self):
        """测试帮助命令"""
        runner = CliRunner()
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert 'Usage:' in result.output
        assert 'Commands:' in result.output
    
    def test_version_command(self):
        """测试版本命令"""
        runner = CliRunner()
        result = runner.invoke(cli, ['--version'])
        
        # 根据实际实现调整期望结果
        assert result.exit_code == 0 or 'version' in result.output.lower()


class TestGenerateCommand:
    """生成文档命令测试"""
    
    def test_generate_docs_help(self):
        """测试生成文档命令帮助"""
        runner = CliRunner()
        result = runner.invoke(cli, ['generate', '--help'])
        
        assert result.exit_code == 0
        assert 'generate' in result.output.lower()
    
    @patch('gendocs.generators.EnterpriseDocumentManager')
    def test_generate_docs_basic(self, mock_manager, temp_dir):
        """测试基础文档生成"""
        # 创建临时项目目录
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        output_dir = temp_dir / "output"
        
        # Mock文档管理器
        mock_instance = Mock()
        mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
        mock_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'generate',
            str(project_dir),
            '--output', str(output_dir)
        ])
        
        # 验证命令执行
        assert result.exit_code == 0 or "Error" not in result.output
    
    @patch('gendocs.generators.EnterpriseDocumentManager')
    def test_generate_docs_with_config(self, mock_manager, temp_dir):
        """测试带配置的文档生成"""
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        config_file = temp_dir / "config.yaml"
        config_file.write_text("""
ai:
  provider: openai
  model: gpt-3.5-turbo
generation:
  output_dir: docs
""")
        
        mock_instance = Mock()
        mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
        mock_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'generate',
            str(project_dir),
            '--config', str(config_file)
        ])
        
        assert result.exit_code == 0 or "Error" not in result.output
    
    @patch('gendocs.generators.EnterpriseDocumentManager')
    def test_generate_docs_with_generators(self, mock_manager, temp_dir):
        """测试指定生成器的文档生成"""
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        mock_instance = Mock()
        mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
        mock_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'generate',
            str(project_dir),
            '--generators', 'overview,api'
        ])
        
        assert result.exit_code == 0 or "Error" not in result.output
    
    def test_generate_docs_invalid_project(self):
        """测试无效项目路径"""
        runner = CliRunner()
        result = runner.invoke(cli, [
            'generate',
            '/nonexistent/project'
        ])
        
        assert result.exit_code != 0
        assert 'error' in result.output.lower() or 'not found' in result.output.lower()
    
    @patch('gendocs.generators.EnterpriseDocumentManager')
    def test_generate_docs_with_ai_disabled(self, mock_manager, temp_dir):
        """测试禁用AI的文档生成"""
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        mock_instance = Mock()
        mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
        mock_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'generate',
            str(project_dir),
            '--no-ai'
        ])
        
        assert result.exit_code == 0 or "Error" not in result.output


class TestInitCommand:
    """初始化配置命令测试"""
    
    def test_init_config_help(self):
        """测试初始化配置命令帮助"""
        runner = CliRunner()
        result = runner.invoke(cli, ['init', '--help'])
        
        assert result.exit_code == 0
        assert 'init' in result.output.lower()
    
    def test_init_config_basic(self, temp_dir):
        """测试基础配置初始化"""
        runner = CliRunner()
        
        with runner.isolated_filesystem():
            result = runner.invoke(cli, ['init'])
            
            # 验证配置文件被创建
            assert result.exit_code == 0
            # 根据实际实现验证配置文件存在
    
    def test_init_config_with_path(self, temp_dir):
        """测试指定路径的配置初始化"""
        config_path = temp_dir / "custom_config.yaml"
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'init',
            '--config-path', str(config_path)
        ])
        
        assert result.exit_code == 0
        # 验证配置文件在指定路径创建
    
    def test_init_config_overwrite(self, temp_dir):
        """测试覆盖现有配置"""
        config_path = temp_dir / "config.yaml"
        config_path.write_text("existing: config")
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'init',
            '--config-path', str(config_path),
            '--force'
        ])
        
        assert result.exit_code == 0


class TestBatchCommand:
    """批量处理命令测试"""
    
    def test_batch_help(self):
        """测试批量处理命令帮助"""
        runner = CliRunner()
        result = runner.invoke(cli, ['batch', '--help'])
        
        assert result.exit_code == 0
        assert 'batch' in result.output.lower()
    
    @patch('gendocs.batch.BatchProcessor')
    def test_batch_start(self, mock_processor, temp_dir):
        """测试启动批量处理"""
        mock_instance = Mock()
        mock_instance.start_processing = AsyncMock()
        mock_processor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['batch', 'start'])
        
        assert result.exit_code == 0 or "started" in result.output.lower()
    
    @patch('gendocs.batch.BatchProcessor')
    def test_batch_stop(self, mock_processor):
        """测试停止批量处理"""
        mock_instance = Mock()
        mock_instance.stop_processing = AsyncMock()
        mock_processor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['batch', 'stop'])
        
        assert result.exit_code == 0 or "stopped" in result.output.lower()
    
    @patch('gendocs.batch.BatchProcessor')
    def test_batch_status(self, mock_processor):
        """测试批量处理状态"""
        mock_instance = Mock()
        mock_instance.get_queue_status.return_value = {
            'total_jobs': 5,
            'pending_jobs': 2,
            'running_jobs': 1,
            'completed_jobs': 2,
            'failed_jobs': 0
        }
        mock_processor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['batch', 'status'])
        
        assert result.exit_code == 0
        assert 'jobs' in result.output.lower()
    
    @patch('gendocs.batch.BatchProcessor')
    def test_batch_add_job(self, mock_processor, temp_dir):
        """测试添加批量作业"""
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        mock_instance = Mock()
        mock_instance.add_job = AsyncMock(return_value="job_123")
        mock_processor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'batch', 'add-job',
            str(project_dir),
            '--output', str(temp_dir / "output")
        ])
        
        assert result.exit_code == 0 or "job" in result.output.lower()


class TestMonitorCommand:
    """监控命令测试"""
    
    def test_monitor_help(self):
        """测试监控命令帮助"""
        runner = CliRunner()
        result = runner.invoke(cli, ['monitor', '--help'])
        
        assert result.exit_code == 0
        assert 'monitor' in result.output.lower()
    
    @patch('gendocs.monitoring.PerformanceMonitor')
    def test_monitor_status(self, mock_monitor):
        """测试监控状态"""
        mock_instance = Mock()
        mock_instance.get_current_status.return_value = {
            'monitoring_active': True,
            'performance_summary': {
                'documents_generated': 10,
                'ai_requests': 25
            }
        }
        mock_monitor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['monitor', 'status'])
        
        assert result.exit_code == 0
        assert 'monitoring' in result.output.lower() or 'status' in result.output.lower()
    
    @patch('gendocs.monitoring.PerformanceMonitor')
    def test_monitor_start(self, mock_monitor):
        """测试启动监控"""
        mock_instance = Mock()
        mock_instance.start_monitoring = Mock()
        mock_monitor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['monitor', 'start'])
        
        assert result.exit_code == 0
    
    @patch('gendocs.monitoring.PerformanceMonitor')
    def test_monitor_report(self, mock_monitor, temp_dir):
        """测试生成监控报告"""
        mock_instance = Mock()
        mock_instance.generate_report_now.return_value = temp_dir / "report.html"
        mock_monitor.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'monitor', 'report',
            '--output', str(temp_dir / "report.html")
        ])
        
        assert result.exit_code == 0


class TestConfigCommand:
    """配置命令测试"""
    
    def test_config_help(self):
        """测试配置命令帮助"""
        runner = CliRunner()
        result = runner.invoke(cli, ['config', '--help'])
        
        assert result.exit_code == 0
        assert 'config' in result.output.lower()
    
    @patch('gendocs.config.ConfigManager')
    def test_config_show(self, mock_config_manager):
        """测试显示配置"""
        mock_instance = Mock()
        mock_instance.get_config.return_value = {
            'ai': {'provider': 'openai'},
            'generation': {'output_dir': 'docs'}
        }
        mock_config_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['config', 'show'])
        
        assert result.exit_code == 0
        assert 'ai' in result.output or 'provider' in result.output
    
    @patch('gendocs.config.ConfigManager')
    def test_config_set(self, mock_config_manager):
        """测试设置配置值"""
        mock_instance = Mock()
        mock_instance.set_config_value = Mock()
        mock_config_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'config', 'set',
            'ai.provider', 'anthropic'
        ])
        
        assert result.exit_code == 0
        mock_instance.set_config_value.assert_called_with('ai.provider', 'anthropic')
    
    @patch('gendocs.config.ConfigManager')
    def test_config_get(self, mock_config_manager):
        """测试获取配置值"""
        mock_instance = Mock()
        mock_instance.get_config_value.return_value = 'openai'
        mock_config_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            'config', 'get',
            'ai.provider'
        ])
        
        assert result.exit_code == 0
        assert 'openai' in result.output
    
    @patch('gendocs.config.ConfigManager')
    def test_config_validate(self, mock_config_manager):
        """测试验证配置"""
        mock_instance = Mock()
        mock_instance.validate_config.return_value = True
        mock_config_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, ['config', 'validate'])
        
        assert result.exit_code == 0
        assert 'valid' in result.output.lower() or 'success' in result.output.lower()


class TestCLIIntegration:
    """CLI集成测试"""
    
    def test_cli_command_registration(self):
        """测试CLI命令注册"""
        # 验证所有主要命令都已注册
        command_names = list(cli.commands.keys())
        
        expected_commands = ['generate', 'init', 'batch', 'monitor', 'config']
        for cmd in expected_commands:
            assert cmd in command_names or cmd in str(cli.commands)
    
    def test_global_options(self):
        """测试全局选项"""
        runner = CliRunner()
        
        # 测试 --verbose 选项
        result = runner.invoke(cli, ['--verbose', '--help'])
        assert result.exit_code == 0
        
        # 测试 --quiet 选项
        result = runner.invoke(cli, ['--quiet', '--help'])
        assert result.exit_code == 0
    
    @patch('gendocs.config.ConfigManager')
    def test_config_file_loading(self, mock_config_manager, temp_dir):
        """测试配置文件加载"""
        config_file = temp_dir / "test_config.yaml"
        config_file.write_text("""
ai:
  provider: test_provider
generation:
  output_dir: test_output
""")
        
        mock_instance = Mock()
        mock_config_manager.return_value = mock_instance
        
        runner = CliRunner()
        result = runner.invoke(cli, [
            '--config', str(config_file),
            'config', 'show'
        ])
        
        # 验证配置文件被加载
        mock_instance.load_config.assert_called()
    
    def test_error_handling(self):
        """测试错误处理"""
        runner = CliRunner()
        
        # 测试无效命令
        result = runner.invoke(cli, ['invalid_command'])
        assert result.exit_code != 0
        
        # 测试无效选项
        result = runner.invoke(cli, ['--invalid-option'])
        assert result.exit_code != 0
    
    @patch('gendocs.generators.EnterpriseDocumentManager')
    def test_end_to_end_workflow(self, mock_manager, temp_dir):
        """测试端到端工作流"""
        # 创建测试项目
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        output_dir = temp_dir / "output"
        
        mock_instance = Mock()
        mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
        mock_manager.return_value = mock_instance
        
        runner = CliRunner()
        
        # 1. 初始化配置
        result = runner.invoke(cli, ['init'])
        
        # 2. 生成文档
        result = runner.invoke(cli, [
            'generate',
            str(project_dir),
            '--output', str(output_dir)
        ])
        
        # 验证整个流程
        assert result.exit_code == 0 or "Error" not in result.output
    
    def test_cli_async_command_handling(self, temp_dir):
        """测试CLI异步命令处理"""
        project_dir = temp_dir / "test_project"
        project_dir.mkdir()
        (project_dir / "main.py").write_text("print('Hello, World!')")
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            runner = CliRunner()
            result = runner.invoke(cli, [
                'generate',
                str(project_dir)
            ])
            
            # CLI应该能够正确处理异步操作
            assert result.exit_code is not None
    
    @patch('gendocs.utils.logger.setup_logging')
    def test_logging_integration(self, mock_setup_logging):
        """测试日志集成"""
        mock_logger = Mock()
        mock_setup_logging.return_value = mock_logger
        
        runner = CliRunner()
        result = runner.invoke(cli, ['--verbose', '--help'])
        
        # 验证日志设置被调用
        assert result.exit_code == 0
    
    def test_cli_signal_handling(self):
        """测试CLI信号处理"""
        # 这个测试可能需要特殊的设置来模拟信号
        # 主要验证CLI能够优雅地处理中断信号
        runner = CliRunner()
        
        # 基本测试：确保CLI能够正常启动和退出
        result = runner.invoke(cli, ['--help'])
        assert result.exit_code == 0