"""
性能分析器模块

提供代码执行性能分析和热点检测功能。
"""

import cProfile
import pstats
import io
import time
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from contextlib import contextmanager
from functools import wraps
import threading
import tracemalloc


@dataclass
class ProfilerConfig:
    """性能分析器配置"""
    enable_profiling: bool = False
    enable_memory_profiling: bool = False
    profile_output_dir: Path = Path(".gendocs/profiles")
    max_profile_files: int = 10
    profile_top_functions: int = 20
    
    # 采样配置
    sample_rate: float = 1.0  # 采样率 (0.0-1.0)
    min_execution_time: float = 0.001  # 最小执行时间阈值(秒)


class FunctionProfiler:
    """函数性能分析器"""
    
    def __init__(self, config: ProfilerConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._profiles: Dict[str, Dict] = {}
        self._lock = threading.Lock()
    
    def profile_function(self, func_name: str = None):
        """函数性能分析装饰器
        
        Args:
            func_name: 函数名称，默认使用函数的__name__
        """
        def decorator(func: Callable) -> Callable:
            if not self.config.enable_profiling:
                return func
            
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                if self.config.sample_rate < 1.0:
                    import random
                    if random.random() > self.config.sample_rate:
                        return func(*args, **kwargs)
                
                start_time = time.time()
                
                # 内存分析
                if self.config.enable_memory_profiling:
                    tracemalloc.start()
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    if duration >= self.config.min_execution_time:
                        # 记录性能数据
                        with self._lock:
                            if name not in self._profiles:
                                self._profiles[name] = {
                                    'call_count': 0,
                                    'total_time': 0.0,
                                    'max_time': 0.0,
                                    'min_time': float('inf'),
                                    'memory_peak': 0
                                }
                            
                            profile = self._profiles[name]
                            profile['call_count'] += 1
                            profile['total_time'] += duration
                            profile['max_time'] = max(profile['max_time'], duration)
                            profile['min_time'] = min(profile['min_time'], duration)
                            
                            # 内存使用
                            if self.config.enable_memory_profiling:
                                try:
                                    current, peak = tracemalloc.get_traced_memory()
                                    profile['memory_peak'] = max(profile['memory_peak'], peak)
                                    tracemalloc.stop()
                                except:
                                    pass
            
            return wrapper
        return decorator
    
    def get_profile_stats(self) -> Dict[str, Dict]:
        """获取性能统计数据"""
        with self._lock:
            stats = {}
            for name, profile in self._profiles.items():
                if profile['call_count'] > 0:
                    stats[name] = {
                        'call_count': profile['call_count'],
                        'total_time': profile['total_time'],
                        'average_time': profile['total_time'] / profile['call_count'],
                        'max_time': profile['max_time'],
                        'min_time': profile['min_time'],
                        'memory_peak_mb': profile['memory_peak'] / (1024 * 1024)
                    }
            return stats
    
    def reset_profiles(self) -> None:
        """重置性能数据"""
        with self._lock:
            self._profiles.clear()


class ProfileManager:
    """性能分析管理器"""
    
    def __init__(self, config: ProfilerConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.function_profiler = FunctionProfiler(config)
        
        # 确保输出目录存在
        self.config.profile_output_dir.mkdir(parents=True, exist_ok=True)
    
    @contextmanager
    def profile_context(self, name: str):
        """性能分析上下文管理器
        
        Args:
            name: 分析名称
        """
        if not self.config.enable_profiling:
            yield
            return
        
        profiler = cProfile.Profile()
        
        self.logger.info(f"开始性能分析: {name}")
        
        # 内存分析
        if self.config.enable_memory_profiling:
            tracemalloc.start()
        
        start_time = time.time()
        profiler.enable()
        
        try:
            yield
        finally:
            profiler.disable()
            end_time = time.time()
            
            duration = end_time - start_time
            self.logger.info(f"性能分析完成: {name}, 耗时: {duration:.3f}秒")
            
            # 保存分析结果
            self._save_profile_result(profiler, name, duration)
            
            # 内存分析结果
            if self.config.enable_memory_profiling:
                try:
                    current, peak = tracemalloc.get_traced_memory()
                    self.logger.info(f"内存使用 - 当前: {current / 1024 / 1024:.2f}MB, 峰值: {peak / 1024 / 1024:.2f}MB")
                    tracemalloc.stop()
                except:
                    pass
    
    def _save_profile_result(self, profiler: cProfile.Profile, name: str, duration: float) -> None:
        """保存性能分析结果"""
        try:
            timestamp = int(time.time())
            
            # 生成文本报告
            stats_buffer = io.StringIO()
            stats = pstats.Stats(profiler, stream=stats_buffer)
            stats.sort_stats('cumulative')
            stats.print_stats(self.config.profile_top_functions)
            
            # 保存文本报告
            text_file = self.config.profile_output_dir / f"{name}_{timestamp}.txt"
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write(f"性能分析报告: {name}\n")
                f.write(f"执行时间: {duration:.3f}秒\n")
                f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
                f.write(stats_buffer.getvalue())
            
            # 保存二进制文件(可用于进一步分析)
            profile_file = self.config.profile_output_dir / f"{name}_{timestamp}.prof"
            profiler.dump_stats(str(profile_file))
            
            self.logger.info(f"性能分析结果已保存: {text_file}")
            
            # 清理旧文件
            self._cleanup_old_profiles()
            
        except Exception as e:
            self.logger.error(f"保存性能分析结果失败: {e}")
    
    def _cleanup_old_profiles(self) -> None:
        """清理旧的性能分析文件"""
        try:
            # 获取所有profile文件
            profile_files = list(self.config.profile_output_dir.glob("*.prof"))
            text_files = list(self.config.profile_output_dir.glob("*.txt"))
            
            all_files = profile_files + text_files
            
            if len(all_files) > self.config.max_profile_files * 2:  # *2 因为每次生成两个文件
                # 按修改时间排序，删除最旧的文件
                all_files.sort(key=lambda f: f.stat().st_mtime)
                
                files_to_delete = len(all_files) - self.config.max_profile_files * 2
                for file_to_delete in all_files[:files_to_delete]:
                    file_to_delete.unlink()
                    self.logger.debug(f"删除旧的性能分析文件: {file_to_delete}")
        
        except Exception as e:
            self.logger.error(f"清理旧性能分析文件失败: {e}")
    
    def analyze_profile_file(self, profile_file: Path) -> Dict[str, Any]:
        """分析性能分析文件
        
        Args:
            profile_file: 性能分析文件路径
            
        Returns:
            分析结果
        """
        try:
            stats = pstats.Stats(str(profile_file))
            
            # 获取总体统计
            total_calls = stats.total_calls
            total_time = stats.total_tt
            
            # 获取顶部函数
            stats.sort_stats('cumulative')
            top_functions = []
            
            for func_info, (cc, nc, tt, ct, callers) in list(stats.stats.items())[:self.config.profile_top_functions]:
                filename, line_num, func_name = func_info
                top_functions.append({
                    'function': f"{filename}:{line_num}({func_name})",
                    'calls': cc,
                    'total_time': tt,
                    'cumulative_time': ct,
                    'per_call': tt / cc if cc > 0 else 0
                })
            
            return {
                'total_calls': total_calls,
                'total_time': total_time,
                'top_functions': top_functions
            }
        
        except Exception as e:
            self.logger.error(f"分析性能分析文件失败: {e}")
            return {}
    
    def get_all_profile_summaries(self) -> List[Dict[str, Any]]:
        """获取所有性能分析摘要
        
        Returns:
            性能分析摘要列表
        """
        summaries = []
        
        try:
            for profile_file in self.config.profile_output_dir.glob("*.prof"):
                try:
                    # 从文件名解析信息
                    name_parts = profile_file.stem.split('_')
                    if len(name_parts) >= 2:
                        name = '_'.join(name_parts[:-1])
                        timestamp = int(name_parts[-1])
                        
                        analysis = self.analyze_profile_file(profile_file)
                        if analysis:
                            summaries.append({
                                'name': name,
                                'timestamp': timestamp,
                                'file_path': str(profile_file),
                                'total_calls': analysis.get('total_calls', 0),
                                'total_time': analysis.get('total_time', 0.0),
                                'top_function': analysis.get('top_functions', [{}])[0].get('function', ''),
                                'file_size': profile_file.stat().st_size
                            })
                except Exception as e:
                    self.logger.warning(f"处理性能分析文件失败 {profile_file}: {e}")
            
            # 按时间戳排序
            summaries.sort(key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"获取性能分析摘要失败: {e}")
        
        return summaries
    
    def profile_decorator(self, name: str = None):
        """性能分析装饰器
        
        Args:
            name: 分析名称
        """
        def decorator(func: Callable) -> Callable:
            analysis_name = name or f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self.profile_context(analysis_name):
                    return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    def get_function_profiles(self) -> Dict[str, Dict]:
        """获取函数性能统计"""
        return self.function_profiler.get_profile_stats()
    
    def reset_function_profiles(self) -> None:
        """重置函数性能统计"""
        self.function_profiler.reset_profiles()


# 便捷的装饰器函数
def profile_function(name: str = None, config: Optional[ProfilerConfig] = None):
    """性能分析装饰器函数
    
    Args:
        name: 分析名称
        config: 分析器配置
    """
    if config is None:
        config = ProfilerConfig()
    
    manager = ProfileManager(config)
    return manager.profile_decorator(name)


def profile_memory(func: Callable) -> Callable:
    """内存使用分析装饰器
    
    Args:
        func: 要分析的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        tracemalloc.start()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            logger = logging.getLogger(func.__module__)
            logger.info(
                f"内存使用 {func.__name__}: "
                f"当前={current / 1024 / 1024:.2f}MB, "
                f"峰值={peak / 1024 / 1024:.2f}MB"
            )
    
    return wrapper