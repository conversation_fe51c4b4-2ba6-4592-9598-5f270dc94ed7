# GenDocs 开发指南

## 开发环境设置

### 系统要求

- Python 3.8 或更高版本
- Git版本控制系统
- 推荐使用虚拟环境

### 克隆项目

```bash
git clone <repository-url>
cd gendocs
```

### 创建虚拟环境

```bash
# 使用venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 或使用conda
conda create -n gendocs python=3.8
conda activate gendocs
```

### 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt

# 开发模式安装
pip install -e .
```

### 开发依赖说明

- `pytest` - 测试框架
- `pytest-asyncio` - 异步测试支持
- `black` - 代码格式化
- `flake8` - 代码检查
- `mypy` - 类型检查
- `pre-commit` - Git钩子管理

## 项目结构

```
gendocs/
├── src/gendocs/           # 主要源代码
│   ├── __init__.py
│   ├── __main__.py        # 命令行入口
│   ├── ai/                # AI集成模块
│   ├── analyzers/         # 项目分析器
│   ├── batch/             # 批量处理
│   ├── cli/               # 命令行界面
│   ├── config/            # 配置管理
│   ├── generators/        # 文档生成器
│   ├── monitoring/        # 性能监控
│   └── utils/             # 工具函数
├── templates/             # 模板文件
├── tests/                 # 测试代码
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── conftest.py        # 测试配置
├── docs/                  # 项目文档
├── requirements.txt       # 生产依赖
├── requirements-dev.txt   # 开发依赖
├── setup.py              # 安装配置
├── pytest.ini           # 测试配置
├── .pre-commit-config.yaml # 代码质量检查
└── README.md             # 项目说明
```

## 编码规范

### Python编码标准

遵循PEP 8标准，具体要求：

1. **缩进**: 使用4个空格
2. **行长度**: 最大88字符（Black格式化器标准）
3. **导入顺序**: 标准库 -> 第三方库 -> 本地模块
4. **命名规范**:
   - 类名: `PascalCase`
   - 函数/变量: `snake_case`
   - 常量: `UPPER_CASE`
   - 私有方法: `_leading_underscore`

### 代码格式化

使用Black进行自动格式化：

```bash
# 格式化所有Python文件
black src/ tests/

# 检查格式（不修改文件）
black --check src/ tests/
```

### 代码检查

使用flake8进行代码质量检查：

```bash
# 运行代码检查
flake8 src/ tests/

# 配置在setup.cfg中
```

### 类型检查

使用mypy进行类型检查：

```bash
# 运行类型检查
mypy src/gendocs

# 配置在mypy.ini中
```

## 开发工作流

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发功能
# 编写代码...

# 3. 运行测试
pytest

# 4. 代码质量检查
black src/ tests/
flake8 src/ tests/
mypy src/gendocs

# 5. 提交代码
git add .
git commit -m "feat: add new feature"

# 6. 推送分支
git push origin feature/new-feature

# 7. 创建Pull Request
```

### 2. Git提交规范

使用Conventional Commits规范：

- `feat:` 新功能
- `fix:` 修复问题
- `docs:` 文档更新
- `style:` 代码格式（不影响功能）
- `refactor:` 重构代码
- `test:` 添加测试
- `chore:` 构建过程或辅助工具的变动

示例：
```bash
git commit -m "feat: add batch processing support"
git commit -m "fix: handle empty project directories"
git commit -m "docs: update API reference"
```

### 3. Pre-commit钩子

设置pre-commit钩子自动检查代码质量：

```bash
# 安装pre-commit
pip install pre-commit

# 安装钩子
pre-commit install

# 手动运行所有钩子
pre-commit run --all-files
```

## 测试指南

### 测试结构

```
tests/
├── conftest.py           # 测试配置和fixtures
├── unit/                 # 单元测试
│   ├── test_config.py
│   ├── test_analyzers.py
│   ├── test_generators.py
│   ├── test_monitoring.py
│   ├── test_batch.py
│   ├── test_utils.py
│   └── test_cli.py
└── integration/          # 集成测试
    └── test_full_workflow.py
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/unit/test_config.py

# 运行特定测试方法
pytest tests/unit/test_config.py::TestConfigManager::test_load_config

# 运行带覆盖率报告
pytest --cov=src/gendocs --cov-report=html

# 运行并发测试
pytest -n auto

# 详细输出
pytest -v

# 只运行失败的测试
pytest --lf
```

### 编写测试

#### 单元测试示例

```python
import pytest
from unittest.mock import Mock, patch
from gendocs.config import ConfigManager

class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_config_success(self, temp_dir):
        """测试成功加载配置"""
        # 准备测试数据
        config_file = temp_dir / "test_config.yaml"
        config_file.write_text("""
ai:
  provider: test_provider
generation:
  output_dir: test_output
""")
        
        # 执行测试
        manager = ConfigManager()
        result = manager.load_config(config_file)
        
        # 验证结果
        assert result is True
        assert manager.get_config_value("ai.provider") == "test_provider"
    
    @patch('gendocs.config.yaml.safe_load')
    def test_load_config_yaml_error(self, mock_yaml_load):
        """测试YAML解析错误"""
        # 模拟YAML解析错误
        mock_yaml_load.side_effect = yaml.YAMLError("Invalid YAML")
        
        manager = ConfigManager()
        
        with pytest.raises(ConfigurationError):
            manager.load_config("invalid_config.yaml")
```

#### 异步测试示例

```python
import pytest
import asyncio
from unittest.mock import AsyncMock

class TestAsyncGenerator:
    """异步生成器测试"""
    
    @pytest.mark.asyncio
    async def test_generate_document(self, mock_project_dir):
        """测试异步文档生成"""
        # 准备Mock对象
        mock_ai_provider = Mock()
        mock_ai_provider.generate_content = AsyncMock(
            return_value="Generated content"
        )
        
        # 执行异步测试
        generator = DocumentGenerator(ai_provider=mock_ai_provider)
        result = await generator.generate(
            mock_project_dir, 
            output_path="test.md",
            context={"test": "data"}
        )
        
        # 验证结果
        assert result is True
        mock_ai_provider.generate_content.assert_called_once()
```

#### 使用Fixtures

```python
# conftest.py中定义的fixtures
@pytest.fixture
def config_manager():
    """创建测试用配置管理器"""
    manager = ConfigManager()
    manager.load_defaults()
    return manager

@pytest.fixture
def mock_project_dir(temp_dir):
    """创建模拟项目目录"""
    project_dir = temp_dir / "test_project"
    project_dir.mkdir()
    (project_dir / "main.py").write_text("print('Hello, World!')")
    return project_dir

# 在测试中使用fixtures
def test_with_fixtures(config_manager, mock_project_dir):
    """使用fixtures的测试"""
    assert config_manager is not None
    assert mock_project_dir.exists()
```

### 测试最佳实践

1. **测试命名**: 使用描述性的测试名称
2. **AAA模式**: Arrange（准备）、Act（执行）、Assert（验证）
3. **Mock外部依赖**: 使用Mock模拟外部服务
4. **测试隔离**: 每个测试独立，不依赖其他测试
5. **边界条件**: 测试正常情况和边界情况
6. **异常处理**: 测试错误处理逻辑

## 新功能开发

### 添加新的分析器

1. **创建分析器类**:

```python
# src/gendocs/analyzers/javascript_analyzer.py
from pathlib import Path
from .base import BaseAnalyzer, ProjectAnalysisResult
from .types import ProjectType, FrameworkType

class JavaScriptAnalyzer(BaseAnalyzer):
    """JavaScript项目分析器"""
    
    def can_analyze(self, project_path: Path) -> bool:
        """判断是否能分析JavaScript项目"""
        package_json = project_path / "package.json"
        js_files = list(project_path.glob("*.js"))
        return package_json.exists() or len(js_files) > 0
    
    def analyze(self, project_path: Path) -> ProjectAnalysisResult:
        """分析JavaScript项目"""
        # 实现分析逻辑
        pass
```

2. **注册分析器**:

```python
# src/gendocs/analyzers/__init__.py
from .registry import AnalyzerRegistry
from .javascript_analyzer import JavaScriptAnalyzer

def get_registry() -> AnalyzerRegistry:
    """获取分析器注册表"""
    registry = AnalyzerRegistry()
    # 注册所有分析器
    registry.register(JavaScriptAnalyzer())
    return registry
```

3. **编写测试**:

```python
# tests/unit/test_javascript_analyzer.py
import pytest
from gendocs.analyzers.javascript_analyzer import JavaScriptAnalyzer

class TestJavaScriptAnalyzer:
    """JavaScript分析器测试"""
    
    def test_can_analyze_js_project(self, temp_dir):
        """测试能否分析JavaScript项目"""
        # 创建测试项目
        js_project = temp_dir / "js_project"
        js_project.mkdir()
        (js_project / "package.json").write_text('{"name": "test"}')
        
        # 测试分析器
        analyzer = JavaScriptAnalyzer()
        assert analyzer.can_analyze(js_project) is True
```

### 添加新的生成器

1. **创建生成器类**:

```python
# src/gendocs/generators/enterprise/security_generator.py
from pathlib import Path
from .base import EnterpriseGenerator

class SecurityGenerator(EnterpriseGenerator):
    """安全文档生成器"""
    
    template_name = "security_guide.md.j2"
    
    async def generate(self, project_path: Path, 
                      output_path: Path, context: dict) -> bool:
        """生成安全文档"""
        try:
            # 获取项目分析结果
            analysis_result = self.get_project_analysis(project_path)
            
            # 构建模板上下文
            template_context = self.build_enterprise_context(
                project_path, analysis_result
            )
            
            # 添加安全相关上下文
            template_context.update({
                "security_checklist": self._generate_security_checklist(analysis_result),
                "dependencies_audit": self._audit_dependencies(analysis_result),
                "security_recommendations": self._get_security_recommendations(analysis_result)
            })
            
            # 渲染模板
            content = self.render_template(
                self.get_template_path(self.template_name),
                template_context
            )
            
            # AI增强
            if self.ai_provider:
                content = await self.enhance_with_ai_insights(
                    content, "security_analysis", template_context
                )
            
            # 写入文件
            output_path.parent.mkdir(parents=True, exist_ok=True)
            output_path.write_text(content, encoding='utf-8')
            
            return True
            
        except Exception as e:
            self.logger.error(f"生成安全文档失败: {e}")
            return False
    
    def _generate_security_checklist(self, analysis_result):
        """生成安全检查清单"""
        # 实现安全检查逻辑
        pass
```

2. **注册生成器**:

```python
# src/gendocs/generators/enterprise/__init__.py
from .security_generator import SecurityGenerator

ENTERPRISE_GENERATORS = {
    "security": {
        "class": SecurityGenerator,
        "description": "生成安全指南文档",
        "output_filename": "security/guide.md"
    }
}
```

3. **创建模板**:

```jinja2
<!-- templates/base/security_guide.md.j2 -->
# {{ project_name }} 安全指南

## 安全概览

本文档提供了 {{ project_name }} 项目的安全指南和最佳实践。

### 项目信息
- **项目类型**: {{ project_type }}
- **框架**: {{ framework }}
- **版本**: {{ version }}

## 安全检查清单

{% for item in security_checklist %}
- [ ] {{ item.title }}: {{ item.description }}
{% endfor %}

## 依赖审计

{% for dep in dependencies_audit %}
### {{ dep.name }}
- **版本**: {{ dep.version }}
- **安全状态**: {{ dep.security_status }}
{% if dep.vulnerabilities %}
- **已知漏洞**: {{ dep.vulnerabilities|length }}
{% endif %}
{% endfor %}

## 安全建议

{% for recommendation in security_recommendations %}
### {{ recommendation.category }}
{{ recommendation.description }}

{% if recommendation.code_example %}
```{{ recommendation.language }}
{{ recommendation.code_example }}
```
{% endif %}
{% endfor %}
```

### 添加CLI命令

1. **创建命令模块**:

```python
# src/gendocs/cli/commands/security.py
import click
from pathlib import Path
from ..main import cli

@cli.group()
def security():
    """安全相关命令"""
    pass

@security.command()
@click.argument('project_path', type=click.Path(exists=True))
@click.option('--output', '-o', help='输出文件路径')
def audit(project_path, output):
    """执行安全审计"""
    from gendocs.generators.enterprise.security_generator import SecurityGenerator
    
    project_path = Path(project_path)
    output_path = Path(output) if output else project_path / "security_audit.md"
    
    # 执行安全审计逻辑
    click.echo(f"正在审计项目: {project_path}")
    # ...
    click.echo(f"审计报告已生成: {output_path}")
```

2. **注册命令**:

```python
# src/gendocs/cli/main.py
from .commands import security  # 导入安全命令模块
```

## 调试指南

### 启用调试模式

```bash
# 设置日志级别为DEBUG
export GENDOCS_LOG_LEVEL=DEBUG

# 启用详细输出
gendocs generate /path/to/project --verbose

# 启用性能分析
gendocs generate /path/to/project --profile
```

### 使用调试器

```python
# 在代码中添加断点
import pdb; pdb.set_trace()

# 或使用ipdb（更友好的调试器）
import ipdb; ipdb.set_trace()

# 在pytest中调试
pytest --pdb tests/unit/test_config.py::test_specific_function
```

### 日志调试

```python
import logging

# 获取模块logger
logger = logging.getLogger(__name__)

# 添加调试信息
logger.debug(f"配置值: {config_value}")
logger.info(f"处理项目: {project_path}")
logger.warning(f"配置项缺失: {missing_key}")
logger.error(f"处理失败: {error}")
```

## 性能优化

### 性能分析

```bash
# 使用cProfile分析性能
python -m cProfile -o profile.stats -m gendocs generate /path/to/project

# 分析结果
python -c "
import pstats
stats = pstats.Stats('profile.stats')
stats.sort_stats('cumulative')
stats.print_stats(20)
"
```

### 内存分析

```bash
# 安装memory_profiler
pip install memory_profiler

# 分析内存使用
python -m memory_profiler -m gendocs generate /path/to/project
```

### 异步性能优化

```python
import asyncio
import aiofiles

# 使用异步文件操作
async def write_file_async(path: Path, content: str):
    async with aiofiles.open(path, 'w', encoding='utf-8') as f:
        await f.write(content)

# 并发处理多个任务
async def process_multiple_projects(projects):
    tasks = [process_project(project) for project in projects]
    results = await asyncio.gather(*tasks)
    return results
```

## 发布流程

### 版本管理

1. **更新版本号**:

```python
# src/gendocs/__init__.py
__version__ = "1.2.0"
```

2. **更新CHANGELOG**:

```markdown
# Changelog

## [1.2.0] - 2024-01-15

### Added
- 新增安全文档生成器
- 添加JavaScript项目分析支持

### Fixed
- 修复配置文件解析问题
- 改进错误处理机制

### Changed
- 优化批量处理性能
- 更新AI提示词
```

3. **创建发布标签**:

```bash
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin v1.2.0
```

### 构建和发布

```bash
# 清理构建文件
rm -rf build/ dist/ *.egg-info/

# 构建包
python setup.py sdist bdist_wheel

# 检查包
twine check dist/*

# 上传到PyPI（测试环境）
twine upload --repository-url https://test.pypi.org/legacy/ dist/*

# 上传到PyPI（生产环境）
twine upload dist/*
```

## 常见问题解决

### 导入错误

```python
# 问题：循环导入
# 解决：延迟导入或重构模块结构

def get_analyzer():
    from .analyzers import PythonAnalyzer  # 延迟导入
    return PythonAnalyzer()
```

### 异步编程问题

```python
# 问题：在同步上下文中调用异步函数
# 错误：
# result = async_function()

# 正确：
result = asyncio.run(async_function())

# 或在异步上下文中：
result = await async_function()
```

### 测试问题

```python
# 问题：测试之间相互影响
# 解决：使用proper fixtures和teardown

@pytest.fixture(autouse=True)
def reset_state():
    """每个测试前重置状态"""
    # 重置全局状态
    yield
    # 清理资源
```

## 贡献指南

### 报告问题

1. 使用GitHub Issues报告bug
2. 提供详细的复现步骤
3. 包含错误日志和系统信息
4. 使用适当的标签分类

### 提交代码

1. Fork项目到个人仓库
2. 创建功能分支
3. 编写代码和测试
4. 确保所有测试通过
5. 创建Pull Request
6. 等待代码审查

### 代码审查

审查要点：
- 代码风格是否符合规范
- 测试覆盖是否充分
- 是否有潜在的性能问题
- 文档是否需要更新
- API设计是否合理

---

本开发指南提供了GenDocs项目开发的全面指导，帮助开发者快速上手并贡献高质量的代码。