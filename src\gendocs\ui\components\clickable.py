from textual.widget import Widget
from textual.reactive import Reactive
from textual.message import Message

class Clickable(Widget):
    """可点击组件基类"""
    hovered = Reactive(False)

    async def on_enter(self) -> None:
        """鼠标悬停事件"""
        self.hovered = True

    async def on_leave(self) -> None:
        """鼠标离开事件"""
        self.hovered = False

    async def on_click(self) -> None:
        """点击事件"""
        pass

class Checkbox(Clickable):
    """支持鼠标的复选框"""
    checked = Reactive(False)

    async def on_click(self) -> None:
        self.checked = not self.checked 