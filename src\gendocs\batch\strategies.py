"""
批量处理策略模块

实现不同的批量处理策略。
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Callable, Any, Optional
from dataclasses import dataclass

from .processor import ProcessingResult, ProcessingStatus


class ProcessingStrategy(ABC):
    """处理策略基类"""
    
    @abstractmethod
    async def process_batch(self, 
                          projects: List[Any],
                          process_func: Callable,
                          config: Any) -> List[ProcessingResult]:
        """批量处理项目
        
        Args:
            projects: 项目列表
            process_func: 处理函数
            config: 配置对象
            
        Returns:
            处理结果列表
        """
        pass


class SequentialStrategy(ProcessingStrategy):
    """顺序处理策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def process_batch(self, 
                          projects: List[Any],
                          process_func: Callable,
                          config: Any) -> List[ProcessingResult]:
        """顺序处理项目"""
        results = []
        
        for i, project in enumerate(projects):
            self.logger.info(f"处理项目 {i+1}/{len(projects)}: {project.path}")
            
            try:
                result = await process_func(project)
                results.append(result)
                
                # 检查是否快速失败
                if config.fail_fast and not result.success:
                    self.logger.error(f"项目处理失败，启用快速失败模式: {project.path}")
                    # 为剩余项目创建跳过状态的结果
                    for remaining_project in projects[i+1:]:
                        skipped_result = ProcessingResult(
                            project_path=remaining_project.path,
                            status=ProcessingStatus.SKIPPED,
                            error_message="由于快速失败模式，项目被跳过"
                        )
                        results.append(skipped_result)
                    break
                
                # 检查是否继续处理错误项目
                if not config.continue_on_error and not result.success:
                    self.logger.error(f"项目处理失败，停止处理: {project.path}")
                    break
                
            except Exception as e:
                self.logger.error(f"处理项目时发生异常: {project.path}, 错误: {e}")
                error_result = ProcessingResult(
                    project_path=project.path,
                    status=ProcessingStatus.FAILED,
                    error_message=str(e)
                )
                results.append(error_result)
                
                if config.fail_fast or not config.continue_on_error:
                    break
        
        return results


class ConcurrentStrategy(ProcessingStrategy):
    """并发处理策略"""
    
    def __init__(self, max_concurrent: int = 3):
        self.max_concurrent = max_concurrent
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def process_batch(self, 
                          projects: List[Any],
                          process_func: Callable,
                          config: Any) -> List[ProcessingResult]:
        """并发处理项目"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        results = []
        
        async def _process_with_semaphore(project):
            async with semaphore:
                return await process_func(project)
        
        # 创建并发任务
        tasks = []
        for project in projects:
            task = asyncio.create_task(_process_with_semaphore(project))
            tasks.append(task)
        
        # 等待所有任务完成
        try:
            if config.fail_fast:
                # 快速失败模式：任何一个失败就取消所有任务
                results = await self._process_with_fail_fast(tasks, projects)
            else:
                # 正常模式：等待所有任务完成
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理异常结果
                processed_results = []
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_result = ProcessingResult(
                            project_path=projects[i].path,
                            status=ProcessingStatus.FAILED,
                            error_message=str(result)
                        )
                        processed_results.append(error_result)
                    else:
                        processed_results.append(result)
                
                results = processed_results
                
        except Exception as e:
            self.logger.error(f"并发处理时发生异常: {e}")
            # 为所有项目创建失败结果
            results = [
                ProcessingResult(
                    project_path=project.path,
                    status=ProcessingStatus.FAILED,
                    error_message=f"并发处理异常: {str(e)}"
                )
                for project in projects
            ]
        
        return results
    
    async def _process_with_fail_fast(self, tasks: List[asyncio.Task], projects: List[Any]) -> List[ProcessingResult]:
        """快速失败模式的并发处理"""
        results = []
        completed_tasks = set()
        
        try:
            # 等待任务完成，一旦有失败就取消其他任务
            for task in asyncio.as_completed(tasks):
                try:
                    result = await task
                    results.append(result)
                    completed_tasks.add(task)
                    
                    if not result.success:
                        self.logger.error(f"项目处理失败，取消其他任务: {result.project_path}")
                        # 取消其他任务
                        for other_task in tasks:
                            if other_task not in completed_tasks:
                                other_task.cancel()
                        break
                        
                except asyncio.CancelledError:
                    # 任务被取消
                    break
                except Exception as e:
                    # 任务异常
                    project_index = tasks.index(task)
                    error_result = ProcessingResult(
                        project_path=projects[project_index].path,
                        status=ProcessingStatus.FAILED,
                        error_message=str(e)
                    )
                    results.append(error_result)
                    completed_tasks.add(task)
                    
                    # 取消其他任务
                    for other_task in tasks:
                        if other_task not in completed_tasks:
                            other_task.cancel()
                    break
            
            # 为被取消的任务创建跳过状态的结果
            for i, task in enumerate(tasks):
                if task not in completed_tasks:
                    skipped_result = ProcessingResult(
                        project_path=projects[i].path,
                        status=ProcessingStatus.SKIPPED,
                        error_message="由于快速失败模式，任务被取消"
                    )
                    results.append(skipped_result)
            
        except Exception as e:
            self.logger.error(f"快速失败处理时发生异常: {e}")
            # 取消所有任务
            for task in tasks:
                task.cancel()
        
        return results


class AdaptiveStrategy(ProcessingStrategy):
    """自适应处理策略
    
    根据系统负载和项目特征动态调整并发数量。
    """
    
    def __init__(self, 
                 initial_concurrent: int = 3,
                 min_concurrent: int = 1,
                 max_concurrent: int = 10):
        self.initial_concurrent = initial_concurrent
        self.min_concurrent = min_concurrent
        self.max_concurrent = max_concurrent
        self.current_concurrent = initial_concurrent
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def process_batch(self, 
                          projects: List[Any],
                          process_func: Callable,
                          config: Any) -> List[ProcessingResult]:
        """自适应处理项目"""
        # 将项目分组处理
        batch_size = self.current_concurrent
        results = []
        
        for i in range(0, len(projects), batch_size):
            batch_projects = projects[i:i + batch_size]
            
            self.logger.info(f"处理批次 {i//batch_size + 1}, 并发数: {self.current_concurrent}")
            
            # 使用并发策略处理当前批次
            concurrent_strategy = ConcurrentStrategy(self.current_concurrent)
            batch_results = await concurrent_strategy.process_batch(
                batch_projects, process_func, config
            )
            
            results.extend(batch_results)
            
            # 根据结果调整并发数
            self._adjust_concurrency(batch_results)
            
            # 检查是否需要停止
            if config.fail_fast and any(not r.success for r in batch_results):
                break
        
        return results
    
    def _adjust_concurrency(self, batch_results: List[ProcessingResult]) -> None:
        """根据批次结果调整并发数"""
        success_rate = len([r for r in batch_results if r.success]) / len(batch_results)
        avg_duration = sum(r.duration_seconds or 0 for r in batch_results) / len(batch_results)
        
        # 根据成功率和平均耗时调整并发数
        if success_rate >= 0.9 and avg_duration < 60:
            # 成功率高且速度快，增加并发数
            self.current_concurrent = min(self.current_concurrent + 1, self.max_concurrent)
            self.logger.info(f"性能良好，增加并发数至: {self.current_concurrent}")
        elif success_rate < 0.7 or avg_duration > 120:
            # 成功率低或耗时长，减少并发数
            self.current_concurrent = max(self.current_concurrent - 1, self.min_concurrent)
            self.logger.info(f"性能不佳，减少并发数至: {self.current_concurrent}")


class RetryStrategy(ProcessingStrategy):
    """重试处理策略
    
    为失败的项目提供重试机制。
    """
    
    def __init__(self, base_strategy: ProcessingStrategy, max_retries: int = 2):
        self.base_strategy = base_strategy
        self.max_retries = max_retries
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def process_batch(self, 
                          projects: List[Any],
                          process_func: Callable,
                          config: Any) -> List[ProcessingResult]:
        """带重试的批量处理"""
        # 第一次处理
        results = await self.base_strategy.process_batch(projects, process_func, config)
        
        # 重试失败的项目
        for retry_count in range(self.max_retries):
            failed_projects = []
            failed_indices = []
            
            for i, result in enumerate(results):
                if not result.success and result.status != ProcessingStatus.SKIPPED:
                    failed_projects.append(projects[i])
                    failed_indices.append(i)
            
            if not failed_projects:
                break
            
            self.logger.info(f"第 {retry_count + 1} 次重试，重试 {len(failed_projects)} 个失败项目")
            
            # 等待一段时间后重试
            if hasattr(config, 'retry_delay_seconds'):
                await asyncio.sleep(config.retry_delay_seconds)
            
            # 重试失败的项目
            retry_results = await self.base_strategy.process_batch(
                failed_projects, process_func, config
            )
            
            # 更新结果
            for i, retry_result in enumerate(retry_results):
                original_index = failed_indices[i]
                results[original_index] = retry_result
        
        return results