# GenDocs 配置示例文件
# 复制此文件为 gendocs.yaml 并根据需要修改配置

ai:
  # AI服务配置 - 选择你的AI提供商
  provider: "openai"                    # 可选: openai, azure, deepseek, qwen, claude
  
  # OpenAI 配置示例
  base_url: "https://api.openai.com/v1"
  api_key: "${GENDOCS_AI_API_KEY}"      # 从环境变量读取
  model: "gpt-4o-mini"
  
  # DeepSeek 配置示例
  # base_url: "https://api.deepseek.com/v1"
  # api_key: "${DEEPSEEK_API_KEY}"
  # model: "deepseek-chat"
  
  # Qwen 配置示例
  # base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  # api_key: "${QWEN_API_KEY}"
  # model: "qwen-turbo"
  
  # 生成参数
  max_tokens: 4000
  temperature: 0.3
  timeout: 60
  retry_attempts: 3

languages:
  # 启用的编程语言支持
  enabled:
    - "python"
    # - "javascript"      # 未来支持
    # - "typescript"      # 未来支持
    # - "java"           # 未来支持
  
  # 项目类型检测顺序（按优先级）
  detection_order:
    - "python"

generation:
  # 文档生成选项
  backup_existing: true                 # 是否备份现有文档
  backup_format: "timestamp"            # 备份格式: timestamp, incremental, none
  concurrent_jobs: 4                    # 并发任务数，根据你的机器性能调整
  
  # 需要AI增强的文档类型（这些文档会使用AI生成更丰富的内容）
  ai_enhanced_docs:
    - "user_story_map"                  # 用户故事地图
    - "adr"                            # 架构决策记录
    - "runbook"                        # 运维手册

output:
  # 输出配置
  base_dir: "docs"                      # 文档输出目录
  formats:                              # 输出格式
    - "markdown"
  include_metadata: true                # 是否包含生成元数据

templates:
  # 模板配置
  base_path: "templates"                # 内置模板路径
  custom_path: "${GENDOCS_CUSTOM_TEMPLATES}"  # 自定义模板路径（可选）

# 环境变量设置示例:
# export GENDOCS_AI_API_KEY="your-api-key-here"
# export GENDOCS_CUSTOM_TEMPLATES="/path/to/your/custom/templates"