"""
性能指标收集模块

收集和管理GenDocs运行过程中的各种性能指标。
"""

import time
import psutil
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from collections import defaultdict, deque
from enum import Enum
import threading
import json


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"  # 计数器
    GAUGE = "gauge"     # 仪表
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"     # 计时器


@dataclass
class MetricPoint:
    """单个指标数据点"""
    name: str
    value: Union[int, float]
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    # 文档生成指标
    total_documents_generated: int = 0
    successful_generations: int = 0
    failed_generations: int = 0
    generation_time_seconds: float = 0.0
    
    # AI调用指标
    ai_requests_total: int = 0
    ai_requests_successful: int = 0
    ai_requests_failed: int = 0
    ai_response_time_seconds: float = 0.0
    ai_tokens_consumed: int = 0
    
    # 文件处理指标
    files_analyzed: int = 0
    files_processed: int = 0
    total_file_size_bytes: int = 0
    
    # 系统资源指标
    peak_memory_usage_mb: float = 0.0
    cpu_time_seconds: float = 0.0
    
    # 错误指标
    total_errors: int = 0
    error_types: Dict[str, int] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'document_generation': {
                'total_generated': self.total_documents_generated,
                'successful': self.successful_generations,
                'failed': self.failed_generations,
                'total_time_seconds': self.generation_time_seconds,
                'average_time_per_doc': (
                    self.generation_time_seconds / max(self.total_documents_generated, 1)
                )
            },
            'ai_integration': {
                'total_requests': self.ai_requests_total,
                'successful_requests': self.ai_requests_successful,
                'failed_requests': self.ai_requests_failed,
                'success_rate': (
                    self.ai_requests_successful / max(self.ai_requests_total, 1) * 100
                ),
                'average_response_time': (
                    self.ai_response_time_seconds / max(self.ai_requests_total, 1)
                ),
                'tokens_consumed': self.ai_tokens_consumed
            },
            'file_processing': {
                'files_analyzed': self.files_analyzed,
                'files_processed': self.files_processed,
                'total_size_mb': self.total_file_size_bytes / (1024 * 1024),
            },
            'system_resources': {
                'peak_memory_mb': self.peak_memory_usage_mb,
                'cpu_time_seconds': self.cpu_time_seconds
            },
            'errors': {
                'total_errors': self.total_errors,
                'error_breakdown': self.error_types
            }
        }


@dataclass 
class SystemMetrics:
    """系统资源指标"""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    disk_usage_percent: float = 0.0
    network_io_bytes: int = 0
    disk_io_bytes: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    
    @classmethod
    def collect_current(cls) -> 'SystemMetrics':
        """收集当前系统指标"""
        try:
            cpu = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 网络和磁盘IO
            net_io = psutil.net_io_counters()
            disk_io = psutil.disk_io_counters()
            
            return cls(
                cpu_percent=cpu,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_usage_percent=disk.percent,
                network_io_bytes=net_io.bytes_sent + net_io.bytes_recv if net_io else 0,
                disk_io_bytes=disk_io.read_bytes + disk_io.write_bytes if disk_io else 0
            )
        except Exception:
            return cls()


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        """初始化指标收集器
        
        Args:
            max_history: 最大历史记录数量
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.max_history = max_history
        
        # 指标存储
        self._metrics = PerformanceMetrics()
        self._metric_points: deque = deque(maxlen=max_history)
        self._timers: Dict[str, float] = {}
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, List[float]] = defaultdict(list)
        
        # 系统指标历史
        self._system_metrics_history: deque = deque(maxlen=max_history)
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 启动时间
        self._start_time = time.time()
        
        self.logger.info("性能指标收集器已初始化")
    
    def start_timer(self, name: str) -> None:
        """开始计时
        
        Args:
            name: 计时器名称
        """
        with self._lock:
            self._timers[name] = time.time()
    
    def end_timer(self, name: str, labels: Optional[Dict[str, str]] = None) -> float:
        """结束计时并记录
        
        Args:
            name: 计时器名称
            labels: 标签信息
            
        Returns:
            经过的时间(秒)
        """
        with self._lock:
            if name not in self._timers:
                self.logger.warning(f"计时器 {name} 未启动")
                return 0.0
            
            duration = time.time() - self._timers[name]
            del self._timers[name]
            
            # 记录指标点
            self._record_metric(name, duration, MetricType.TIMER, labels or {})
            
            # 更新直方图
            self._histograms[name].append(duration)
            
            return duration
    
    def increment_counter(self, name: str, value: int = 1, labels: Optional[Dict[str, str]] = None) -> None:
        """增加计数器
        
        Args:
            name: 计数器名称
            value: 增加的值
            labels: 标签信息
        """
        with self._lock:
            self._counters[name] += value
            self._record_metric(name, self._counters[name], MetricType.COUNTER, labels or {})
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """设置仪表值
        
        Args:
            name: 仪表名称
            value: 值
            labels: 标签信息
        """
        with self._lock:
            self._gauges[name] = value
            self._record_metric(name, value, MetricType.GAUGE, labels or {})
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """记录直方图数据
        
        Args:
            name: 直方图名称
            value: 值
            labels: 标签信息
        """
        with self._lock:
            self._histograms[name].append(value)
            self._record_metric(name, value, MetricType.HISTOGRAM, labels or {})
    
    def _record_metric(self, name: str, value: Union[int, float], 
                      metric_type: MetricType, labels: Dict[str, str]) -> None:
        """记录指标点(内部方法)"""
        point = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.now(),
            labels=labels,
            metric_type=metric_type
        )
        self._metric_points.append(point)
    
    def record_document_generation(self, success: bool, duration: float, 
                                 generator_name: str = "") -> None:
        """记录文档生成指标
        
        Args:
            success: 是否成功
            duration: 耗时
            generator_name: 生成器名称
        """
        with self._lock:
            self._metrics.total_documents_generated += 1
            self._metrics.generation_time_seconds += duration
            
            if success:
                self._metrics.successful_generations += 1
            else:
                self._metrics.failed_generations += 1
            
            # 记录详细指标
            labels = {'generator': generator_name, 'success': str(success)}
            self._record_metric('document_generation_duration', duration, MetricType.TIMER, labels)
            self.increment_counter('document_generation_total', 1, labels)
    
    def record_ai_request(self, success: bool, response_time: float, 
                         tokens_used: int = 0, provider: str = "") -> None:
        """记录AI请求指标
        
        Args:
            success: 是否成功
            response_time: 响应时间
            tokens_used: 使用的token数量
            provider: AI提供商
        """
        with self._lock:
            self._metrics.ai_requests_total += 1
            self._metrics.ai_response_time_seconds += response_time
            self._metrics.ai_tokens_consumed += tokens_used
            
            if success:
                self._metrics.ai_requests_successful += 1
            else:
                self._metrics.ai_requests_failed += 1
            
            # 记录详细指标
            labels = {'provider': provider, 'success': str(success)}
            self._record_metric('ai_request_duration', response_time, MetricType.TIMER, labels)
            self._record_metric('ai_tokens_used', tokens_used, MetricType.GAUGE, labels)
            self.increment_counter('ai_requests_total', 1, labels)
    
    def record_file_processing(self, file_count: int, total_size: int) -> None:
        """记录文件处理指标
        
        Args:
            file_count: 文件数量
            total_size: 总大小(字节)
        """
        with self._lock:
            self._metrics.files_processed += file_count
            self._metrics.total_file_size_bytes += total_size
            
            self.increment_counter('files_processed', file_count)
            self.set_gauge('total_file_size_bytes', self._metrics.total_file_size_bytes)
    
    def record_error(self, error_type: str, error_message: str = "") -> None:
        """记录错误指标
        
        Args:
            error_type: 错误类型
            error_message: 错误消息
        """
        with self._lock:
            self._metrics.total_errors += 1
            self._metrics.error_types[error_type] = self._metrics.error_types.get(error_type, 0) + 1
            
            labels = {'error_type': error_type}
            self.increment_counter('errors_total', 1, labels)
    
    def update_system_metrics(self) -> None:
        """更新系统资源指标"""
        try:
            current_metrics = SystemMetrics.collect_current()
            
            with self._lock:
                self._system_metrics_history.append(current_metrics)
                
                # 更新峰值内存使用
                if current_metrics.memory_used_mb > self._metrics.peak_memory_usage_mb:
                    self._metrics.peak_memory_usage_mb = current_metrics.memory_used_mb
                
                # 记录系统指标
                self.set_gauge('cpu_percent', current_metrics.cpu_percent)
                self.set_gauge('memory_percent', current_metrics.memory_percent)
                self.set_gauge('memory_used_mb', current_metrics.memory_used_mb)
                self.set_gauge('disk_usage_percent', current_metrics.disk_usage_percent)
        
        except Exception as e:
            self.logger.error(f"更新系统指标失败: {e}")
    
    def get_metrics_summary(self) -> PerformanceMetrics:
        """获取指标摘要
        
        Returns:
            性能指标对象
        """
        with self._lock:
            # 更新运行时间
            self._metrics.cpu_time_seconds = time.time() - self._start_time
            return self._metrics
    
    def get_histogram_stats(self, name: str) -> Dict[str, float]:
        """获取直方图统计信息
        
        Args:
            name: 直方图名称
            
        Returns:
            统计信息字典
        """
        with self._lock:
            values = self._histograms.get(name, [])
            if not values:
                return {}
            
            sorted_values = sorted(values)
            count = len(values)
            
            return {
                'count': count,
                'min': sorted_values[0],
                'max': sorted_values[-1],
                'mean': sum(values) / count,
                'p50': sorted_values[count // 2],
                'p90': sorted_values[int(count * 0.9)],
                'p95': sorted_values[int(count * 0.95)],
                'p99': sorted_values[int(count * 0.99)]
            }
    
    def get_recent_metrics(self, duration_minutes: int = 5) -> List[MetricPoint]:
        """获取最近的指标数据
        
        Args:
            duration_minutes: 时间范围(分钟)
            
        Returns:
            指标数据点列表
        """
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with self._lock:
            return [
                point for point in self._metric_points
                if point.timestamp >= cutoff_time
            ]
    
    def get_system_metrics_history(self, duration_minutes: int = 30) -> List[SystemMetrics]:
        """获取系统指标历史
        
        Args:
            duration_minutes: 时间范围(分钟)
            
        Returns:
            系统指标列表
        """
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with self._lock:
            return [
                metrics for metrics in self._system_metrics_history
                if metrics.timestamp >= cutoff_time
            ]
    
    def export_metrics(self, file_path: Path) -> None:
        """导出指标数据到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            with self._lock:
                export_data = {
                    'timestamp': datetime.now().isoformat(),
                    'summary': self._metrics.to_dict(),
                    'counters': dict(self._counters),
                    'gauges': dict(self._gauges),
                    'histogram_stats': {
                        name: self.get_histogram_stats(name)
                        for name in self._histograms.keys()
                    },
                    'recent_metrics': [
                        {
                            'name': point.name,
                            'value': point.value,
                            'timestamp': point.timestamp.isoformat(),
                            'labels': point.labels,
                            'type': point.metric_type.value
                        }
                        for point in list(self._metric_points)
                    ]
                }
            
            file_path.write_text(
                json.dumps(export_data, ensure_ascii=False, indent=2),
                encoding='utf-8'
            )
            
            self.logger.info(f"指标数据已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"导出指标数据失败: {e}")
    
    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._metrics = PerformanceMetrics()
            self._metric_points.clear()
            self._timers.clear()
            self._counters.clear()
            self._gauges.clear()
            self._histograms.clear()
            self._system_metrics_history.clear()
            self._start_time = time.time()
            
            self.logger.info("性能指标已重置")


# 全局指标收集器实例
_global_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器实例
    
    Returns:
        指标收集器实例
    """
    global _global_metrics_collector
    if _global_metrics_collector is None:
        _global_metrics_collector = MetricsCollector()
    return _global_metrics_collector


def reset_global_metrics_collector() -> None:
    """重置全局指标收集器"""
    global _global_metrics_collector
    _global_metrics_collector = None