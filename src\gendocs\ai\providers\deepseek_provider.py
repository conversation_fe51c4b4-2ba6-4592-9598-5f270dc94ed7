"""
DeepSeek API提供商实现

DeepSeek API兼容OpenAI格式，所以继承OpenAI提供商。
"""

from .openai_provider import OpenAIProvider


class DeepSeekProvider(OpenAIProvider):
    """DeepSeek API提供商
    
    DeepSeek API完全兼容OpenAI格式，所以直接继承OpenAI提供商。
    """
    
    def __init__(self, config):
        """初始化DeepSeek提供商"""
        super().__init__(config)
        
        # 设置默认base_url（如果未配置）
        if config.base_url == "https://api.openai.com/v1":
            config.base_url = "https://api.deepseek.com/v1"
        
        # 重新设置请求头
        self._headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
    
    def validate_config(self):
        """验证DeepSeek配置"""
        issues = super().validate_config()
        
        # 检查是否使用了正确的API端点
        if "deepseek.com" not in self.config.base_url:
            issues.append("建议使用DeepSeek官方API端点: https://api.deepseek.com/v1")
        
        return issues