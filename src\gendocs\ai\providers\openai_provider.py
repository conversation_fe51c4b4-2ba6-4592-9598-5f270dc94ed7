"""
OpenAI API提供商实现

支持OpenAI官方API和兼容接口。
"""

import json
import asyncio
from typing import Dict, Any, List

try:
    import aiohttp
except ImportError:
    aiohttp = None

from ..base import BaseAIProvider, AIRequest, AIResponse, AIGenerationError


class OpenAIProvider(BaseAIProvider):
    """OpenAI API提供商
    
    支持OpenAI官方API以及兼容OpenAI格式的其他API服务。
    """
    
    def __init__(self, config):
        """初始化OpenAI提供商"""
        super().__init__(config)
        
        if aiohttp is None:
            raise AIGenerationError("需要安装 aiohttp 依赖: pip install aiohttp")
        
        # 设置HTTP会话
        self._session = None
        self._headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self._session = aiohttp.ClientSession(
                headers=self._headers,
                timeout=timeout
            )
        return self._session
    
    async def generate_completion(self, request: AIRequest) -> AIResponse:
        """生成文本补全"""
        return await self._retry_on_failure(self._generate_completion_impl, request)
    
    async def _generate_completion_impl(self, request: AIRequest) -> AIResponse:
        """生成文本补全的实际实现"""
        session = await self._get_session()
        
        # 构建请求数据
        messages = self._build_messages(request)
        params = self._get_generation_params(request)
        
        data = {
            "messages": messages,
            **params
        }
        
        # 发送请求
        url = f"{self.config.base_url.rstrip('/')}/chat/completions"
        
        try:
            async with session.post(url, json=data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise AIGenerationError(
                        f"API请求失败 (状态码: {response.status}): {error_text}"
                    )
                
                result = await response.json()
                return self._parse_completion_response(result)
                
        except aiohttp.ClientError as e:
            raise AIGenerationError(f"网络请求失败: {e}")
        except json.JSONDecodeError as e:
            raise AIGenerationError(f"响应JSON解析失败: {e}")
    
    async def generate_structured(self, request: AIRequest, schema: Dict) -> Dict[str, Any]:
        """生成结构化输出"""
        # 在提示词中添加结构化输出要求
        structured_prompt = self._build_structured_prompt(request.prompt, schema)
        structured_request = AIRequest(
            prompt=structured_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            system_prompt=request.system_prompt,
            context=request.context
        )
        
        response = await self.generate_completion(structured_request)
        
        # 尝试解析JSON响应
        try:
            return json.loads(response.content)
        except json.JSONDecodeError:
            # 如果不是有效JSON，尝试提取JSON部分
            return self._extract_json_from_text(response.content)
    
    def _build_structured_prompt(self, original_prompt: str, schema: Dict) -> str:
        """构建结构化输出提示词"""
        schema_str = json.dumps(schema, indent=2, ensure_ascii=False)
        
        return f"""
{original_prompt}

请按照以下JSON schema格式返回结果，只返回JSON内容，不要包含其他文本：

```json
{schema_str}
```

返回的JSON：
"""
    
    def _extract_json_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON"""
        import re
        
        # 尝试找到JSON代码块
        json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        match = re.search(json_pattern, text, re.DOTALL)
        
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass
        
        # 尝试找到第一个JSON对象
        brace_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        match = re.search(brace_pattern, text)
        
        if match:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                pass
        
        raise AIGenerationError("无法从响应中提取有效的JSON结构")
    
    def _parse_completion_response(self, response_data: Dict) -> AIResponse:
        """解析补全响应"""
        try:
            choice = response_data["choices"][0]
            message = choice["message"]
            
            return AIResponse(
                content=message["content"],
                model=response_data.get("model", self.config.model),
                tokens_used=response_data.get("usage", {}).get("total_tokens"),
                finish_reason=choice.get("finish_reason"),
                metadata={
                    "response_id": response_data.get("id"),
                    "created": response_data.get("created"),
                    "usage": response_data.get("usage")
                }
            )
            
        except (KeyError, IndexError) as e:
            raise AIGenerationError(f"响应格式错误: {e}")
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        issues = super().validate_config()
        
        # 检查URL格式
        if not self.config.base_url.startswith(("http://", "https://")):
            issues.append("base_url必须是有效的HTTP/HTTPS URL")
        
        return issues
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            test_request = AIRequest(
                prompt="测试连接",
                max_tokens=5,
                temperature=0.1
            )
            
            response = await self.generate_completion(test_request)
            return bool(response.content.strip())
            
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
        finally:
            await self.close()
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, '_session') and self._session and not self._session.closed:
            # 在主线程中安排关闭任务
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except RuntimeError:
                pass