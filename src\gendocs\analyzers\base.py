"""
项目分析器基类

定义项目分析的统一接口和数据结构。
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from enum import Enum


class ProjectType(str, Enum):
    """项目类型枚举"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"
    UNKNOWN = "unknown"


class FrameworkType(str, Enum):
    """框架类型枚举"""
    # Python框架
    DJANGO = "django"
    FLASK = "flask"
    FASTAPI = "fastapi"
    # JavaScript/TypeScript框架
    REACT = "react"
    VUE = "vue"
    ANGULAR = "angular"
    EXPRESS = "express"
    NEXTJS = "nextjs"
    # Java框架
    SPRING = "spring"
    SPRING_BOOT = "spring-boot"
    # 其他
    UNKNOWN_FRAMEWORK = "unknown"


@dataclass
class DependencyInfo:
    """依赖信息"""
    name: str
    version: Optional[str] = None
    description: Optional[str] = None
    is_dev_dependency: bool = False
    is_external: bool = True


@dataclass
class APIInfo:
    """API接口信息"""
    method: str
    path: str
    description: Optional[str] = None
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    responses: List[Dict[str, Any]] = field(default_factory=list)
    module: Optional[str] = None
    function: Optional[str] = None


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    path: str
    description: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)
    exports: List[str] = field(default_factory=list)
    file_count: int = 0
    line_count: int = 0


@dataclass
class TechStackInfo:
    """技术栈信息"""
    languages: List[str] = field(default_factory=list)
    frameworks: List[str] = field(default_factory=list)
    databases: List[str] = field(default_factory=list)
    tools: List[str] = field(default_factory=list)
    runtime: List[str] = field(default_factory=list)


@dataclass
class ArchitectureInfo:
    """架构信息"""
    pattern: Optional[str] = None  # MVC, MVP, MVVM, etc.
    layers: List[Dict[str, Any]] = field(default_factory=list)
    components: List[Dict[str, Any]] = field(default_factory=list)
    data_flow: Optional[str] = None
    design_patterns: List[str] = field(default_factory=list)


@dataclass
class QualityMetrics:
    """代码质量指标"""
    total_files: int = 0
    total_lines: int = 0
    code_lines: int = 0
    comment_lines: int = 0
    blank_lines: int = 0
    cyclomatic_complexity: Optional[float] = None
    maintainability_index: Optional[float] = None
    test_coverage: Optional[float] = None
    duplication_ratio: Optional[float] = None


@dataclass
class ProjectAnalysisResult:
    """项目分析结果"""
    project_name: str
    project_type: ProjectType
    framework: Optional[FrameworkType] = None
    root_path: Optional[Path] = None
    
    # 基本信息
    description: Optional[str] = None
    version: Optional[str] = None
    author: Optional[str] = None
    license: Optional[str] = None
    
    # 技术信息
    tech_stack: TechStackInfo = field(default_factory=TechStackInfo)
    dependencies: List[DependencyInfo] = field(default_factory=list)
    apis: List[APIInfo] = field(default_factory=list)
    modules: List[ModuleInfo] = field(default_factory=list)
    
    # 架构信息
    architecture: ArchitectureInfo = field(default_factory=ArchitectureInfo)
    quality_metrics: QualityMetrics = field(default_factory=QualityMetrics)
    
    # 部署信息
    deployment_config: Dict[str, Any] = field(default_factory=dict)
    
    # 其他信息
    readme_content: Optional[str] = None
    changelog_content: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, (ProjectType, FrameworkType)):
                result[key] = value.value
            elif isinstance(value, Path):
                result[key] = str(value)
            elif hasattr(value, '__dict__'):
                # 处理嵌套的dataclass
                result[key] = value.__dict__ if hasattr(value, '__dict__') else value
            else:
                result[key] = value
        return result


class BaseAnalyzer(ABC):
    """项目分析器基类
    
    定义项目分析的统一接口，支持多种编程语言和框架的分析。
    """
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._supported_extensions: Set[str] = set()
        self._framework_indicators: Dict[str, List[str]] = {}
    
    @property
    @abstractmethod
    def project_type(self) -> ProjectType:
        """项目类型"""
        pass
    
    @property
    @abstractmethod
    def supported_extensions(self) -> Set[str]:
        """支持的文件扩展名"""
        pass
    
    @abstractmethod
    def can_analyze(self, project_path: Path) -> bool:
        """检查是否能分析该项目
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            是否支持分析该项目
        """
        pass
    
    @abstractmethod
    def analyze_project(self, project_path: Path) -> ProjectAnalysisResult:
        """分析项目
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            项目分析结果
            
        Raises:
            AnalysisError: 分析失败时抛出
        """
        pass
    
    def detect_framework(self, project_path: Path) -> Optional[FrameworkType]:
        """检测项目使用的框架
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            检测到的框架类型，未检测到返回None
        """
        for framework, indicators in self._framework_indicators.items():
            if self._check_framework_indicators(project_path, indicators):
                try:
                    return FrameworkType(framework)
                except ValueError:
                    self.logger.warning(f"未知框架类型: {framework}")
                    continue
        return None
    
    def _check_framework_indicators(self, project_path: Path, indicators: List[str]) -> bool:
        """检查框架指示器
        
        Args:
            project_path: 项目路径
            indicators: 框架指示器列表
            
        Returns:
            是否匹配框架指示器
        """
        for indicator in indicators:
            if self._check_indicator(project_path, indicator):
                return True
        return False
    
    def _check_indicator(self, project_path: Path, indicator: str) -> bool:
        """检查单个指示器
        
        Args:
            project_path: 项目路径
            indicator: 指示器（文件名、包名等）
            
        Returns:
            是否匹配指示器
        """
        # 检查文件是否存在
        file_path = project_path / indicator
        if file_path.exists():
            return True
        
        # 检查通配符匹配
        if '*' in indicator:
            pattern_parts = indicator.split('*')
            for path in project_path.rglob('*'):
                path_str = str(path.relative_to(project_path))
                if all(part in path_str for part in pattern_parts if part):
                    return True
        
        return False
    
    def get_project_files(self, project_path: Path) -> List[Path]:
        """获取项目中的相关源文件
        
        Args:
            project_path: 项目路径
            
        Returns:
            源文件路径列表
        """
        files = []
        for ext in self.supported_extensions:
            pattern = f"**/*{ext}"
            for file_path in project_path.rglob(pattern):
                if self._should_include_file(file_path):
                    files.append(file_path)
        return sorted(files)
    
    def _should_include_file(self, file_path: Path) -> bool:
        """判断是否应该包含该文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否应该包含
        """
        # 排除的目录
        excluded_dirs = {
            '.git', '.venv', '__pycache__', 'node_modules',
            '.idea', '.vscode', '.pytest_cache', 'build', 
            'dist', 'target', '.next', '.nuxt'
        }
        
        # 检查是否在排除目录中
        for parent in file_path.parents:
            if parent.name in excluded_dirs:
                return False
        
        # 排除隐藏文件（除了特定的配置文件）
        if file_path.name.startswith('.') and file_path.name not in {
            '.env', '.env.example', '.gitignore', '.dockerignore'
        }:
            return False
        
        return True
    
    def count_lines_of_code(self, file_path: Path) -> Dict[str, int]:
        """统计代码行数
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含total_lines, code_lines, comment_lines, blank_lines的字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            total_lines = len(lines)
            code_lines = 0
            comment_lines = 0
            blank_lines = 0
            
            for line in lines:
                stripped = line.strip()
                if not stripped:
                    blank_lines += 1
                elif self._is_comment_line(stripped, file_path.suffix):
                    comment_lines += 1
                else:
                    code_lines += 1
            
            return {
                'total_lines': total_lines,
                'code_lines': code_lines,
                'comment_lines': comment_lines,
                'blank_lines': blank_lines
            }
            
        except Exception as e:
            self.logger.warning(f"无法统计文件行数 {file_path}: {e}")
            return {
                'total_lines': 0,
                'code_lines': 0,
                'comment_lines': 0,
                'blank_lines': 0
            }
    
    def _is_comment_line(self, line: str, file_extension: str) -> bool:
        """判断是否为注释行
        
        Args:
            line: 代码行
            file_extension: 文件扩展名
            
        Returns:
            是否为注释行
        """
        comment_patterns = {
            '.py': ['#'],
            '.js': ['//', '/*', '*'],
            '.ts': ['//', '/*', '*'],
            '.jsx': ['//', '/*', '*'],
            '.tsx': ['//', '/*', '*'],
            '.java': ['//', '/*', '*'],
            '.cpp': ['//', '/*', '*'],
            '.c': ['//', '/*', '*'],
            '.h': ['//', '/*', '*'],
            '.hpp': ['//', '/*', '*'],
            '.go': ['//'],
            '.rs': ['//', '/*'],
        }
        
        patterns = comment_patterns.get(file_extension, [])
        return any(line.startswith(pattern) for pattern in patterns)


class AnalysisError(Exception):
    """分析错误异常"""
    pass