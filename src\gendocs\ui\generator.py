"""
UI适配器模块 - 连接UI和生成器
"""

from pathlib import Path
import logging
from ..generator import DocGenerator, Config, FileUtils
from typing import List, Optional

class DocumentGenerator:
    """UI文档生成器适配器"""
    def __init__(self):
        self._config = Config()
        self._utils = FileUtils()
        self._generator = DocGenerator(self._config, self._utils)
        self._project_dir = Path.cwd()
        self.logger = logging.getLogger(__name__)

    def set_project_dir(self, path: Path) -> None:
        """设置项目目录"""
        self._project_dir = path.absolute()
        self._config.project_root = self._project_dir
        self._config.docs_dir = self._project_dir / "docs"
        self.logger.info(f"项目目录设置为: {self._project_dir}")

    def get_available_modules(self) -> List[str]:
        """获取可用的模块列表"""
        if not self._project_dir:
            return []
        
        modules = []
        src_dir = self._project_dir / "src"
        if src_dir.exists():
            for item in src_dir.iterdir():
                if item.is_dir() and (item / "__init__.py").exists():
                    modules.append(item.name)
        return modules or ["default"]

    def generate(self, modules: Optional[List[str]] = None) -> None:
        """生成文档"""
        if not self._project_dir:
            raise ValueError("请先设置项目目录")
        
        if modules:
            self.logger.info(f"选择的模块: {', '.join(modules)}")
            # TODO: 处理模块选择逻辑
        
        try:
            self._generator.generate()
            self.logger.info("文档生成完成")
        except Exception as e:
            self.logger.error(f"生成文档时出错: {str(e)}")
            raise 