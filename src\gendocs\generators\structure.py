"""
代码结构生成器模块
"""

import ast
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

from .base import BaseGenerator

class StructureGenerator(BaseGenerator):
    """代码结构生成器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._excluded_dirs = {'.git', '.venv', '__pycache__', 'build', 'dist', 'node_modules'}
        self._included_exts = {'.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h', '.hpp'}
        
    def _generate(self) -> bool:
        """生成代码结构文档
        
        Returns:
            生成是否成功
        """
        self.logger.info("正在生成代码结构文档...")
        
        try:
            # 写入标题
            self._write("# 代码结构\n\n")
            
            # 并行处理每个模块
            with ThreadPoolExecutor() as executor:
                futures = []
                for module in self.config.modules:
                    module_path = self.config.get_module_path(module)
                    if not module_path.exists():
                        self.logger.warning(f"模块路径不存在: {module_path}")
                        continue
                        
                    future = executor.submit(self._generate_module_structure, 
                                          module, 
                                          module_path)
                    futures.append((module, future))
                    
                # 等待所有任务完成并写入结果
                for module, future in futures:
                    try:
                        module_doc = future.result()
                        if module_doc:
                            self._write(f"## {module}\n\n")
                            self._write(module_doc)
                    except Exception as e:
                        self.logger.error(f"生成模块 {module} 的结构文档失败: {str(e)}")
                        
            # 写入文件
            output_file = self.config.docs_dir / "structure.md"
            return self.write_to_file(output_file)
            
        except Exception as e:
            self.logger.error(f"生成代码结构文档失败: {str(e)}")
            return False
            
    def _generate_module_structure(self, 
                                 module: str, 
                                 module_path: Path) -> Optional[str]:
        """为单个模块生成结构文档
        
        Args:
            module: 模块名
            module_path: 模块路径
            
        Returns:
            生成的文档内容，如果失败则返回None
        """
        try:
            # 生成目录树
            tree = self._generate_tree(module_path)
            if not tree:
                return None
                
            docs = [tree, "\n"]
            
            # 生成文件描述
            descriptions = self._generate_file_descriptions(module_path)
            if descriptions:
                docs.append("### 文件说明\n\n")
                docs.append(descriptions)
                
            return "".join(docs)
            
        except Exception as e:
            self.logger.error(f"生成模块 {module} 的结构文档时发生错误: {str(e)}")
            return None
            
    def _generate_tree(self, path: Path, prefix: str = "", is_last: bool = True) -> str:
        """生成目录树
        
        Args:
            path: 路径
            prefix: 前缀
            is_last: 是否是最后一个项目
            
        Returns:
            目录树文档
        """
        if path.name in self._excluded_dirs:
            return ""
            
        # 构建当前行
        current = "└── " if is_last else "├── "
        result = [f"{prefix}{current}{path.name}\n"]
        
        # 处理子目录和文件
        if path.is_dir():
            # 获取所有子项目
            items = sorted(
                [p for p in path.iterdir() 
                 if p.name not in self._excluded_dirs and 
                 (p.is_dir() or p.suffix in self._included_exts)],
                key=lambda p: (not p.is_dir(), p.name)  # 目录优先
            )
            
            # 递归处理每个子项目
            for i, item in enumerate(items):
                new_prefix = prefix + ("    " if is_last else "│   ")
                result.append(self._generate_tree(
                    item,
                    new_prefix,
                    i == len(items) - 1
                ))
                
        return "".join(result)
        
    def _generate_file_descriptions(self, path: Path) -> Optional[str]:
        """生成文件描述
        
        Args:
            path: 路径
            
        Returns:
            文件描述文档
        """
        try:
            descriptions = []
            
            # 并行处理所有文件
            with ThreadPoolExecutor() as executor:
                futures = []
                
                # 收集所有需要处理的文件
                for file_path in path.rglob("*"):
                    if (file_path.is_file() and 
                        file_path.suffix in self._included_exts and
                        not any(p.name in self._excluded_dirs 
                               for p in file_path.parents)):
                        future = executor.submit(
                            self._get_file_description,
                            file_path
                        )
                        futures.append((file_path, future))
                        
                # 等待所有任务完成
                for file_path, future in futures:
                    try:
                        desc = future.result()
                        if desc:
                            rel_path = file_path.relative_to(self.config.project_root)
                            descriptions.append(f"#### {rel_path}\n\n{desc}\n")
                    except Exception as e:
                        self.logger.warning(
                            f"获取文件 {file_path} 的描述失败: {str(e)}"
                        )
                        
            return "\n".join(descriptions) if descriptions else None
            
        except Exception as e:
            self.logger.error(f"生成文件描述时发生错误: {str(e)}")
            return None
            
    @lru_cache(maxsize=1024)
    def _get_file_description(self, file_path: Path) -> Optional[str]:
        """获取文件描述
        
        使用缓存避免重复解析
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件描述
        """
        try:
            content = self.read_file_content(file_path)
            if not content:
                return None
                
            # Python文件使用AST解析文档字符串
            if file_path.suffix == '.py':
                tree = ast.parse(content, mode='exec')
                doc = ast.get_docstring(tree)
                if doc:
                    return doc.strip()
                    
            # 其他文件类型查找文件头注释
            else:
                # 提取开头的连续注释
                comments = []
                for line in content.splitlines():
                    line = line.strip()
                    # 支持常见的注释格式
                    if (line.startswith('//') or 
                        line.startswith('#') or 
                        line.startswith('/*') or 
                        line.startswith('*')):
                        comments.append(line.lstrip('/#* '))
                    elif not line:
                        continue
                    else:
                        break
                        
                if comments:
                    return "\n".join(comments).strip()
                    
            return None
            
        except Exception as e:
            self.logger.warning(f"解析文件 {file_path} 失败: {str(e)}")
            return None 