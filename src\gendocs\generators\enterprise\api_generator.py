"""
API文档生成器

生成API接口文档，包括接口说明、参数、响应等。
"""

from pathlib import Path
from typing import Dict, Any, List

from .base import EnterpriseGenerator


class APIGenerator(EnterpriseGenerator):
    """API文档生成器"""
    
    def __init__(self, config_manager=None, ai_provider=None):
        super().__init__(config_manager, ai_provider)
        self.template_name = "base/api_docs.md.j2"
        self.output_filename = "API_DOCS.md"
    
    async def generate(self, project_path: Path, output_path: Path, context: Dict[str, Any]) -> bool:
        """生成API文档"""
        try:
            self.logger.info(f"开始生成API文档: {project_path}")
            
            enterprise_context = self.build_enterprise_context(project_path, context.get('analysis_result'))
            enterprise_context.update(context)
            
            api_context = self._build_api_context(enterprise_context)
            
            base_content = self.render_template(self.template_name, api_context)
            
            if self._ai_provider:
                enhanced_content = await self.generate_ai_content(
                    "api_documentation_enhancement",
                    **api_context
                )
                if enhanced_content:
                    base_content = enhanced_content
            
            final_output_path = output_path / self.output_filename
            with open(final_output_path, 'w', encoding='utf-8') as f:
                f.write(base_content)
            
            if self.validate_output(final_output_path):
                self.logger.info(f"API文档生成完成: {final_output_path}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"生成API文档失败: {e}")
            return False
    
    def _build_api_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """构建API特定上下文"""
        analysis_result = context.get('analysis_result')
        
        api_context = context.copy()
        api_context.update({
            'api_overview': self._build_api_overview(analysis_result),
            'api_groups': self._group_apis(analysis_result),
            'authentication': self._analyze_authentication(analysis_result),
            'error_codes': self._generate_error_codes(),
            'rate_limiting': self._analyze_rate_limiting(analysis_result),
            'examples': self._generate_examples(analysis_result)
        })
        
        return api_context
    
    def _build_api_overview(self, analysis_result) -> Dict[str, Any]:
        """构建API概述"""
        overview = {
            'base_url': 'http://localhost:8000',
            'version': 'v1',
            'protocol': 'HTTP/HTTPS',
            'format': 'JSON',
            'total_endpoints': 0
        }
        
        if analysis_result and analysis_result.apis:
            overview['total_endpoints'] = len(analysis_result.apis)
            
            # 分析API协议
            methods = {api.method for api in analysis_result.apis}
            if len(methods) > 1:
                overview['methods'] = list(methods)
        
        return overview
    
    def _group_apis(self, analysis_result) -> List[Dict[str, Any]]:
        """按功能分组API"""
        groups = []
        
        if not analysis_result or not analysis_result.apis:
            return groups
        
        # 简单的按路径前缀分组
        api_groups = {}
        for api in analysis_result.apis:
            path_parts = api.path.split('/')
            group_name = path_parts[1] if len(path_parts) > 1 else 'default'
            
            if group_name not in api_groups:
                api_groups[group_name] = []
            api_groups[group_name].append(api)
        
        for group_name, apis in api_groups.items():
            groups.append({
                'name': group_name.title(),
                'description': f'{group_name}相关接口',
                'apis': apis
            })
        
        return groups
    
    def _analyze_authentication(self, analysis_result) -> Dict[str, Any]:
        """分析认证方式"""
        auth = {
            'type': 'Bearer Token',
            'description': 'API使用Bearer Token进行身份验证',
            'header': 'Authorization: Bearer <token>',
            'example': 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
        }
        
        if analysis_result and analysis_result.framework:
            framework = analysis_result.framework.value.lower()
            if framework == 'django':
                auth['type'] = 'Session/Token'
                auth['description'] = 'Django支持Session和Token认证'
        
        return auth
    
    def _generate_error_codes(self) -> List[Dict[str, Any]]:
        """生成错误码说明"""
        return [
            {'code': 200, 'message': 'OK', 'description': '请求成功'},
            {'code': 400, 'message': 'Bad Request', 'description': '请求参数错误'},
            {'code': 401, 'message': 'Unauthorized', 'description': '未授权访问'},
            {'code': 404, 'message': 'Not Found', 'description': '资源不存在'},
            {'code': 500, 'message': 'Internal Server Error', 'description': '服务器内部错误'}
        ]
    
    def _analyze_rate_limiting(self, analysis_result) -> Dict[str, Any]:
        """分析限流配置"""
        return {
            'enabled': True,
            'default_limit': '100 requests per hour',
            'headers': [
                'X-RateLimit-Limit: 100',
                'X-RateLimit-Remaining: 99',
                'X-RateLimit-Reset: 1640995200'
            ]
        }
    
    def _generate_examples(self, analysis_result) -> List[Dict[str, str]]:
        """生成API使用示例"""
        examples = []
        
        if analysis_result and analysis_result.apis:
            for api in analysis_result.apis[:3]:
                examples.append({
                    'title': f'{api.method} {api.path}',
                    'curl': f'curl -X {api.method} "{api.path}"',
                    'response': '{"status": "success", "data": {}}'
                })
        
        return examples