# {{ project_name }} - 开发指南

## 概览

本文档提供了 {{ project_name }} 的完整开发指南，包括环境搭建、开发规范、测试和部署流程。

## 环境搭建

### 系统要求

{% if system_requirements %}
{% for req in system_requirements %}
- **{{ req.category }}**: {{ req.requirement }}
{% endfor %}
{% else %}
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
{% if project_type == "python" %}
- **Python版本**: {{ python_version if python_version else "3.8+" }}
{% elif project_type == "javascript" or project_type == "typescript" %}
- **Node.js版本**: {{ node_version if node_version else "16.0+" }}
{% elif project_type == "java" %}
- **JDK版本**: {{ java_version if java_version else "11+" }}
{% endif %}
- **内存要求**: 4GB RAM (推荐8GB)
- **磁盘空间**: 至少2GB可用空间
{% endif %}

### 必需工具

{% if required_tools %}
{% for tool in required_tools %}
- **{{ tool.name }}**: {{ tool.description }}
  - 安装命令: `{{ tool.install_command }}`
  - 验证命令: `{{ tool.verify_command }}`
{% endfor %}
{% else %}
{% if project_type == "python" %}
- **Python**: 主要开发语言
- **pip**: Python包管理器
- **virtualenv**: 虚拟环境管理 (推荐)
{% elif project_type == "javascript" or project_type == "typescript" %}
- **Node.js**: JavaScript运行环境
- **npm/yarn**: 包管理器
{% elif project_type == "java" %}
- **JDK**: Java开发工具包
- **Maven/Gradle**: 构建工具
{% endif %}
- **Git**: 版本控制系统
- **IDE**: VS Code / IntelliJ IDEA / PyCharm (推荐)
{% endif %}

### 开发环境搭建

#### 1. 克隆项目

```bash
git clone {{ repository_url if repository_url else "https://github.com/your-org/" + project_name + ".git" }}
cd {{ project_name }}
```

#### 2. 环境配置

{% if project_type == "python" %}
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```
{% elif project_type == "javascript" or project_type == "typescript" %}
```bash
# 安装依赖
npm install
# 或使用 yarn
yarn install

# 安装开发工具
npm install -g {{ dev_tools if dev_tools else "eslint prettier" }}
```
{% elif project_type == "java" %}
```bash
# 使用Maven
mvn clean install

# 使用Gradle
./gradlew build
```
{% endif %}

#### 3. 配置文件设置

{% if config_files %}
{% for config in config_files %}
复制 `{{ config.template }}` 为 `{{ config.target }}`，并根据需要修改配置：

```bash
cp {{ config.template }} {{ config.target }}
```

主要配置项：
{% for setting in config.settings %}
- `{{ setting.key }}`: {{ setting.description }}
{% endfor %}

{% endfor %}
{% else %}
创建本地配置文件：

```bash
{% if project_type == "python" %}
cp .env.example .env
{% elif project_type == "javascript" or project_type == "typescript" %}
cp .env.example .env.local
{% endif %}
```

设置必要的环境变量：
```env
{% if project_type == "python" %}
DEBUG=True
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///db.sqlite3
{% elif project_type == "javascript" or project_type == "typescript" %}
NODE_ENV=development
API_BASE_URL=http://localhost:3000
{% endif %}
```
{% endif %}

#### 4. 数据库设置

{% if database_setup %}
{{ database_setup }}
{% else %}
{% if has_database %}
```bash
{% if project_type == "python" %}
# Django项目
python manage.py migrate

# Flask项目
flask db upgrade
{% elif project_type == "javascript" or project_type == "typescript" %}
# 运行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed
{% elif project_type == "java" %}
# Spring Boot项目
mvn spring-boot:run -Dspring-boot.run.arguments="--spring.profiles.active=dev"
{% endif %}
```
{% endif %}
{% endif %}

#### 5. 启动开发服务器

```bash
{% if project_type == "python" %}
{% if framework == "django" %}
python manage.py runserver
{% elif framework == "flask" %}
flask run
{% elif framework == "fastapi" %}
uvicorn main:app --reload
{% else %}
python main.py
{% endif %}
{% elif project_type == "javascript" %}
npm start
# 或
npm run dev
{% elif project_type == "typescript" %}
npm run dev
{% elif project_type == "java" %}
{% if framework == "spring" %}
mvn spring-boot:run
{% else %}
./gradlew bootRun
{% endif %}
{% endif %}
```

访问地址: {{ dev_server_url if dev_server_url else "http://localhost:8080" }}

## 开发规范

### 代码规范

{% if coding_standards %}
{% for standard in coding_standards %}
#### {{ standard.category }}

{{ standard.description }}

{% if standard.rules %}
{% for rule in standard.rules %}
- {{ rule }}
{% endfor %}
{% endif %}

{% if standard.example %}
**示例:**
```{{ project_type }}
{{ standard.example }}
```
{% endif %}

{% endfor %}
{% else %}
#### 命名规范

{% if project_type == "python" %}
- **变量和函数**: snake_case (例: `user_name`, `get_user_info`)
- **类名**: PascalCase (例: `UserManager`, `DatabaseConnection`)
- **常量**: UPPER_SNAKE_CASE (例: `MAX_RETRY_COUNT`)
- **私有方法**: 以下划线开头 (例: `_internal_method`)
{% elif project_type == "javascript" or project_type == "typescript" %}
- **变量和函数**: camelCase (例: `userName`, `getUserInfo`)
- **类名**: PascalCase (例: `UserManager`, `DatabaseConnection`)
- **常量**: UPPER_SNAKE_CASE (例: `MAX_RETRY_COUNT`)
- **组件名**: PascalCase (例: `UserProfile`, `LoginForm`)
{% elif project_type == "java" %}
- **变量和方法**: camelCase (例: `userName`, `getUserInfo`)
- **类名**: PascalCase (例: `UserManager`, `DatabaseConnection`)
- **常量**: UPPER_SNAKE_CASE (例: `MAX_RETRY_COUNT`)
- **包名**: 小写字母 (例: `com.company.project`)
{% endif %}

#### 注释规范

- 所有公共方法必须有文档注释
- 复杂业务逻辑需要添加行内注释
- 注释应该解释"为什么"而不是"是什么"

{% if project_type == "python" %}
```python
def calculate_user_score(user_id: int, include_bonus: bool = False) -> float:
    """计算用户积分
    
    Args:
        user_id: 用户ID
        include_bonus: 是否包含奖励积分
        
    Returns:
        用户总积分
        
    Raises:
        UserNotFoundError: 用户不存在时抛出
    """
    # 实现代码...
```
{% elif project_type == "javascript" or project_type == "typescript" %}
```javascript
/**
 * 计算用户积分
 * @param {number} userId - 用户ID
 * @param {boolean} includeBonus - 是否包含奖励积分
 * @returns {Promise<number>} 用户总积分
 * @throws {UserNotFoundError} 用户不存在时抛出
 */
async function calculateUserScore(userId, includeBonus = false) {
    // 实现代码...
}
```
{% elif project_type == "java" %}
```java
/**
 * 计算用户积分
 * @param userId 用户ID
 * @param includeBonus 是否包含奖励积分
 * @return 用户总积分
 * @throws UserNotFoundException 用户不存在时抛出
 */
public double calculateUserScore(int userId, boolean includeBonus) {
    // 实现代码...
}
```
{% endif %}
{% endif %}

### Git 工作流

#### 分支策略

- **main/master**: 生产环境分支，只接受来自develop的合并
- **develop**: 开发主分支，包含最新的开发功能
- **feature/***: 功能分支，从develop创建，完成后合并回develop
- **hotfix/***: 紧急修复分支，从main创建，修复后合并到main和develop
- **release/***: 发布分支，从develop创建，用于发布准备

#### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明:**
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具或辅助工具的变动

**示例:**
```
feat(auth): add user login functionality

Implement JWT-based authentication system with login and logout endpoints.
Includes password hashing and token validation.

Closes #123
```

### 代码审查

#### 审查清单

- [ ] 代码符合项目规范
- [ ] 功能实现正确且完整
- [ ] 包含适当的测试用例
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性考虑

#### 审查流程

1. 创建 Pull Request
2. 自动化测试通过
3. 至少1名团队成员审查
4. 解决所有审查意见
5. 合并到目标分支

## 测试指南

### 测试策略

{% if test_strategy %}
{{ test_strategy }}
{% else %}
- **单元测试**: 测试独立的函数和类
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程
- **性能测试**: 验证系统性能指标
{% endif %}

### 运行测试

```bash
{% if project_type == "python" %}
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_user.py

# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行性能测试
pytest tests/performance/
{% elif project_type == "javascript" or project_type == "typescript" %}
# 运行所有测试
npm test

# 运行特定测试
npm test -- --grep "user login"

# 生成覆盖率报告
npm run test:coverage

# 运行端到端测试
npm run test:e2e
{% elif project_type == "java" %}
# Maven项目
mvn test

# Gradle项目
./gradlew test

# 生成覆盖率报告
mvn jacoco:report
{% endif %}
```

### 测试编写指南

{% if project_type == "python" %}
```python
import pytest
from src.user_service import UserService

class TestUserService:
    def setup_method(self):
        self.user_service = UserService()
    
    def test_create_user_success(self):
        # Arrange
        user_data = {"name": "John", "email": "<EMAIL>"}
        
        # Act
        result = self.user_service.create_user(user_data)
        
        # Assert
        assert result.success is True
        assert result.user_id is not None
    
    def test_create_user_invalid_email(self):
        # Arrange
        user_data = {"name": "John", "email": "invalid-email"}
        
        # Act & Assert
        with pytest.raises(ValidationError):
            self.user_service.create_user(user_data)
```
{% elif project_type == "javascript" or project_type == "typescript" %}
```javascript
import { describe, it, expect, beforeEach } from 'vitest';
import { UserService } from '../src/userService';

describe('UserService', () => {
    let userService;
    
    beforeEach(() => {
        userService = new UserService();
    });
    
    it('should create user successfully', async () => {
        // Arrange
        const userData = { name: 'John', email: '<EMAIL>' };
        
        // Act
        const result = await userService.createUser(userData);
        
        // Assert
        expect(result.success).toBe(true);
        expect(result.userId).toBeDefined();
    });
    
    it('should throw error for invalid email', async () => {
        // Arrange
        const userData = { name: 'John', email: 'invalid-email' };
        
        // Act & Assert
        await expect(userService.createUser(userData))
            .rejects.toThrow('Invalid email format');
    });
});
```
{% elif project_type == "java" %}
```java
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class UserServiceTest {
    private UserService userService;
    
    @BeforeEach
    void setUp() {
        userService = new UserService();
    }
    
    @Test
    void shouldCreateUserSuccessfully() {
        // Arrange
        UserData userData = new UserData("John", "<EMAIL>");
        
        // Act
        UserResult result = userService.createUser(userData);
        
        // Assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getUserId());
    }
    
    @Test
    void shouldThrowExceptionForInvalidEmail() {
        // Arrange
        UserData userData = new UserData("John", "invalid-email");
        
        // Act & Assert
        assertThrows(ValidationException.class, () -> {
            userService.createUser(userData);
        });
    }
}
```
{% endif %}

## 调试技巧

### 日志记录

{% if project_type == "python" %}
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 使用日志
logger.info("用户登录成功", extra={"user_id": user.id})
logger.error("数据库连接失败", exc_info=True)
```
{% elif project_type == "javascript" or project_type == "typescript" %}
```javascript
// 使用winston或类似的日志库
const winston = require('winston');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.File({ filename: 'error.log', level: 'error' }),
        new winston.transports.File({ filename: 'combined.log' })
    ]
});

logger.info('用户登录成功', { userId: user.id });
logger.error('数据库连接失败', error);
```
{% endif %}

### 性能分析

{% if performance_tools %}
{% for tool in performance_tools %}
#### {{ tool.name }}
{{ tool.description }}

使用方法:
```bash
{{ tool.usage }}
```
{% endfor %}
{% else %}
- **性能监控**: 使用APM工具监控应用性能
- **内存分析**: 定期检查内存使用情况
- **数据库优化**: 监控SQL查询性能
- **网络延迟**: 分析API响应时间
{% endif %}

## 部署流程

### 构建准备

```bash
{% if project_type == "python" %}
# 更新依赖文件
pip freeze > requirements.txt

# 运行完整测试套件
pytest

# 代码质量检查
flake8 src/
black --check src/
{% elif project_type == "javascript" or project_type == "typescript" %}
# 安装生产依赖
npm ci --only=production

# 构建项目
npm run build

# 运行测试
npm test

# 代码质量检查
npm run lint
{% elif project_type == "java" %}
# Maven构建
mvn clean package

# Gradle构建
./gradlew build

# 运行测试
mvn test
{% endif %}
```

### 环境配置

{% if deployment_environments %}
{% for env in deployment_environments %}
#### {{ env.name }}环境

- **用途**: {{ env.purpose }}
- **部署方式**: {{ env.deployment_method }}
- **访问地址**: {{ env.url }}

配置要点:
{% for config in env.configurations %}
- {{ config }}
{% endfor %}

{% endfor %}
{% else %}
#### 开发环境 (Development)
- 用于日常开发和调试
- 自动部署from develop分支
- 包含调试信息和详细日志

#### 测试环境 (Staging)
- 用于集成测试和预发布验证
- 部署from release分支
- 模拟生产环境配置

#### 生产环境 (Production)
- 正式服务环境
- 部署from main分支
- 高可用性和性能优化配置
{% endif %}

---

*此文档由 GenDocs 自动生成于 {{ generation_date }}*