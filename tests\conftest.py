"""
pytest 配置文件

定义全局测试装置(fixtures)和配置。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Generator
from unittest.mock import Mock, patch

from gendocs.config import ConfigManager
from gendocs.utils import get_error_handler, setup_logging, LoggerConfig, LogLevel
from gendocs.monitoring import get_metrics_collector, reset_global_metrics_collector


@pytest.fixture(scope="session")
def test_config():
    """测试配置"""
    return {
        "ai": {
            "enabled": False,  # 测试时禁用AI
            "provider": "mock",
            "api_key": "test-key",
            "model": "test-model",
            "temperature": 0.3,
            "max_tokens": 1000
        },
        "generation": {
            "output_dir": "test_docs",
            "concurrent_jobs": 1,
            "include_ai_enhanced": False
        },
        "backup": {
            "enabled": True,
            "max_backups": 5,
            "strategy": "timestamp"
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    }


@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    if temp_path.exists():
        shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def config_manager(test_config, temp_dir):
    """配置管理器fixture"""
    manager = ConfigManager()
    manager._config = test_config.copy()
    manager._config_file = temp_dir / "test_config.yaml"
    return manager


@pytest.fixture
def mock_project_dir(temp_dir):
    """模拟项目目录fixture"""
    project_dir = temp_dir / "test_project"
    project_dir.mkdir()
    
    # 创建基本项目结构
    (project_dir / "src").mkdir()
    (project_dir / "src" / "main.py").write_text("print('Hello World')")
    (project_dir / "src" / "__init__.py").write_text("")
    
    (project_dir / "tests").mkdir()
    (project_dir / "tests" / "test_main.py").write_text("def test_example(): pass")
    
    (project_dir / "README.md").write_text("# Test Project\n\nThis is a test project.")
    (project_dir / "requirements.txt").write_text("requests>=2.25.0\npytest>=6.0.0")
    (project_dir / "setup.py").write_text("""
from setuptools import setup, find_packages

setup(
    name="test-project",
    version="1.0.0",
    packages=find_packages(),
    install_requires=["requests"],
)
""")
    
    return project_dir


@pytest.fixture
def sample_analysis_result():
    """示例分析结果fixture"""
    from gendocs.analyzers.base import ProjectAnalysisResult
    from gendocs.analyzers.types import ProjectType, FrameworkType
    
    return ProjectAnalysisResult(
        project_name="test-project",
        project_type=ProjectType.LIBRARY,
        framework=FrameworkType.NONE,
        version="1.0.0",
        description="Test project for unit tests",
        author="Test Author",
        license="MIT"
    )


@pytest.fixture
def mock_ai_provider():
    """模拟AI提供商fixture"""
    mock_provider = Mock()
    mock_provider.generate_content.return_value = "Generated AI content for testing"
    mock_provider.__class__.__name__ = "MockAIProvider"
    return mock_provider


@pytest.fixture(autouse=True)
def setup_test_logging():
    """为每个测试设置日志"""
    # 设置测试专用日志配置
    log_config = LoggerConfig(
        level=LogLevel.DEBUG,
        enable_file_logging=False,  # 测试时不写文件
        enable_console_logging=False,  # 测试时不输出到控制台
        enable_json_logging=False
    )
    setup_logging(log_config)


@pytest.fixture(autouse=True)
def reset_globals():
    """重置全局状态"""
    # 重置全局指标收集器
    reset_global_metrics_collector()
    
    yield
    
    # 测试后清理
    reset_global_metrics_collector()


@pytest.fixture
def metrics_collector():
    """指标收集器fixture"""
    collector = get_metrics_collector()
    collector.reset_metrics()  # 确保干净状态
    return collector


@pytest.fixture
def sample_config_file(temp_dir):
    """示例配置文件fixture"""
    config_content = """
# GenDocs 测试配置文件

ai:
  enabled: true
  provider: openai
  api_key: "${TEST_API_KEY}"
  model: gpt-4o-mini
  temperature: 0.3
  max_tokens: 2000

generation:
  output_dir: docs
  concurrent_jobs: 2
  include_ai_enhanced: true
  default_generators:
    - overview
    - architecture
    - api_docs

backup:
  enabled: true
  max_backups: 10
  strategy: timestamp

logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
"""
    
    config_file = temp_dir / "gendocs.yaml"
    config_file.write_text(config_content)
    return config_file


@pytest.fixture
def sample_project_files():
    """示例项目文件内容"""
    return {
        "main.py": '''
"""主程序模块"""

import json
import logging
from pathlib import Path


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, config_file: str = "config.json"):
        """初始化数据处理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.data = {}
    
    def load_data(self, file_path: Path) -> dict:
        """加载数据文件
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            加载的数据字典
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 数据格式错误
        """
        if not file_path.exists():
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.logger.info(f"成功加载数据: {file_path}")
            return data
        except json.JSONDecodeError as e:
            raise ValueError(f"数据格式错误: {e}")
    
    def process_data(self, data: dict) -> dict:
        """处理数据
        
        Args:
            data: 输入数据
            
        Returns:
            处理后的数据
        """
        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = value.upper()
            elif isinstance(value, (int, float)):
                result[key] = value * 2
            else:
                result[key] = value
        
        return result


def main():
    """主函数"""
    processor = DataProcessor()
    print("数据处理器启动")


if __name__ == "__main__":
    main()
''',
        "utils.py": '''
"""工具模块"""

import re
import hashlib
from typing import List, Optional, Union


def validate_email(email: str) -> bool:
    """验证邮箱格式
    
    Args:
        email: 邮箱地址
        
    Returns:
        是否为有效邮箱
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def calculate_hash(data: Union[str, bytes], algorithm: str = "md5") -> str:
    """计算数据哈希值
    
    Args:
        data: 要计算哈希的数据
        algorithm: 哈希算法
        
    Returns:
        哈希值字符串
    """
    if isinstance(data, str):
        data = data.encode('utf-8')
    
    if algorithm == "md5":
        return hashlib.md5(data).hexdigest()
    elif algorithm == "sha256":
        return hashlib.sha256(data).hexdigest()
    else:
        raise ValueError(f"不支持的哈希算法: {algorithm}")


def filter_list(items: List[str], pattern: str) -> List[str]:
    """过滤列表项
    
    Args:
        items: 字符串列表
        pattern: 过滤模式
        
    Returns:
        过滤后的列表
    """
    return [item for item in items if pattern in item]


class ConfigLoader:
    """配置加载器"""
    
    @staticmethod
    def load_from_dict(config_dict: dict) -> dict:
        """从字典加载配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            处理后的配置
        """
        result = {}
        for key, value in config_dict.items():
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                # 环境变量替换
                import os
                env_var = value[2:-1]
                result[key] = os.getenv(env_var, value)
            else:
                result[key] = value
        return result
''',
        "api.py": '''
"""API模块"""

from typing import Dict, Any, Optional
import json


class APIClient:
    """API客户端"""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        """初始化API客户端
        
        Args:
            base_url: API基础URL
            api_key: API密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'GenDocs-TestClient/1.0'
        }
        
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求
        
        Args:
            endpoint: API端点
            params: 查询参数
            
        Returns:
            API响应数据
        """
        # 模拟API响应
        return {
            'status': 'success',
            'data': {
                'endpoint': endpoint,
                'method': 'GET',
                'params': params or {}
            }
        }
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            API响应数据
        """
        # 模拟API响应
        return {
            'status': 'success',
            'data': {
                'endpoint': endpoint,
                'method': 'POST',
                'payload': data or {}
            }
        }


def handle_api_error(response: Dict[str, Any]) -> None:
    """处理API错误
    
    Args:
        response: API响应
        
    Raises:
        ValueError: API返回错误
    """
    if response.get('status') != 'success':
        error_msg = response.get('message', '未知API错误')
        raise ValueError(f"API错误: {error_msg}")
'''
    }


@pytest.fixture
def complex_project_dir(temp_dir, sample_project_files):
    """复杂项目目录fixture"""
    project_dir = temp_dir / "complex_project"
    project_dir.mkdir()
    
    # 创建复杂项目结构
    src_dir = project_dir / "src" / "myproject"
    src_dir.mkdir(parents=True)
    
    # 写入示例文件
    for filename, content in sample_project_files.items():
        (src_dir / filename).write_text(content)
    
    (src_dir / "__init__.py").write_text('"""MyProject包"""')
    
    # 创建测试目录
    tests_dir = project_dir / "tests"
    tests_dir.mkdir()
    (tests_dir / "__init__.py").write_text("")
    (tests_dir / "test_main.py").write_text("""
import pytest
from src.myproject.main import DataProcessor

def test_data_processor():
    processor = DataProcessor()
    assert processor.config_file == "config.json"

def test_process_data():
    processor = DataProcessor()
    test_data = {"name": "test", "value": 10}
    result = processor.process_data(test_data)
    assert result["name"] == "TEST"
    assert result["value"] == 20
""")
    
    # 创建配置和文档文件
    (project_dir / "README.md").write_text("""# MyProject

这是一个复杂的测试项目，用于验证GenDocs的功能。

## 功能特性

- 数据处理
- API客户端
- 工具函数

## 安装

```bash
pip install -r requirements.txt
```

## 使用方法

```python
from myproject.main import DataProcessor

processor = DataProcessor()
data = processor.load_data("data.json")
result = processor.process_data(data)
```
""")
    
    (project_dir / "requirements.txt").write_text("""
requests>=2.25.0
pytest>=6.0.0
pyyaml>=5.4.0
click>=8.0.0
""")
    
    (project_dir / "setup.py").write_text("""
from setuptools import setup, find_packages

setup(
    name="myproject",
    version="2.1.0",
    author="Test Author",
    author_email="<EMAIL>",
    description="一个用于测试GenDocs功能的复杂项目",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
    install_requires=[
        "requests>=2.25.0",
        "pyyaml>=5.4.0",
        "click>=8.0.0",
    ],
    extras_require={
        "dev": ["pytest>=6.0.0", "black", "flake8"],
        "docs": ["sphinx", "sphinx-rtd-theme"],
    },
    entry_points={
        "console_scripts": [
            "myproject=myproject.main:main",
        ],
    },
)
""")
    
    (project_dir / "pyproject.toml").write_text("""
[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 88
target-version = ['py38']
""")
    
    (project_dir / "LICENSE").write_text("""MIT License

Copyright (c) 2024 Test Author

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
""")
    
    return project_dir


# 用于异步测试的fixture
@pytest.fixture
def event_loop():
    """事件循环fixture"""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# 性能测试相关的fixture
@pytest.fixture
def performance_test_config():
    """性能测试配置"""
    return {
        "max_execution_time": 5.0,  # 最大执行时间(秒)
        "memory_limit_mb": 100,     # 内存限制(MB)
        "iterations": 10            # 测试迭代次数
    }


# 标记装饰器
def slow_test(func):
    """标记慢速测试"""
    return pytest.mark.slow(func)


def integration_test(func):
    """标记集成测试"""
    return pytest.mark.integration(func)


def requires_ai(func):
    """标记需要AI的测试"""
    return pytest.mark.ai_required(func)