"""
分析器注册表

管理和注册不同语言的项目分析器。
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Type

from .base import BaseAnalyzer, ProjectType, AnalysisError

logger = logging.getLogger(__name__)


class AnalyzerRegistry:
    """分析器注册表
    
    负责管理和注册不同编程语言的项目分析器。
    """
    
    def __init__(self):
        """初始化注册表"""
        self._analyzers: Dict[ProjectType, Type[BaseAnalyzer]] = {}
        self._analyzer_instances: Dict[ProjectType, BaseAnalyzer] = {}
        self._detection_order: List[ProjectType] = [
            ProjectType.PYTHON,
            ProjectType.TYPESCRIPT,
            ProjectType.JAVASCRIPT,
            ProjectType.JAVA,
            ProjectType.GO,
            ProjectType.RUST,
            ProjectType.CPP
        ]
    
    def register(self, analyzer_class: Type[BaseAnalyzer]) -> None:
        """注册分析器
        
        Args:
            analyzer_class: 分析器类
            
        Raises:
            ValueError: 分析器类无效时抛出
        """
        if not issubclass(analyzer_class, BaseAnalyzer):
            raise ValueError("分析器必须继承BaseAnalyzer")
        
        # 创建实例以获取项目类型
        try:
            instance = analyzer_class()
            project_type = instance.project_type
            
            self._analyzers[project_type] = analyzer_class
            self._analyzer_instances[project_type] = instance
            
            logger.info(f"已注册分析器: {project_type.value} -> {analyzer_class.__name__}")
            
        except Exception as e:
            logger.error(f"注册分析器失败 {analyzer_class.__name__}: {e}")
            raise ValueError(f"无法创建分析器实例: {e}")
    
    def get_analyzer(self, project_type: ProjectType) -> Optional[BaseAnalyzer]:
        """获取指定类型的分析器
        
        Args:
            project_type: 项目类型
            
        Returns:
            分析器实例，如果不存在返回None
        """
        return self._analyzer_instances.get(project_type)
    
    def detect_project_type(self, project_path: Path) -> Optional[BaseAnalyzer]:
        """自动检测项目类型并返回对应分析器
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            匹配的分析器实例，如果无法检测返回None
        """
        if not project_path.exists() or not project_path.is_dir():
            logger.warning(f"无效的项目路径: {project_path}")
            return None
        
        # 按检测顺序尝试匹配
        for project_type in self._detection_order:
            analyzer = self._analyzer_instances.get(project_type)
            if analyzer and analyzer.can_analyze(project_path):
                logger.info(f"检测到项目类型: {project_type.value}")
                return analyzer
        
        logger.warning(f"无法检测项目类型: {project_path}")
        return None
    
    def get_supported_types(self) -> List[ProjectType]:
        """获取支持的项目类型列表
        
        Returns:
            支持的项目类型列表
        """
        return list(self._analyzers.keys())
    
    def analyze_project(self, project_path: Path, project_type: Optional[ProjectType] = None) -> Optional[object]:
        """分析项目
        
        Args:
            project_path: 项目路径
            project_type: 指定项目类型，None则自动检测
            
        Returns:
            项目分析结果，失败返回None
            
        Raises:
            AnalysisError: 分析失败时抛出
        """
        try:
            # 获取分析器
            if project_type:
                analyzer = self.get_analyzer(project_type)
                if not analyzer:
                    raise AnalysisError(f"不支持的项目类型: {project_type}")
            else:
                analyzer = self.detect_project_type(project_path)
                if not analyzer:
                    raise AnalysisError(f"无法检测项目类型: {project_path}")
            
            # 执行分析
            logger.info(f"开始分析项目: {project_path}")
            result = analyzer.analyze_project(project_path)
            logger.info(f"项目分析完成: {result.project_type.value}")
            
            return result
            
        except Exception as e:
            logger.error(f"项目分析失败: {e}")
            raise AnalysisError(f"项目分析失败: {e}")
    
    def clear(self) -> None:
        """清空注册表"""
        self._analyzers.clear()
        self._analyzer_instances.clear()
        logger.info("已清空分析器注册表")
    
    def set_detection_order(self, order: List[ProjectType]) -> None:
        """设置项目类型检测顺序
        
        Args:
            order: 检测顺序列表
        """
        self._detection_order = order.copy()
        logger.info(f"已更新检测顺序: {[t.value for t in order]}")
    
    def get_analyzer_info(self) -> Dict[str, Dict[str, any]]:
        """获取所有注册的分析器信息
        
        Returns:
            分析器信息字典
        """
        info = {}
        for project_type, analyzer in self._analyzer_instances.items():
            info[project_type.value] = {
                "class_name": analyzer.__class__.__name__,
                "supported_extensions": list(analyzer.supported_extensions),
                "can_detect_framework": hasattr(analyzer, 'detect_framework')
            }
        return info


# 全局注册表实例
_registry = AnalyzerRegistry()


def get_registry() -> AnalyzerRegistry:
    """获取全局注册表实例
    
    Returns:
        注册表实例
    """
    return _registry


def register_analyzer(analyzer_class: Type[BaseAnalyzer]) -> None:
    """注册分析器到全局注册表
    
    Args:
        analyzer_class: 分析器类
    """
    _registry.register(analyzer_class)


def auto_register_analyzers() -> None:
    """自动注册内置分析器"""
    try:
        # 导入并注册Python分析器
        from .python_analyzer import PythonAnalyzer
        register_analyzer(PythonAnalyzer)
        
        # 未来可以在这里添加其他语言的分析器
        # from .javascript_analyzer import JavaScriptAnalyzer
        # register_analyzer(JavaScriptAnalyzer)
        
        logger.info("内置分析器自动注册完成")
        
    except ImportError as e:
        logger.warning(f"部分分析器注册失败: {e}")
    except Exception as e:
        logger.error(f"自动注册分析器失败: {e}")
        raise