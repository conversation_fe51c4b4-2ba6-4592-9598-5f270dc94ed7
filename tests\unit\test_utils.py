"""
工具类单元测试
"""

import pytest
import logging
import json
from pathlib import Path
from unittest.mock import Mock, patch, mock_open
from datetime import datetime

from gendocs.utils.error_handler import (
    GenDocsError, ConfigurationError, AIProviderError,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON>, ErrorContext
)
from gendocs.utils.logger import LoggerManager, LogConfig, LogLevel
from gendocs.utils.validators import (
    validate_config, ValidationResult, validate_file_path,
    validate_directory_structure
)


class TestGenDocsError:
    """GenDocs错误类测试"""
    
    def test_init_basic(self):
        """测试基础错误初始化"""
        error = GenDocsError("Test error message")
        assert str(error) == "Test error message"
        assert error.error_code == "GENDOCS_ERROR"
        assert error.severity == ErrorSeverity.MEDIUM
    
    def test_init_with_all_params(self):
        """测试带所有参数的错误初始化"""
        context = ErrorContext(
            operation="test_operation",
            file_path="/test/path",
            additional_info={"key": "value"}
        )
        
        error = GenDocsError(
            "Test error",
            error_code="TEST_ERROR",
            severity=ErrorSeverity.HIGH,
            context=context
        )
        
        assert error.error_code == "TEST_ERROR"
        assert error.severity == ErrorSeverity.HIGH
        assert error.context == context
    
    def test_configuration_error(self):
        """测试配置错误"""
        error = ConfigurationError("Invalid configuration")
        assert isinstance(error, GenDocsError)
        assert error.error_code == "CONFIG_ERROR"
        assert "Invalid configuration" in str(error)
    
    def test_ai_provider_error(self):
        """测试AI提供商错误"""
        error = AIProviderError("API request failed")
        assert isinstance(error, GenDocsError)
        assert error.error_code == "AI_PROVIDER_ERROR"
        assert "API request failed" in str(error)
    
    def test_error_context(self):
        """测试错误上下文"""
        context = ErrorContext(
            operation="document_generation",
            file_path="/project/docs/readme.md",
            additional_info={
                "generator": "overview",
                "project_type": "library"
            }
        )
        
        assert context.operation == "document_generation"
        assert context.file_path == "/project/docs/readme.md"
        assert context.additional_info["generator"] == "overview"


class TestErrorHandler:
    """错误处理器测试"""
    
    def test_init(self):
        """测试错误处理器初始化"""
        handler = ErrorHandler()
        assert handler is not None
        assert hasattr(handler, 'error_stats')
    
    def test_handle_error_basic(self):
        """测试基础错误处理"""
        handler = ErrorHandler()
        error = GenDocsError("Test error")
        
        # 应该不抛出异常
        handler.handle_error(error)
        
        # 验证错误统计
        stats = handler.get_error_statistics()
        assert stats['total_errors'] == 1
        assert stats['by_severity'][ErrorSeverity.MEDIUM.value] == 1
    
    def test_handle_error_with_recovery(self):
        """测试带恢复策略的错误处理"""
        handler = ErrorHandler()
        
        def recovery_strategy():
            return "recovered"
        
        error = GenDocsError("Test error")
        result = handler.handle_error(error, recovery_strategy)
        
        assert result == "recovered"
    
    def test_error_statistics(self):
        """测试错误统计"""
        handler = ErrorHandler()
        
        # 添加不同类型和严重性的错误
        handler.handle_error(GenDocsError("Error 1", severity=ErrorSeverity.LOW))
        handler.handle_error(ConfigurationError("Config error"))
        handler.handle_error(AIProviderError("AI error", severity=ErrorSeverity.HIGH))
        
        stats = handler.get_error_statistics()
        
        assert stats['total_errors'] == 3
        assert stats['by_type']['GENDOCS_ERROR'] == 1
        assert stats['by_type']['CONFIG_ERROR'] == 1
        assert stats['by_type']['AI_PROVIDER_ERROR'] == 1
        assert stats['by_severity'][ErrorSeverity.LOW.value] == 1
        assert stats['by_severity'][ErrorSeverity.HIGH.value] == 1
    
    def test_context_manager(self):
        """测试上下文管理器"""
        handler = ErrorHandler()
        
        with handler.error_context("test_operation", "/test/path"):
            # 在上下文中的操作
            pass
        
        # 验证上下文已清理
        assert handler._current_context is None
    
    def test_context_manager_with_error(self):
        """测试上下文管理器中的错误处理"""
        handler = ErrorHandler()
        
        with pytest.raises(ValueError):
            with handler.error_context("test_operation", "/test/path"):
                raise ValueError("Test exception")
        
        # 验证错误被记录
        stats = handler.get_error_statistics()
        assert stats['total_errors'] == 1


class TestLoggerManager:
    """日志管理器测试"""
    
    def test_init_default(self):
        """测试默认初始化"""
        config = LogConfig()
        manager = LoggerManager(config)
        
        assert manager.config == config
        assert manager.loggers is not None
    
    def test_get_logger(self, temp_dir):
        """测试获取日志器"""
        config = LogConfig(
            log_file_path=temp_dir / "test.log",
            level=LogLevel.DEBUG
        )
        manager = LoggerManager(config)
        
        logger = manager.get_logger("test_logger")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_logger"
        assert logger.level == logging.DEBUG
    
    def test_json_logging(self, temp_dir):
        """测试JSON格式日志"""
        log_file = temp_dir / "json_test.log"
        config = LogConfig(
            log_file_path=log_file,
            enable_json_logging=True
        )
        manager = LoggerManager(config)
        
        logger = manager.get_logger("json_test")
        logger.info("Test message", extra={"custom_field": "value"})
        
        # 验证日志文件存在
        assert log_file.exists()
        
        # 验证JSON格式
        with open(log_file, 'r') as f:
            log_line = f.readline().strip()
            log_data = json.loads(log_line)
            assert log_data['message'] == 'Test message'
            assert log_data['custom_field'] == 'value'
    
    def test_colored_console_logging(self):
        """测试彩色控制台日志"""
        config = LogConfig(
            enable_console_logging=True,
            enable_colored_output=True
        )
        manager = LoggerManager(config)
        
        logger = manager.get_logger("colored_test")
        
        # 应该能够正常记录日志而不抛出异常
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
    
    def test_log_rotation(self, temp_dir):
        """测试日志轮转"""
        log_file = temp_dir / "rotation_test.log"
        config = LogConfig(
            log_file_path=log_file,
            max_file_size=1024,  # 1KB
            backup_count=3
        )
        manager = LoggerManager(config)
        
        logger = manager.get_logger("rotation_test")
        
        # 写入大量日志触发轮转
        for i in range(100):
            logger.info(f"Test message {i} with some additional content to make it longer")
        
        # 验证主日志文件存在
        assert log_file.exists()
    
    def test_multiple_loggers(self, temp_dir):
        """测试多个日志器"""
        config = LogConfig(log_file_path=temp_dir / "multi_test.log")
        manager = LoggerManager(config)
        
        logger1 = manager.get_logger("test1")
        logger2 = manager.get_logger("test2")
        logger3 = manager.get_logger("test1")  # 重复获取
        
        assert logger1 is not logger2
        assert logger1 is logger3  # 应该返回同一个实例
    
    def test_setup_logging_integration(self, temp_dir):
        """测试日志设置集成"""
        from gendocs.utils.logger import setup_logging
        
        log_file = temp_dir / "integration_test.log"
        logger = setup_logging(
            log_file=log_file,
            level="DEBUG",
            enable_json=True
        )
        
        assert isinstance(logger, logging.Logger)
        logger.info("Integration test message")
        assert log_file.exists()


class TestValidators:
    """验证器测试"""
    
    def test_validate_config_valid(self, test_config):
        """测试有效配置验证"""
        result = validate_config(test_config)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_config_missing_sections(self):
        """测试缺少配置段的验证"""
        invalid_config = {
            'generation': {
                'output_dir': 'docs'
            }
            # 缺少 'ai' 配置
        }
        
        result = validate_config(invalid_config)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any('ai' in error.lower() for error in result.errors)
    
    def test_validate_config_invalid_values(self):
        """测试无效值的配置验证"""
        invalid_config = {
            'ai': {
                'provider': 'invalid_provider',
                'temperature': 5.0,  # 超出范围
                'max_tokens': -100   # 负数
            },
            'generation': {
                'output_dir': ''
            }
        }
        
        result = validate_config(invalid_config)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_file_path_valid(self, temp_dir):
        """测试有效文件路径验证"""
        test_file = temp_dir / "test.txt"
        test_file.write_text("test content")
        
        result = validate_file_path(test_file)
        assert result.is_valid is True
    
    def test_validate_file_path_nonexistent(self, temp_dir):
        """测试不存在文件路径验证"""
        nonexistent_file = temp_dir / "nonexistent.txt"
        
        result = validate_file_path(nonexistent_file, must_exist=True)
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_directory_structure_valid(self, complex_project_dir):
        """测试有效目录结构验证"""
        result = validate_directory_structure(complex_project_dir)
        assert result.is_valid is True
    
    def test_validate_directory_structure_invalid(self, temp_dir):
        """测试无效目录结构验证"""
        empty_dir = temp_dir / "empty"
        empty_dir.mkdir()
        
        result = validate_directory_structure(empty_dir)
        # 根据实际验证逻辑调整期望结果
        assert isinstance(result, ValidationResult)
    
    def test_validation_result(self):
        """测试验证结果类"""
        # 测试有效结果
        valid_result = ValidationResult()
        assert valid_result.is_valid is True
        assert len(valid_result.errors) == 0
        assert len(valid_result.warnings) == 0
        
        # 测试无效结果
        invalid_result = ValidationResult(
            errors=["Error 1", "Error 2"],
            warnings=["Warning 1"]
        )
        assert invalid_result.is_valid is False
        assert len(invalid_result.errors) == 2
        assert len(invalid_result.warnings) == 1
    
    def test_validation_result_add_methods(self):
        """测试验证结果添加方法"""
        result = ValidationResult()
        
        result.add_error("Test error")
        result.add_warning("Test warning")
        
        assert len(result.errors) == 1
        assert len(result.warnings) == 1
        assert result.is_valid is False
    
    def test_validation_result_string_representation(self):
        """测试验证结果字符串表示"""
        result = ValidationResult(
            errors=["Error 1"],
            warnings=["Warning 1"]
        )
        
        str_repr = str(result)
        assert "Error 1" in str_repr
        assert "Warning 1" in str_repr


class TestUtilsIntegration:
    """工具类集成测试"""
    
    def test_error_handling_with_logging(self, temp_dir):
        """测试错误处理与日志集成"""
        log_file = temp_dir / "error_integration.log"
        log_config = LogConfig(
            log_file_path=log_file,
            enable_json_logging=True
        )
        logger_manager = LoggerManager(log_config)
        logger = logger_manager.get_logger("error_test")
        
        error_handler = ErrorHandler()
        
        # 处理错误并记录日志
        error = GenDocsError("Integration test error")
        error_handler.handle_error(error)
        logger.error(f"Handled error: {error}")
        
        # 验证日志文件
        assert log_file.exists()
        
        # 验证错误统计
        stats = error_handler.get_error_statistics()
        assert stats['total_errors'] == 1
    
    def test_config_validation_with_error_handling(self):
        """测试配置验证与错误处理集成"""
        error_handler = ErrorHandler()
        
        invalid_config = {
            'ai': {
                'provider': 'invalid',
                'temperature': 99.0
            }
        }
        
        try:
            result = validate_config(invalid_config)
            if not result.is_valid:
                for error_msg in result.errors:
                    error = ConfigurationError(error_msg)
                    error_handler.handle_error(error)
        except Exception as e:
            error_handler.handle_error(GenDocsError(str(e)))
        
        stats = error_handler.get_error_statistics()
        assert stats['total_errors'] > 0
    
    @patch('gendocs.utils.logger.datetime')
    def test_logging_timestamps(self, mock_datetime, temp_dir):
        """测试日志时间戳"""
        # Mock时间戳
        fixed_time = datetime(2023, 1, 1, 12, 0, 0)
        mock_datetime.now.return_value = fixed_time
        mock_datetime.strftime = datetime.strftime
        
        log_file = temp_dir / "timestamp_test.log"
        config = LogConfig(
            log_file_path=log_file,
            enable_json_logging=True
        )
        manager = LoggerManager(config)
        logger = manager.get_logger("timestamp_test")
        
        logger.info("Timestamp test message")
        
        # 验证日志文件包含时间戳
        assert log_file.exists()
        with open(log_file, 'r') as f:
            log_content = f.read()
            # 根据实际的时间戳格式调整验证逻辑
            assert '2023' in log_content or 'timestamp' in log_content.lower()