# {{ project_name }} - 运维手册 (Runbook)

## 概览

本文档为 {{ project_name }} 的运维人员提供系统运维、故障处理和应急响应的操作指南。

{{ ai_generated_content if ai_generated_content else "# 运维手册

## 🚨 应急响应

### 服务不可用

**现象**: 用户无法访问系统
**处理步骤**:
1. 检查负载均衡器状态
2. 验证应用服务器运行状态
3. 检查数据库连接
4. 查看系统资源使用情况

### 性能问题

**现象**: 响应时间显著增加
**处理步骤**:
1. 检查系统负载
2. 分析慢查询日志
3. 检查缓存命中率
4. 验证网络连接状态" }}

## 🔧 日常维护操作

### 系统状态检查

#### 基础检查脚本

```bash
#!/bin/bash
# health_check.sh - 系统健康检查脚本

echo "=== 系统健康检查 ==="
echo "时间: $(date)"
echo

# 检查系统负载
echo "1. 系统负载:"
uptime

# 检查磁盘空间
echo -e "\n2. 磁盘使用情况:"
df -h

# 检查内存使用
echo -e "\n3. 内存使用情况:"
free -h

# 检查关键服务状态
echo -e "\n4. 服务状态:"
{% if project_type == "python" %}
systemctl status {{ project_name }} --no-pager -l
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 status
{% elif project_type == "java" %}
systemctl status {{ project_name }} --no-pager -l
{% endif %}

echo -e "\n5. 数据库状态:"
systemctl status postgresql --no-pager -l

echo -e "\n6. 缓存服务状态:"
systemctl status redis --no-pager -l

echo -e "\n7. Web服务器状态:"
systemctl status nginx --no-pager -l

# 检查网络连接
echo -e "\n8. 网络连接检查:"
{% if database_host %}
nc -zv {{ database_host }} 5432
{% else %}
nc -zv localhost 5432
{% endif %}
nc -zv localhost 6379

echo -e "\n=== 检查完成 ==="
```

### 应用重启操作

{% if restart_procedures %}
{% for procedure in restart_procedures %}
#### {{ procedure.service }}

```bash
# {{ procedure.description }}
{{ procedure.commands }}
```

**验证步骤**:
{% for step in procedure.verification %}
- {{ step }}
{% endfor %}

{% endfor %}
{% else %}
#### 应用服务重启

```bash
{% if project_type == "python" %}
# 重启应用服务
sudo systemctl restart {{ project_name }}

# 检查服务状态
sudo systemctl status {{ project_name }}

# 查看启动日志
sudo journalctl -u {{ project_name }} -f
{% elif project_type == "javascript" or project_type == "typescript" %}
# 重启PM2管理的应用
pm2 restart {{ project_name }}

# 检查应用状态
pm2 status

# 查看应用日志
pm2 logs {{ project_name }}
{% elif project_type == "java" %}
# 重启Spring Boot应用
sudo systemctl restart {{ project_name }}

# 检查服务状态
sudo systemctl status {{ project_name }}

# 查看应用日志
sudo journalctl -u {{ project_name }} -f
{% endif %}
```

#### 数据库重启

```bash
# 重启PostgreSQL
sudo systemctl restart postgresql

# 检查数据库状态
sudo systemctl status postgresql

# 测试数据库连接
sudo -u postgres psql -c "SELECT version();"
```

#### 缓存服务重启

```bash
# 重启Redis
sudo systemctl restart redis

# 检查Redis状态
sudo systemctl status redis

# 测试Redis连接
redis-cli ping
```
{% endif %}

## 📊 监控和告警

### 关键指标监控

{% if monitoring_metrics %}
{% for metric in monitoring_metrics %}
#### {{ metric.name }}

**描述**: {{ metric.description }}
**正常范围**: {{ metric.normal_range }}
**警告阈值**: {{ metric.warning_threshold }}
**严重阈值**: {{ metric.critical_threshold }}

**监控命令**:
```bash
{{ metric.monitoring_command }}
```

{% endfor %}
{% else %}
#### 系统资源监控

**CPU使用率**
- 正常范围: < 70%
- 警告阈值: 70-85%
- 严重阈值: > 85%

```bash
# 查看CPU使用率
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
```

**内存使用率**
- 正常范围: < 80%
- 警告阈值: 80-90%
- 严重阈值: > 90%

```bash
# 查看内存使用率
free | grep Mem | awk '{printf("%.2f%%\n", $3/$2 * 100.0)}'
```

**磁盘使用率**
- 正常范围: < 80%
- 警告阈值: 80-90%
- 严重阈值: > 90%

```bash
# 查看磁盘使用率
df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{print $5 " " $1}' | while read output;
do
  echo $output
done
```
{% endif %}

### 应用性能监控

#### 响应时间监控

```bash
#!/bin/bash
# response_time_check.sh

URL="{{ app_url if app_url else 'http://localhost:8080/health' }}"
THRESHOLD=2000  # 2秒阈值

response_time=$(curl -o /dev/null -s -w '%{time_total}\n' $URL)
response_time_ms=$(echo "$response_time * 1000" | bc)

echo "响应时间: ${response_time_ms}ms"

if (( $(echo "$response_time_ms > $THRESHOLD" | bc -l) )); then
    echo "警告: 响应时间超过阈值 (${THRESHOLD}ms)"
    # 可以在此处添加告警通知逻辑
fi
```

#### 数据库性能监控

```sql
-- 慢查询监控
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 连接数监控
SELECT count(*) as total_connections,
       sum(case when state = 'active' then 1 else 0 end) as active_connections
FROM pg_stat_activity;

-- 数据库大小监控
SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
FROM pg_database
ORDER BY pg_database_size(datname) DESC;
```

## 🚨 故障处理指南

### 常见故障处理

{% if troubleshooting_procedures %}
{% for issue in troubleshooting_procedures %}
#### {{ issue.title }}

**故障现象**:
{% for symptom in issue.symptoms %}
- {{ symptom }}
{% endfor %}

**可能原因**:
{% for cause in issue.possible_causes %}
- {{ cause }}
{% endfor %}

**诊断步骤**:
{% for step in issue.diagnostic_steps %}
{{ loop.index }}. {{ step }}
{% endfor %}

**解决方案**:
{% for solution in issue.solutions %}
- **{{ solution.title }}**: {{ solution.description }}
  ```bash
  {{ solution.commands }}
  ```
{% endfor %}

**预防措施**:
{% for prevention in issue.prevention_measures %}
- {{ prevention }}
{% endfor %}

---

{% endfor %}
{% else %}
#### 应用服务无响应

**故障现象**:
- HTTP请求超时
- 负载均衡器健康检查失败
- 用户报告无法访问

**诊断步骤**:
1. 检查应用进程状态
2. 查看应用日志
3. 检查系统资源使用
4. 验证数据库连接

**解决方案**:
```bash
# 检查应用状态
{% if project_type == "python" %}
sudo systemctl status {{ project_name }}
sudo journalctl -u {{ project_name }} --since "5 minutes ago"
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 status
pm2 logs {{ project_name }} --lines 50
{% endif %}

# 重启应用
{% if project_type == "python" %}
sudo systemctl restart {{ project_name }}
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 restart {{ project_name }}
{% endif %}
```

#### 数据库连接问题

**故障现象**:
- 应用报告数据库连接错误
- 数据库查询超时
- 连接池耗尽

**诊断步骤**:
1. 检查数据库服务状态
2. 查看数据库连接数
3. 检查数据库日志
4. 验证网络连接

**解决方案**:
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 查看连接数
sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"

# 检查慢查询
sudo -u postgres psql -c "SELECT query, state, query_start FROM pg_stat_activity WHERE state != 'idle';"

# 重启数据库（谨慎操作）
sudo systemctl restart postgresql
```

#### 内存不足问题

**故障现象**:
- 系统响应缓慢
- OOM Killer被触发
- 应用频繁重启

**诊断步骤**:
1. 检查内存使用情况
2. 识别内存消耗大的进程
3. 分析内存泄漏可能性

**解决方案**:
```bash
# 查看内存使用
free -h
top -o %MEM

# 查看进程内存使用
ps aux --sort=-%mem | head -20

# 清理缓存（临时措施）
echo 3 | sudo tee /proc/sys/vm/drop_caches

# 重启高内存消耗的服务
{% if project_type == "python" %}
sudo systemctl restart {{ project_name }}
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 restart {{ project_name }}
{% endif %}
```
{% endif %}

## 🔐 安全操作

### 日常安全检查

```bash
#!/bin/bash
# security_check.sh - 安全检查脚本

echo "=== 安全检查 ==="

# 检查失败的登录尝试
echo "1. 近期失败的SSH登录尝试:"
grep "Failed password" /var/log/auth.log | tail -10

# 检查系统更新
echo -e "\n2. 可用系统更新:"
apt list --upgradable 2>/dev/null | grep -v "WARNING"

# 检查防火墙状态
echo -e "\n3. 防火墙状态:"
ufw status

# 检查监听端口
echo -e "\n4. 监听端口:"
netstat -tuln | grep LISTEN

# 检查文件权限
echo -e "\n5. 关键文件权限检查:"
ls -la /etc/passwd /etc/shadow /etc/ssh/sshd_config

echo -e "\n=== 安全检查完成 ==="
```

### 证书管理

```bash
# 检查SSL证书过期时间
echo | openssl s_client -servername {{ domain_name if domain_name else "example.com" }} -connect {{ domain_name if domain_name else "example.com" }}:443 2>/dev/null | openssl x509 -noout -dates

# Let's Encrypt证书续期
sudo certbot renew --dry-run

# 手动续期特定证书
sudo certbot renew --cert-name {{ domain_name if domain_name else "example.com" }}
```

## 📂 备份和恢复

### 数据库备份

#### 自动备份脚本

```bash
#!/bin/bash
# backup_database.sh - 数据库备份脚本

BACKUP_DIR="/var/backups/{{ project_name }}"
DB_NAME="{{ project_name }}"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump $DB_NAME > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

# 发送备份状态通知
if [ $? -eq 0 ]; then
    echo "数据库备份成功: $BACKUP_FILE.gz"
else
    echo "数据库备份失败!" >&2
    exit 1
fi
```

#### 数据库恢复

```bash
# 停止应用服务
{% if project_type == "python" %}
sudo systemctl stop {{ project_name }}
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 stop {{ project_name }}
{% endif %}

# 创建数据库备份（安全措施）
pg_dump {{ project_name }} > /tmp/pre_restore_backup.sql

# 恢复数据库
gunzip -c /path/to/backup.sql.gz | psql {{ project_name }}

# 重启应用服务
{% if project_type == "python" %}
sudo systemctl start {{ project_name }}
{% elif project_type == "javascript" or project_type == "typescript" %}
pm2 start {{ project_name }}
{% endif %}

# 验证恢复结果
# 检查关键数据是否正确恢复
```

### 应用文件备份

```bash
#!/bin/bash
# backup_application.sh - 应用文件备份脚本

APP_DIR="/home/<USER>/{{ project_name }}"
BACKUP_DIR="/var/backups/{{ project_name }}/app"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件（排除不必要的文件）
tar -czf "$BACKUP_DIR/app_backup_$DATE.tar.gz" \
    --exclude="*.log" \
    --exclude="__pycache__" \
    --exclude="node_modules" \
    --exclude=".git" \
    -C $(dirname $APP_DIR) $(basename $APP_DIR)

# 删除30天前的备份
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +30 -delete

echo "应用备份完成: $BACKUP_DIR/app_backup_$DATE.tar.gz"
```

## 📞 联系信息和升级路径

### 紧急联系人

{% if emergency_contacts %}
{% for contact in emergency_contacts %}
- **{{ contact.role }}**: {{ contact.name }}
  - 电话: {{ contact.phone }}
  - 邮箱: {{ contact.email }}
  - 工作时间: {{ contact.hours }}
{% endfor %}
{% else %}
- **系统管理员**: [姓名]
  - 电话: [电话号码]
  - 邮箱: [邮箱地址]
  - 24小时待命

- **应用负责人**: [姓名]
  - 电话: [电话号码]
  - 邮箱: [邮箱地址]
  - 工作时间: 9:00-18:00

- **数据库管理员**: [姓名]
  - 电话: [电话号码]
  - 邮箱: [邮箱地址]
  - 紧急情况24小时响应
{% endif %}

### 升级路径

#### 严重程度定义

- **P0 (严重)**: 系统完全不可用，影响所有用户
- **P1 (高)**: 核心功能不可用，影响大部分用户
- **P2 (中)**: 部分功能异常，影响部分用户
- **P3 (低)**: 轻微问题，不影响核心功能

#### 升级流程

1. **P0/P1问题**: 立即联系紧急联系人
2. **P2问题**: 2小时内联系相关负责人
3. **P3问题**: 工作时间内通过正常渠道报告

### 外部依赖联系方式

{% if external_dependencies %}
{% for dep in external_dependencies %}
- **{{ dep.name }}**: 
  - 支持电话: {{ dep.support_phone }}
  - 支持邮箱: {{ dep.support_email }}
  - 文档地址: {{ dep.documentation_url }}
{% endfor %}
{% else %}
- **云服务提供商**: [联系方式]
- **CDN提供商**: [联系方式]
- **第三方API服务**: [联系方式]
{% endif %}

---

*此文档由 GenDocs 结合AI分析自动生成于 {{ generation_date }}*

**最后更新**: {{ generation_date }}
**文档版本**: 1.0
**负责人**: [运维团队]