"""
日志配置模块

提供统一的日志配置和管理功能。
"""

import logging
import logging.handlers
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import json


class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LoggerConfig:
    """日志配置"""
    level: LogLevel = LogLevel.INFO
    format_string: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    
    # 文件日志配置
    enable_file_logging: bool = True
    log_file_path: Path = Path(".gendocs/logs/gendocs.log")
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # 控制台日志配置
    enable_console_logging: bool = True
    console_level: Optional[LogLevel] = None  # 如果为None，使用主级别
    
    # JSON日志配置
    enable_json_logging: bool = False
    json_log_file: Path = Path(".gendocs/logs/gendocs.json")
    
    # 结构化日志配置
    include_extra_fields: bool = True
    extra_fields: Dict[str, Any] = None
    
    # 过滤配置
    ignored_loggers: List[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.extra_fields is None:
            self.extra_fields = {}
        if self.ignored_loggers is None:
            self.ignored_loggers = ['urllib3', 'requests', 'aiohttp']


class JsonFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def __init__(self, extra_fields: Dict[str, Any] = None):
        super().__init__()
        self.extra_fields = extra_fields or {}
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process
        }
        
        # 添加额外字段
        log_data.update(self.extra_fields)
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        if hasattr(record, 'extra'):
            log_data.update(record.extra)
        
        return json.dumps(log_data, ensure_ascii=False)


class ColorFormatter(logging.Formatter):
    """彩色控制台格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
        'RESET': '\033[0m'       # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化带颜色的日志记录"""
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化消息
        formatted = super().format(record)
        
        # 仅在控制台输出时添加颜色
        if hasattr(sys.stderr, 'isatty') and sys.stderr.isatty():
            return f"{color}{formatted}{reset}"
        
        return formatted


class ContextFilter(logging.Filter):
    """上下文过滤器，添加额外的上下文信息"""
    
    def __init__(self, extra_fields: Dict[str, Any] = None):
        super().__init__()
        self.extra_fields = extra_fields or {}
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤并添加上下文信息"""
        # 添加额外字段
        for key, value in self.extra_fields.items():
            setattr(record, key, value)
        
        return True


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config: LoggerConfig):
        """初始化日志管理器
        
        Args:
            config: 日志配置
        """
        self.config = config
        self._loggers: Dict[str, logging.Logger] = {}
        self._setup_root_logger()
    
    def _setup_root_logger(self) -> None:
        """设置根日志记录器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.level.value))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 设置文件日志处理器
        if self.config.enable_file_logging:
            self._setup_file_handler(root_logger)
        
        # 设置控制台日志处理器
        if self.config.enable_console_logging:
            self._setup_console_handler(root_logger)
        
        # 设置JSON日志处理器
        if self.config.enable_json_logging:
            self._setup_json_handler(root_logger)
        
        # 设置忽略的日志记录器
        self._setup_ignored_loggers()
    
    def _setup_file_handler(self, logger: logging.Logger) -> None:
        """设置文件日志处理器"""
        # 确保日志目录存在
        self.config.log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=self.config.log_file_path,
            maxBytes=self.config.max_file_size,
            backupCount=self.config.backup_count,
            encoding='utf-8'
        )
        
        file_handler.setLevel(getattr(logging, self.config.level.value))
        
        # 设置格式化器
        formatter = logging.Formatter(
            fmt=self.config.format_string,
            datefmt=self.config.date_format
        )
        file_handler.setFormatter(formatter)
        
        # 添加上下文过滤器
        if self.config.include_extra_fields:
            context_filter = ContextFilter(self.config.extra_fields)
            file_handler.addFilter(context_filter)
        
        logger.addHandler(file_handler)
    
    def _setup_console_handler(self, logger: logging.Logger) -> None:
        """设置控制台日志处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        
        # 设置控制台日志级别
        console_level = self.config.console_level or self.config.level
        console_handler.setLevel(getattr(logging, console_level.value))
        
        # 设置彩色格式化器
        formatter = ColorFormatter(
            fmt=self.config.format_string,
            datefmt=self.config.date_format
        )
        console_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
    
    def _setup_json_handler(self, logger: logging.Logger) -> None:
        """设置JSON日志处理器"""
        # 确保日志目录存在
        self.config.json_log_file.parent.mkdir(parents=True, exist_ok=True)
        
        json_handler = logging.handlers.RotatingFileHandler(
            filename=self.config.json_log_file,
            maxBytes=self.config.max_file_size,
            backupCount=self.config.backup_count,
            encoding='utf-8'
        )
        
        json_handler.setLevel(getattr(logging, self.config.level.value))
        
        # 设置JSON格式化器
        formatter = JsonFormatter(self.config.extra_fields)
        json_handler.setFormatter(formatter)
        
        logger.addHandler(json_handler)
    
    def _setup_ignored_loggers(self) -> None:
        """设置忽略的日志记录器"""
        for logger_name in self.config.ignored_loggers:
            ignored_logger = logging.getLogger(logger_name)
            ignored_logger.setLevel(logging.WARNING)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            日志记录器实例
        """
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def add_context(self, **kwargs) -> None:
        """添加全局上下文信息
        
        Args:
            **kwargs: 上下文字段
        """
        self.config.extra_fields.update(kwargs)
        
        # 更新所有处理器的上下文
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            for filter_obj in handler.filters:
                if isinstance(filter_obj, ContextFilter):
                    filter_obj.extra_fields.update(kwargs)
    
    def set_level(self, level: LogLevel) -> None:
        """设置日志级别
        
        Args:
            level: 新的日志级别
        """
        self.config.level = level
        log_level = getattr(logging, level.value)
        
        # 更新根日志记录器级别
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 更新所有处理器级别
        for handler in root_logger.handlers:
            if not isinstance(handler, logging.StreamHandler) or handler.stream != sys.stdout:
                handler.setLevel(log_level)
    
    def create_operation_logger(self, operation_name: str, **context) -> logging.Logger:
        """创建操作专用的日志记录器
        
        Args:
            operation_name: 操作名称
            **context: 操作上下文
            
        Returns:
            操作日志记录器
        """
        logger_name = f"gendocs.operations.{operation_name}"
        logger = self.get_logger(logger_name)
        
        # 添加操作上下文
        operation_context = {
            'operation': operation_name,
            'operation_id': context.get('operation_id', f"{operation_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            **context
        }
        
        # 创建带上下文的适配器
        return logging.LoggerAdapter(logger, operation_context)


# 全局日志管理器实例
_global_logger_manager: Optional[LoggerManager] = None


def setup_logging(config: Optional[LoggerConfig] = None) -> LoggerManager:
    """设置日志系统
    
    Args:
        config: 日志配置
        
    Returns:
        日志管理器实例
    """
    global _global_logger_manager
    
    if config is None:
        config = LoggerConfig()
    
    _global_logger_manager = LoggerManager(config)
    return _global_logger_manager


def get_logger(name: str = "") -> logging.Logger:
    """获取日志记录器
    
    Args:
        name: 日志记录器名称，默认为空使用调用模块名
        
    Returns:
        日志记录器实例
    """
    global _global_logger_manager
    
    if _global_logger_manager is None:
        setup_logging()
    
    if not name:
        # 自动获取调用者模块名
        import inspect
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back
            if caller_frame:
                caller_module = caller_frame.f_globals.get('__name__', 'unknown')
                name = caller_module
        finally:
            del frame
    
    return _global_logger_manager.get_logger(name)


def get_operation_logger(operation_name: str, **context) -> logging.Logger:
    """获取操作专用日志记录器
    
    Args:
        operation_name: 操作名称
        **context: 操作上下文
        
    Returns:
        操作日志记录器
    """
    global _global_logger_manager
    
    if _global_logger_manager is None:
        setup_logging()
    
    return _global_logger_manager.create_operation_logger(operation_name, **context)


def log_performance(func_name: str, duration: float, **kwargs) -> None:
    """记录性能日志
    
    Args:
        func_name: 函数名称
        duration: 执行时间
        **kwargs: 额外信息
    """
    logger = get_logger("gendocs.performance")
    
    log_data = {
        'function': func_name,
        'duration_seconds': duration,
        'performance_log': True,
        **kwargs
    }
    
    logger.info(f"性能统计: {func_name} 耗时 {duration:.3f}秒", extra=log_data)


def log_error_with_context(error: Exception, 
                          operation: str = "",
                          **context) -> None:
    """记录带上下文的错误日志
    
    Args:
        error: 异常对象
        operation: 操作名称
        **context: 错误上下文
    """
    logger = get_logger("gendocs.errors")
    
    error_data = {
        'error_type': type(error).__name__,
        'operation': operation,
        'error_log': True,
        **context
    }
    
    logger.error(f"操作失败: {operation} - {str(error)}", exc_info=error, extra=error_data)