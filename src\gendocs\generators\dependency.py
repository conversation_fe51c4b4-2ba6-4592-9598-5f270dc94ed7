"""
依赖分析生成器模块
"""

import ast
import re
import sys
import json
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from functools import lru_cache

from .base import BaseGenerator

class DependencyGenerator(BaseGenerator):
    """依赖分析生成器"""
    
    def _generate(self) -> bool:
        """生成依赖分析文档
        
        Returns:
            生成是否成功
        """
        self.logger.info("正在生成依赖分析文档...")
        
        try:
            # 写入标题
            self._write("# 依赖分析\n\n")
            
            # 处理每个模块
            for module in self.config.modules:
                module_path = self.config.get_module_path(module)
                if not module_path.exists():
                    self.logger.warning(f"模块路径不存在: {module_path}")
                    continue
                    
                # 写入模块标题
                self._write(f"## {module}\n\n")
                
                # 分析内部依赖
                self._write("### 内部依赖\n\n")
                imports = self._analyze_imports(module_path)
                if imports:
                    self._write_imports(imports)
                else:
                    self._write("无内部依赖\n")
                self._write("\n")
                
                # 分析外部依赖
                self._write("### 外部依赖\n\n")
                deps = self._analyze_external_deps(module_path)
                if deps:
                    self._write_dependencies(deps)
                else:
                    self._write("无外部依赖\n")
                self._write("\n")
                
            # 写入文件
            output_file = self.config.docs_dir / "dependencies.md"
            return self.write_to_file(output_file)
            
        except Exception as e:
            self.logger.error(f"生成依赖分析文档失败: {str(e)}")
            return False
            
    def _analyze_imports(self, path: Path) -> Dict[str, Set[str]]:
        """分析内部导入
        
        Args:
            path: 模块路径
            
        Returns:
            {模块名: {导入项}}的字典
        """
        imports: Dict[str, Set[str]] = {}
        
        # 查找所有Python文件
        python_files = self.find_python_files(path)
        
        for py_file in python_files:
            content = self.read_file_content(py_file)
            if not content:
                continue
                
            try:
                # 使用exec模式解析，提高性能
                tree = ast.parse(content, mode='exec')
                
                # 使用ast.NodeVisitor提高性能
                class ImportVisitor(ast.NodeVisitor):
                    def __init__(self, generator):
                        self.imports = {}
                        self.generator = generator
                        
                    def visit_Import(self, node):
                        for name in node.names:
                            module = name.name.split(".")[0]
                            if not self.generator._is_external_module(module):
                                self.imports.setdefault(module, set()).add(name.name)
                                
                    def visit_ImportFrom(self, node):
                        if node.module:
                            module = node.module.split(".")[0]
                            if not self.generator._is_external_module(module):
                                self.imports.setdefault(module, set()).update(
                                    name.name for name in node.names
                                )
                                
                visitor = ImportVisitor(self)
                visitor.visit(tree)
                
                # 合并导入信息
                for module, names in visitor.imports.items():
                    imports.setdefault(module, set()).update(names)
                    
            except Exception as e:
                self.logger.warning(f"分析文件 {py_file} 失败: {str(e)}")
                
        return imports
        
    def _analyze_external_deps(self, path: Path) -> Dict[str, str]:
        """分析外部依赖
        
        Args:
            path: 模块路径
            
        Returns:
            {包名: 版本}的字典
        """
        deps: Dict[str, str] = {}
        
        # 检查requirements.txt
        req_file = self.config.project_root / "requirements.txt"
        if req_file.exists():
            content = self.read_file_content(req_file)
            if content:
                # 使用正则表达式一次性匹配所有依赖
                pattern = re.compile(r"^([^=<>]+).*$", re.MULTILINE)
                for match in pattern.finditer(content):
                    line = match.group(0).strip()
                    if line and not line.startswith("#"):
                        package = match.group(1).strip()
                        deps[package] = line
                            
        # 检查setup.py
        setup_file = self.config.project_root / "setup.py"
        if setup_file.exists():
            content = self.read_file_content(setup_file)
            if content:
                # 使用正则表达式一次性匹配所有依赖
                pattern = re.compile(r"install_requires\s*=\s*\[(.*?)\]", re.DOTALL)
                match = pattern.search(content)
                if match:
                    dep_pattern = re.compile(r"['\"]([^'\"]+)['\"]")
                    for dep_match in dep_pattern.finditer(match.group(1)):
                        dep = dep_match.group(1)
                        package = re.split(r"[>=<]", dep)[0].strip()
                        deps[package] = dep
                        
        return deps
        
    @lru_cache(maxsize=1024)
    def _is_external_module(self, module: str) -> bool:
        """检查是否是外部模块
        
        使用lru_cache缓存结果以提高性能
        
        Args:
            module: 模块名
            
        Returns:
            是否是外部模块
        """
        # 标准库模块
        if module in sys.stdlib_module_names:
            return True
            
        # 已安装的第三方模块
        try:
            __import__(module)
            return True
        except ImportError:
            return False
            
    def _write_imports(self, imports: Dict[str, Set[str]]) -> None:
        """写入导入信息
        
        Args:
            imports: 导入信息字典
        """
        # 使用表格格式提高可读性
        self._write("| 模块 | 导入项 |\n")
        self._write("|------|--------|\n")
        for module, names in sorted(imports.items()):
            self._write(f"| **{module}** | {', '.join(sorted(names))} |\n")
                
    def _write_dependencies(self, deps: Dict[str, str]) -> None:
        """写入依赖信息
        
        Args:
            deps: 依赖信息字典
        """
        # 使用表格格式提高可读性
        self._write("| 包名 | 版本要求 |\n")
        self._write("|------|----------|\n")
        for package, spec in sorted(deps.items()):
            self._write(f"| {package} | `{spec}` |\n") 