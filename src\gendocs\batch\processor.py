"""
批量处理器核心模块

实现多项目的批量文档生成功能。
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from enum import Enum

from ..config import ConfigManager
from ..generators import EnterpriseDocumentManager
from ..analyzers.registry import get_registry
from .strategies import ProcessingStrategy, SequentialStrategy, ConcurrentStrategy


class ProcessingStatus(Enum):
    """处理状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ProjectConfig:
    """单个项目配置"""
    path: Path
    output_dir: Optional[Path] = None
    generators: Optional[List[str]] = None
    config_file: Optional[Path] = None
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    priority: int = 0  # 优先级，数字越大优先级越高
    

@dataclass
class ProcessingResult:
    """处理结果"""
    project_path: Path
    status: ProcessingStatus
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    generated_docs: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    
    @property
    def success(self) -> bool:
        """是否成功"""
        return self.status == ProcessingStatus.COMPLETED
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'project_path': str(self.project_path),
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': self.duration_seconds,
            'generated_docs': self.generated_docs,
            'error_message': self.error_message,
            'warnings': self.warnings,
            'success': self.success
        }


@dataclass
class BatchConfig:
    """批量处理配置"""
    max_concurrent: int = 3
    fail_fast: bool = False  # 遇到错误时是否立即停止
    continue_on_error: bool = True  # 单个项目失败时是否继续处理其他项目
    timeout_seconds: int = 600  # 单个项目的超时时间
    retry_attempts: int = 1  # 重试次数
    retry_delay_seconds: int = 5  # 重试延迟
    
    # 过滤配置
    skip_existing: bool = False  # 跳过已存在文档的项目
    min_file_count: int = 1  # 最少文件数量过滤
    
    # 输出配置
    generate_report: bool = True  # 生成处理报告
    report_format: str = "json"  # html, json, csv
    
    # 通知配置
    progress_callback: Optional[Callable] = None
    completion_callback: Optional[Callable] = None


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config_manager: ConfigManager, batch_config: Optional[BatchConfig] = None):
        """初始化批量处理器
        
        Args:
            config_manager: 配置管理器
            batch_config: 批量处理配置
        """
        self.config_manager = config_manager
        self.batch_config = batch_config or BatchConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 处理策略
        if self.batch_config.max_concurrent <= 1:
            self.strategy = SequentialStrategy()
        else:
            self.strategy = ConcurrentStrategy(self.batch_config.max_concurrent)
    
    async def process_projects(self, projects: List[ProjectConfig]) -> List[ProcessingResult]:
        """批量处理项目
        
        Args:
            projects: 项目配置列表
            
        Returns:
            处理结果列表
        """
        if not projects:
            self.logger.warning("没有项目需要处理")
            return []
        
        self.logger.info(f"开始批量处理 {len(projects)} 个项目")
        
        # 过滤和排序项目
        filtered_projects = self._filter_projects(projects)
        sorted_projects = self._sort_projects(filtered_projects)
        
        if not sorted_projects:
            self.logger.warning("所有项目都被过滤，没有项目需要处理")
            return []
        
        self.logger.info(f"实际处理 {len(sorted_projects)} 个项目")
        
        # 执行批量处理
        results = await self.strategy.process_batch(
            projects=sorted_projects,
            process_func=self._process_single_project,
            config=self.batch_config
        )
        
        # 生成处理报告
        if self.batch_config.generate_report:
            await self._generate_report(results)
        
        # 调用完成回调
        if self.batch_config.completion_callback:
            try:
                await self.batch_config.completion_callback(results)
            except Exception as e:
                self.logger.error(f"执行完成回调失败: {e}")
        
        return results
    
    def _filter_projects(self, projects: List[ProjectConfig]) -> List[ProjectConfig]:
        """过滤项目列表"""
        filtered = []
        
        for project in projects:
            # 检查项目路径是否存在
            if not project.path.exists():
                self.logger.warning(f"项目路径不存在，跳过: {project.path}")
                continue
            
            if not project.path.is_dir():
                self.logger.warning(f"项目路径不是目录，跳过: {project.path}")
                continue
            
            # 检查文件数量
            try:
                file_count = len(list(project.path.rglob("*.py")))  # 仅统计Python文件
                if file_count < self.batch_config.min_file_count:
                    self.logger.info(f"项目文件太少({file_count})，跳过: {project.path}")
                    continue
            except Exception as e:
                self.logger.warning(f"无法统计项目文件数量，跳过: {project.path}, 错误: {e}")
                continue
            
            # 检查是否跳过已存在文档的项目
            if self.batch_config.skip_existing:
                output_dir = project.output_dir or (project.path / "docs")
                if output_dir.exists() and list(output_dir.glob("*.md")):
                    self.logger.info(f"项目已存在文档，跳过: {project.path}")
                    continue
            
            filtered.append(project)
        
        return filtered
    
    def _sort_projects(self, projects: List[ProjectConfig]) -> List[ProjectConfig]:
        """排序项目列表"""
        # 按优先级降序排序，优先级相同的按路径名称排序
        return sorted(projects, key=lambda p: (-p.priority, str(p.path)))
    
    async def _process_single_project(self, project: ProjectConfig) -> ProcessingResult:
        """处理单个项目
        
        Args:
            project: 项目配置
            
        Returns:
            处理结果
        """
        result = ProcessingResult(project_path=project.path, status=ProcessingStatus.PENDING)
        
        try:
            result.status = ProcessingStatus.RUNNING
            result.start_time = datetime.now()
            
            self.logger.info(f"开始处理项目: {project.path}")
            
            # 加载项目特定配置
            config_manager = self._create_project_config_manager(project)
            
            # 创建文档管理器
            doc_manager = EnterpriseDocumentManager(config_manager)
            
            # 设置输出目录
            output_dir = project.output_dir or (project.path / "docs")
            
            # 生成文档
            generation_results = await asyncio.wait_for(
                doc_manager.generate_all_documents(
                    project_path=project.path,
                    output_dir=output_dir,
                    selected_generators=project.generators
                ),
                timeout=self.batch_config.timeout_seconds
            )
            
            # 处理结果
            successful_docs = [name for name, success in generation_results.items() if success]
            failed_docs = [name for name, success in generation_results.items() if not success]
            
            result.generated_docs = successful_docs
            
            if failed_docs:
                result.warnings.append(f"以下生成器执行失败: {', '.join(failed_docs)}")
            
            if successful_docs:
                result.status = ProcessingStatus.COMPLETED
                self.logger.info(f"项目处理成功: {project.path}, 生成了 {len(successful_docs)} 个文档")
            else:
                result.status = ProcessingStatus.FAILED
                result.error_message = "所有文档生成器都执行失败"
                self.logger.error(f"项目处理失败: {project.path}, 所有生成器都失败")
            
        except asyncio.TimeoutError:
            result.status = ProcessingStatus.FAILED
            result.error_message = f"处理超时 ({self.batch_config.timeout_seconds}秒)"
            self.logger.error(f"项目处理超时: {project.path}")
            
        except Exception as e:
            result.status = ProcessingStatus.FAILED
            result.error_message = str(e)
            self.logger.error(f"项目处理异常: {project.path}, 错误: {e}")
        
        finally:
            result.end_time = datetime.now()
            if result.start_time:
                result.duration_seconds = (result.end_time - result.start_time).total_seconds()
            
            # 调用进度回调
            if self.batch_config.progress_callback:
                try:
                    await self.batch_config.progress_callback(result)
                except Exception as e:
                    self.logger.error(f"执行进度回调失败: {e}")
        
        return result
    
    def _create_project_config_manager(self, project: ProjectConfig) -> ConfigManager:
        """为项目创建配置管理器"""
        project_config_manager = ConfigManager()
        
        # 加载全局配置
        try:
            global_config = self.config_manager.get_config()
            project_config_manager._config = global_config.copy()
        except:
            project_config_manager.load_config()
        
        # 加载项目特定配置
        if project.config_file and project.config_file.exists():
            try:
                project_config_manager.load_config(project.config_file)
            except Exception as e:
                self.logger.warning(f"无法加载项目配置文件 {project.config_file}: {e}")
        
        # 应用自定义设置
        if project.custom_settings:
            config = project_config_manager.get_config()
            config.update(project.custom_settings)
        
        return project_config_manager
    
    async def _generate_report(self, results: List[ProcessingResult]) -> None:
        """生成处理报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if self.batch_config.report_format == "json":
                await self._generate_json_report(results, timestamp)
            elif self.batch_config.report_format == "html":
                await self._generate_html_report(results, timestamp)
            elif self.batch_config.report_format == "csv":
                await self._generate_csv_report(results, timestamp)
            
        except Exception as e:
            self.logger.error(f"生成处理报告失败: {e}")
    
    async def _generate_json_report(self, results: List[ProcessingResult], timestamp: str) -> None:
        """生成JSON格式报告"""
        import json
        
        report_data = {
            'timestamp': timestamp,
            'total_projects': len(results),
            'successful_projects': len([r for r in results if r.success]),
            'failed_projects': len([r for r in results if not r.success]),
            'total_duration_seconds': sum(r.duration_seconds or 0 for r in results),
            'results': [r.to_dict() for r in results]
        }
        
        report_path = Path(f"batch_report_{timestamp}.json")
        report_path.write_text(json.dumps(report_data, ensure_ascii=False, indent=2), encoding='utf-8')
        
        self.logger.info(f"JSON报告已生成: {report_path}")
    
    async def _generate_html_report(self, results: List[ProcessingResult], timestamp: str) -> None:
        """生成HTML格式报告"""
        # 简单的HTML报告模板
        html_template = """<!DOCTYPE html>
<html>
<head>
    <title>GenDocs 批量处理报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .result {{ margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .success {{ border-left: 5px solid #4CAF50; }}
        .failed {{ border-left: 5px solid #f44336; }}
        .warnings {{ color: #ff9800; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>GenDocs 批量处理报告</h1>
        <p>生成时间: {timestamp}</p>
    </div>
    
    <div class="summary">
        <h2>处理摘要</h2>
        <ul>
            <li>总项目数: {total_projects}</li>
            <li>成功项目: {successful_projects}</li>
            <li>失败项目: {failed_projects}</li>
            <li>总耗时: {total_duration:.2f} 秒</li>
        </ul>
    </div>
    
    <div class="results">
        <h2>详细结果</h2>
        {result_html}
    </div>
</body>
</html>"""
        
        successful_count = len([r for r in results if r.success])
        failed_count = len(results) - successful_count
        total_duration = sum(r.duration_seconds or 0 for r in results)
        
        result_items = []
        for result in results:
            status_class = "success" if result.success else "failed"
            warnings_html = ""
            if result.warnings:
                warnings_html = f"<div class='warnings'>警告: {'; '.join(result.warnings)}</div>"
            
            error_html = ""
            if result.error_message:
                error_html = f"<div style='color: red;'>错误: {result.error_message}</div>"
            
            docs_html = ""
            if result.generated_docs:
                docs_html = f"<div>生成文档: {', '.join(result.generated_docs)}</div>"
            
            result_items.append(f"""
            <div class="result {status_class}">
                <h3>{result.project_path}</h3>
                <p>状态: {result.status.value}</p>
                <p>耗时: {result.duration_seconds:.2f} 秒</p>
                {docs_html}
                {warnings_html}
                {error_html}
            </div>
            """)
        
        html_content = html_template.format(
            timestamp=timestamp,
            total_projects=len(results),
            successful_projects=successful_count,
            failed_projects=failed_count,
            total_duration=total_duration,
            result_html="".join(result_items)
        )
        
        report_path = Path(f"batch_report_{timestamp}.html")
        report_path.write_text(html_content, encoding='utf-8')
        
        self.logger.info(f"HTML报告已生成: {report_path}")
    
    async def _generate_csv_report(self, results: List[ProcessingResult], timestamp: str) -> None:
        """生成CSV格式报告"""
        import csv
        
        report_path = Path(f"batch_report_{timestamp}.csv")
        
        with open(report_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入标题行
            writer.writerow([
                'Project Path', 'Status', 'Duration (seconds)', 
                'Generated Docs', 'Error Message', 'Warnings'
            ])
            
            # 写入数据行
            for result in results:
                writer.writerow([
                    str(result.project_path),
                    result.status.value,
                    result.duration_seconds or 0,
                    '; '.join(result.generated_docs),
                    result.error_message or '',
                    '; '.join(result.warnings)
                ])
        
        self.logger.info(f"CSV报告已生成: {report_path}")
    
    @classmethod
    def discover_projects(cls, 
                         root_path: Path, 
                         patterns: Optional[List[str]] = None,
                         max_depth: int = 3) -> List[ProjectConfig]:
        """自动发现项目
        
        Args:
            root_path: 根路径
            patterns: 项目识别模式
            max_depth: 最大搜索深度
            
        Returns:
            发现的项目配置列表
        """
        if patterns is None:
            patterns = [
                "setup.py", "pyproject.toml", "requirements.txt",
                "package.json", "pom.xml", "Cargo.toml",
                "go.mod", "Gemfile", "composer.json"
            ]
        
        projects = []
        
        def _search_directory(path: Path, current_depth: int):
            if current_depth > max_depth:
                return
            
            # 检查当前目录是否为项目
            for pattern in patterns:
                if (path / pattern).exists():
                    projects.append(ProjectConfig(path=path))
                    return  # 找到项目标识符就不再搜索子目录
            
            # 搜索子目录
            try:
                for item in path.iterdir():
                    if item.is_dir() and not item.name.startswith('.'):
                        _search_directory(item, current_depth + 1)
            except PermissionError:
                pass
        
        _search_directory(root_path, 0)
        return projects