"""
类图生成器模块
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from .base import BaseGenerator

class DiagramGenerator(BaseGenerator):
    """类图生成器"""
    
    def _generate(self) -> bool:
        """生成类图文档
        
        Returns:
            生成是否成功
        """
        self.logger.info("正在生成类图文档...")
        
        try:
            # 写入标题
            self._write("# 类图\n\n")
            
            # 确保输出目录存在
            output_dir = self.config.docs_dir / "diagrams"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 并行处理每个模块
            with ThreadPoolExecutor() as executor:
                futures = []
                for module in self.config.modules:
                    module_path = self.config.get_module_path(module)
                    if not module_path.exists():
                        self.logger.warning(f"模块路径不存在: {module_path}")
                        continue
                        
                    future = executor.submit(self._generate_module_diagram, 
                                          module, 
                                          module_path,
                                          output_dir)
                    futures.append((module, future))
                    
                # 等待所有任务完成并写入结果
                for module, future in futures:
                    try:
                        diagram_path = future.result()
                        if diagram_path:
                            self._write(f"## {module}\n\n")
                            rel_path = os.path.relpath(diagram_path, self.config.docs_dir)
                            self._write(f"![{module} 类图]({rel_path})\n\n")
                    except Exception as e:
                        self.logger.error(f"生成模块 {module} 的类图失败: {str(e)}")
                        
            # 写入文件
            output_file = self.config.docs_dir / "diagrams.md"
            return self.write_to_file(output_file)
            
        except Exception as e:
            self.logger.error(f"生成类图文档失败: {str(e)}")
            return False
            
    def _generate_module_diagram(self, 
                               module: str, 
                               module_path: Path,
                               output_dir: Path) -> Optional[Path]:
        """为单个模块生成类图
        
        Args:
            module: 模块名
            module_path: 模块路径
            output_dir: 输出目录
            
        Returns:
            生成的类图文件路径，如果失败则返回None
        """
        try:
            # 查找所有Python文件
            python_files = self.find_python_files(module_path)
            if not python_files:
                self.logger.warning(f"模块 {module} 中未找到Python文件")
                return None
                
            # 构建pyreverse命令
            output_file = output_dir / f"{module}_classes.png"
            cmd = [
                sys.executable,
                "-m", "pylint.pyreverse.main",
                "-o", "png",
                "-d", str(output_dir),
                "-p", module,
                *[str(f) for f in python_files]
            ]
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                self.logger.error(
                    f"生成模块 {module} 的类图失败:\n"
                    f"命令: {' '.join(cmd)}\n"
                    f"错误: {result.stderr}"
                )
                return None
                
            # 检查输出文件
            if not output_file.exists():
                self.logger.error(f"类图文件未生成: {output_file}")
                return None
                
            return output_file
            
        except Exception as e:
            self.logger.error(f"生成模块 {module} 的类图时发生错误: {str(e)}")
            return None
            
    def ensure_tool(self, tool: str, package: str) -> bool:
        """确保工具可用
        
        Args:
            tool: 工具名称
            package: 包名
            
        Returns:
            工具是否可用
        """
        try:
            subprocess.run(
                [tool, "--version"],
                capture_output=True,
                check=False
            )
            return True
        except FileNotFoundError:
            self.logger.warning(
                f"工具 {tool} 不可用，请安装 {package} 包"
            )
            return False 