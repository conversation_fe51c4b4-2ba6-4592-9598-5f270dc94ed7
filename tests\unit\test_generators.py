"""
文档生成器单元测试
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from gendocs.generators.base import BaseGenerator
from gendocs.generators.enterprise.base import EnterpriseGenerator
from gendocs.generators.enterprise.overview import OverviewGenerator
from gendocs.generators import EnterpriseDocumentManager


class TestBaseGenerator:
    """基础生成器测试类"""
    
    def test_init(self, config_manager):
        """测试基础生成器初始化"""
        generator = BaseGenerator(config_manager)
        assert generator.config_manager == config_manager
        assert generator.logger is not None
    
    def test_init_without_config(self):
        """测试无配置初始化"""
        generator = BaseGenerator()
        assert generator.config_manager is None
    
    def test_generate_not_implemented(self):
        """测试generate方法未实现"""
        generator = BaseGenerator()
        
        with pytest.raises(NotImplementedError):
            asyncio.run(generator.generate(Path("/test"), Path("/output"), {}))
    
    def test_get_template_path(self, config_manager):
        """测试获取模板路径"""
        generator = BaseGenerator(config_manager)
        
        # 测试默认模板路径
        template_path = generator.get_template_path("test_template.md.j2")
        assert isinstance(template_path, Path)
        assert template_path.name == "test_template.md.j2"
    
    def test_render_template(self, config_manager, temp_dir):
        """测试渲染模板"""
        generator = BaseGenerator(config_manager)
        
        # 创建测试模板
        template_content = "Hello {{ name }}! Version: {{ version }}"
        template_file = temp_dir / "test.j2"
        template_file.write_text(template_content)
        
        # 渲染模板
        context = {"name": "World", "version": "1.0.0"}
        result = generator.render_template(template_file, context)
        
        assert result == "Hello World! Version: 1.0.0"
    
    def test_render_template_with_nonexistent_file(self, config_manager, temp_dir):
        """测试渲染不存在的模板文件"""
        generator = BaseGenerator(config_manager)
        
        nonexistent_file = temp_dir / "nonexistent.j2"
        
        with pytest.raises(FileNotFoundError):
            generator.render_template(nonexistent_file, {})
    
    @patch('gendocs.generators.base.BaseGenerator.generate_ai_content')
    async def test_generate_ai_content(self, mock_ai_content, config_manager, mock_ai_provider):
        """测试AI内容生成"""
        mock_ai_content.return_value = "AI generated content"
        
        generator = BaseGenerator(config_manager, mock_ai_provider)
        
        result = await generator.generate_ai_content("test_prompt", test_param="value")
        assert result == "AI generated content"
        mock_ai_content.assert_called_once()
    
    def test_get_timestamp(self, config_manager):
        """测试获取时间戳"""
        generator = BaseGenerator(config_manager)
        timestamp = generator._get_timestamp()
        
        assert isinstance(timestamp, str)
        assert len(timestamp) > 0


class TestEnterpriseGenerator:
    """企业级生成器测试类"""
    
    def test_init(self, config_manager):
        """测试企业级生成器初始化"""
        generator = EnterpriseGenerator(config_manager)
        assert generator.config_manager == config_manager
        assert hasattr(generator, 'backup_manager')
    
    def test_init_backup_manager(self, config_manager):
        """测试初始化备份管理器"""
        generator = EnterpriseGenerator(config_manager)
        
        # 验证备份管理器初始化
        if generator.backup_manager:
            assert hasattr(generator.backup_manager, 'create_backup')
    
    @pytest.mark.asyncio
    async def test_generate_with_backup(self, config_manager, mock_project_dir, temp_dir):
        """测试带备份的文档生成"""
        generator = EnterpriseGenerator(config_manager)
        
        # Mock generate方法
        with patch.object(generator, 'generate', return_value=True) as mock_generate:
            result = await generator.generate_with_backup(
                mock_project_dir,
                temp_dir / "output",
                {"test": "context"}
            )
            
            assert result is True
            mock_generate.assert_called_once()
    
    def test_get_project_analysis(self, config_manager, mock_project_dir):
        """测试获取项目分析"""
        generator = EnterpriseGenerator(config_manager)
        
        with patch('gendocs.analyzers.registry.get_registry') as mock_registry:
            mock_analyzer_registry = Mock()
            mock_result = Mock()
            mock_analyzer_registry.analyze_project.return_value = mock_result
            mock_registry.return_value = mock_analyzer_registry
            
            result = generator.get_project_analysis(mock_project_dir)
            assert result == mock_result
    
    def test_build_enterprise_context(self, config_manager, mock_project_dir, sample_analysis_result):
        """测试构建企业级上下文"""
        generator = EnterpriseGenerator(config_manager)
        
        context = generator.build_enterprise_context(mock_project_dir, sample_analysis_result)
        
        assert isinstance(context, dict)
        assert 'project_path' in context
        assert 'project_name' in context
        assert 'analysis_result' in context
        assert 'timestamp' in context
        assert context['project_name'] == mock_project_dir.name
    
    @pytest.mark.asyncio
    async def test_enhance_with_ai_insights(self, config_manager, mock_ai_provider):
        """测试AI增强功能"""
        generator = EnterpriseGenerator(config_manager, mock_ai_provider)
        
        base_content = "Base documentation content"
        context = {"project_name": "test", "project_type": "library"}
        
        with patch.object(generator, 'generate_ai_content', return_value="Enhanced content") as mock_ai:
            result = await generator.enhance_with_ai_insights(
                base_content, "overview_enhancement", context
            )
            
            assert "Enhanced content" in result
            mock_ai.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_enhance_with_ai_insights_no_provider(self, config_manager):
        """测试无AI提供商时的增强功能"""
        generator = EnterpriseGenerator(config_manager)  # 无AI提供商
        
        base_content = "Base documentation content"
        context = {"project_name": "test"}
        
        result = await generator.enhance_with_ai_insights(
            base_content, "overview_enhancement", context
        )
        
        # 应该返回原始内容
        assert result == base_content
    
    def test_format_tech_stack(self, config_manager):
        """测试格式化技术栈"""
        generator = EnterpriseGenerator(config_manager)
        
        # 创建模拟技术栈对象
        mock_tech_stack = Mock()
        mock_tech_stack.languages = ["Python", "JavaScript"]
        mock_tech_stack.frameworks = ["Django", "React"]
        mock_tech_stack.databases = ["PostgreSQL"]
        
        result = generator._format_tech_stack(mock_tech_stack)
        
        assert "Python" in result
        assert "Django" in result
        assert "PostgreSQL" in result
    
    def test_extract_main_features(self, config_manager):
        """测试提取主要功能"""
        generator = EnterpriseGenerator(config_manager)
        
        context = {
            "apis": [Mock(), Mock(), Mock()],  # 3个API
            "modules": [Mock(), Mock()],      # 2个模块
            "framework": "django"
        }
        
        features = generator._extract_main_features(context)
        
        assert isinstance(features, list)
        assert any("3个API接口" in feature for feature in features)
        assert any("2个功能模块" in feature for feature in features)
        assert any("Web应用开发框架" in feature for feature in features)
    
    def test_validate_output(self, config_manager, temp_dir):
        """测试输出验证"""
        generator = EnterpriseGenerator(config_manager)
        
        # 测试有效输出文件
        valid_file = temp_dir / "valid.md"
        valid_file.write_text("# Valid Documentation\n\nThis is a valid documentation file with sufficient content.")
        
        assert generator.validate_output(valid_file) is True
        
        # 测试空文件
        empty_file = temp_dir / "empty.md"
        empty_file.write_text("")
        
        assert generator.validate_output(empty_file) is False
        
        # 测试不存在的文件
        nonexistent_file = temp_dir / "nonexistent.md"
        
        assert generator.validate_output(nonexistent_file) is False


class TestOverviewGenerator:
    """概览生成器测试类"""
    
    def test_init(self, config_manager):
        """测试概览生成器初始化"""
        generator = OverviewGenerator(config_manager)
        assert generator.config_manager == config_manager
        assert hasattr(generator, 'template_name')
    
    @pytest.mark.asyncio
    async def test_generate(self, config_manager, mock_project_dir, temp_dir, sample_analysis_result):
        """测试概览文档生成"""
        generator = OverviewGenerator(config_manager)
        output_path = temp_dir / "README.md"
        
        context = {
            "analysis_result": sample_analysis_result,
            "project_name": "test-project"
        }
        
        with patch.object(generator, 'render_template', return_value="Generated README content"):
            result = await generator.generate(mock_project_dir, output_path, context)
            
            assert result is True
            assert output_path.exists()
    
    def test_get_template_name(self, config_manager):
        """测试获取模板名称"""
        generator = OverviewGenerator(config_manager)
        
        if hasattr(generator, 'get_template_name'):
            template_name = generator.get_template_name()
            assert isinstance(template_name, str)
            assert template_name.endswith('.j2')


class TestEnterpriseDocumentManager:
    """企业文档管理器测试类"""
    
    def test_init(self, config_manager):
        """测试文档管理器初始化"""
        manager = EnterpriseDocumentManager(config_manager)
        assert manager.config_manager == config_manager
        assert hasattr(manager, 'generators')
    
    def test_get_available_generators(self, config_manager):
        """测试获取可用生成器"""
        manager = EnterpriseDocumentManager(config_manager)
        generators = manager.get_available_generators()
        
        assert isinstance(generators, list)
        assert len(generators) > 0
        
        # 验证生成器信息结构
        for gen_info in generators:
            assert 'name' in gen_info
            assert 'description' in gen_info
            assert 'output_filename' in gen_info
    
    @pytest.mark.asyncio
    async def test_generate_all_documents(self, config_manager, mock_project_dir, temp_dir):
        """测试生成所有文档"""
        manager = EnterpriseDocumentManager(config_manager)
        
        # Mock各个生成器的generate方法
        with patch.multiple(
            'gendocs.generators.enterprise.overview.OverviewGenerator',
            generate=AsyncMock(return_value=True)
        ):
            results = await manager.generate_all_documents(
                mock_project_dir,
                temp_dir
            )
            
            assert isinstance(results, dict)
            # 至少应该有一个生成器的结果
            assert len(results) > 0
    
    @pytest.mark.asyncio
    async def test_generate_selected_documents(self, config_manager, mock_project_dir, temp_dir):
        """测试生成选定的文档"""
        manager = EnterpriseDocumentManager(config_manager)
        selected_generators = ['overview']
        
        with patch.multiple(
            'gendocs.generators.enterprise.overview.OverviewGenerator',
            generate=AsyncMock(return_value=True)
        ):
            results = await manager.generate_all_documents(
                mock_project_dir,
                temp_dir,
                selected_generators=selected_generators
            )
            
            assert isinstance(results, dict)
            # 应该只包含选定的生成器结果
            assert 'overview' in results or len(results) == len(selected_generators)
    
    def test_get_generator_by_name(self, config_manager):
        """测试根据名称获取生成器"""
        manager = EnterpriseDocumentManager(config_manager)
        
        # 测试存在的生成器
        overview_gen = manager._get_generator_by_name('overview')
        if overview_gen:
            assert hasattr(overview_gen, 'generate')
        
        # 测试不存在的生成器
        nonexistent_gen = manager._get_generator_by_name('nonexistent')
        assert nonexistent_gen is None
    
    @pytest.mark.asyncio
    async def test_generate_single_document(self, config_manager, mock_project_dir, temp_dir):
        """测试生成单个文档"""
        manager = EnterpriseDocumentManager(config_manager)
        
        with patch.object(manager, '_get_generator_by_name') as mock_get_gen:
            mock_generator = Mock()
            mock_generator.generate = AsyncMock(return_value=True)
            mock_get_gen.return_value = mock_generator
            
            result = await manager._generate_single_document(
                'overview',
                mock_project_dir,
                temp_dir,
                {}
            )
            
            assert result is True
            mock_generator.generate.assert_called_once()
    
    def test_setup_ai_provider(self, config_manager):
        """测试设置AI提供商"""
        manager = EnterpriseDocumentManager(config_manager)
        
        # 测试AI提供商设置
        if hasattr(manager, '_setup_ai_provider'):
            ai_provider = manager._setup_ai_provider()
            # 根据配置可能为None（测试时AI被禁用）
            assert ai_provider is None or hasattr(ai_provider, 'generate_content')
    
    def test_create_output_directory(self, config_manager, temp_dir):
        """测试创建输出目录"""
        manager = EnterpriseDocumentManager(config_manager)
        
        output_dir = temp_dir / "test_output"
        
        if hasattr(manager, '_create_output_directory'):
            manager._create_output_directory(output_dir)
            assert output_dir.exists()
            assert output_dir.is_dir()


class TestGeneratorIntegration:
    """生成器集成测试类"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_generation(self, config_manager, complex_project_dir, temp_dir):
        """测试端到端文档生成"""
        manager = EnterpriseDocumentManager(config_manager)
        output_dir = temp_dir / "generated_docs"
        
        # 执行完整的文档生成流程
        results = await manager.generate_all_documents(
            complex_project_dir,
            output_dir,
            selected_generators=['overview']  # 只测试一个生成器
        )
        
        # 验证结果
        assert isinstance(results, dict)
        assert output_dir.exists()
    
    @pytest.mark.asyncio 
    async def test_concurrent_generation(self, config_manager, mock_project_dir, temp_dir):
        """测试并发生成"""
        manager = EnterpriseDocumentManager(config_manager)
        
        # 创建多个并发任务
        tasks = []
        for i in range(3):
            output_dir = temp_dir / f"output_{i}"
            task = manager.generate_all_documents(
                mock_project_dir,
                output_dir,
                selected_generators=['overview']
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有任务都成功完成
        for results in results_list:
            if isinstance(results, Exception):
                pytest.fail(f"并发生成失败: {results}")
            assert isinstance(results, dict)
    
    @pytest.mark.asyncio
    async def test_error_handling_in_generation(self, config_manager, mock_project_dir, temp_dir):
        """测试生成过程中的错误处理"""
        manager = EnterpriseDocumentManager(config_manager)
        
        # Mock一个会抛出异常的生成器
        with patch.object(manager, '_get_generator_by_name') as mock_get_gen:
            mock_generator = Mock()
            mock_generator.generate = AsyncMock(side_effect=Exception("Test error"))
            mock_get_gen.return_value = mock_generator
            
            results = await manager.generate_all_documents(
                mock_project_dir,
                temp_dir,
                selected_generators=['overview']
            )
            
            # 应该优雅地处理错误
            assert isinstance(results, dict)
            # 失败的生成器应该返回False
            assert any(not success for success in results.values())