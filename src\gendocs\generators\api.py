"""
API文档生成器模块
"""

import ast
import inspect
import importlib
import sys
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

from .base import BaseGenerator

class ApiGenerator(BaseGenerator):
    """API文档生成器"""
    
    def _generate(self) -> bool:
        """生成API文档
        
        Returns:
            生成是否成功
        """
        self.logger.info("正在生成API文档...")
        
        try:
            # 写入标题
            self._write("# API文档\n\n")
            
            # 并行处理每个模块
            with ThreadPoolExecutor() as executor:
                futures = []
                for module in self.config.modules:
                    module_path = self.config.get_module_path(module)
                    if not module_path.exists():
                        self.logger.warning(f"模块路径不存在: {module_path}")
                        continue
                        
                    future = executor.submit(self._generate_module_docs, 
                                          module, 
                                          module_path)
                    futures.append((module, future))
                    
                # 等待所有任务完成并写入结果
                for module, future in futures:
                    try:
                        module_doc = future.result()
                        if module_doc:
                            self._write(f"## {module}\n\n")
                            self._write(module_doc)
                    except Exception as e:
                        self.logger.error(f"生成模块 {module} 的API文档失败: {str(e)}")
                        
            # 写入文件
            output_file = self.config.docs_dir / "api.md"
            return self.write_to_file(output_file)
            
        except Exception as e:
            self.logger.error(f"生成API文档失败: {str(e)}")
            return False
            
    def _generate_module_docs(self, 
                            module: str, 
                            module_path: Path) -> Optional[str]:
        """为单个模块生成API文档
        
        Args:
            module: 模块名
            module_path: 模块路径
            
        Returns:
            生成的文档内容，如果失败则返回None
        """
        try:
            # 查找所有Python文件
            python_files = self.find_python_files(module_path)
            if not python_files:
                self.logger.warning(f"模块 {module} 中未找到Python文件")
                return None
                
            # 收集所有文档
            docs = []
            for py_file in python_files:
                file_doc = self._generate_file_docs(py_file)
                if file_doc:
                    rel_path = py_file.relative_to(self.config.project_root)
                    docs.append(f"### {rel_path}\n\n{file_doc}\n")
                    
            return "\n".join(docs) if docs else None
            
        except Exception as e:
            self.logger.error(f"生成模块 {module} 的API文档时发生错误: {str(e)}")
            return None
            
    def _generate_file_docs(self, file_path: Path) -> Optional[str]:
        """为单个文件生成API文档
        
        Args:
            file_path: 文件路径
            
        Returns:
            生成的文档内容，如果失败则返回None
        """
        try:
            content = self.read_file_content(file_path)
            if not content:
                return None
                
            # 解析AST
            tree = ast.parse(content, mode='exec')
            
            # 使用Visitor模式收集文档
            class DocVisitor(ast.NodeVisitor):
                def __init__(self):
                    self.docs = []
                    self.current_class = None
                    
                def visit_Module(self, node):
                    if ast.get_docstring(node):
                        self.docs.append(f"**模块文档:**\n\n{ast.get_docstring(node)}\n")
                    self.generic_visit(node)
                    
                def visit_ClassDef(self, node):
                    old_class = self.current_class
                    self.current_class = node.name
                    
                    # 类文档
                    doc = ast.get_docstring(node)
                    if doc:
                        self.docs.append(f"#### 类 `{node.name}`\n\n{doc}\n")
                        
                    # 基类
                    if node.bases:
                        bases = [self._format_expr(base) for base in node.bases]
                        self.docs.append(f"继承自: {', '.join(bases)}\n\n")
                        
                    self.generic_visit(node)
                    self.current_class = old_class
                    
                def visit_FunctionDef(self, node):
                    # 函数文档
                    doc = ast.get_docstring(node)
                    if doc:
                        if self.current_class:
                            self.docs.append(f"##### 方法 `{node.name}`\n\n")
                        else:
                            self.docs.append(f"#### 函数 `{node.name}`\n\n")
                            
                        # 参数信息
                        args = self._format_arguments(node.args)
                        if args:
                            self.docs.append(f"参数:\n{args}\n")
                            
                        # 返回类型
                        if node.returns:
                            ret_type = self._format_expr(node.returns)
                            self.docs.append(f"返回类型: `{ret_type}`\n\n")
                            
                        self.docs.append(f"{doc}\n\n")
                        
                def _format_arguments(self, args: ast.arguments) -> str:
                    """格式化函数参数"""
                    parts = []
                    
                    # 位置参数
                    for arg in args.args:
                        anno = self._format_expr(arg.annotation) if arg.annotation else "Any"
                        parts.append(f"- `{arg.arg}`: `{anno}`")
                        
                    # 可变位置参数
                    if args.vararg:
                        anno = self._format_expr(args.vararg.annotation) if args.vararg.annotation else "Any"
                        parts.append(f"- `*{args.vararg.arg}`: `{anno}`")
                        
                    # 关键字参数
                    for arg in args.kwonlyargs:
                        anno = self._format_expr(arg.annotation) if arg.annotation else "Any"
                        parts.append(f"- `{arg.arg}`: `{anno}`")
                        
                    # 可变关键字参数
                    if args.kwarg:
                        anno = self._format_expr(args.kwarg.annotation) if args.kwarg.annotation else "Any"
                        parts.append(f"- `**{args.kwarg.arg}`: `{anno}`")
                        
                    return "\n".join(parts)
                    
                def _format_expr(self, node: ast.AST) -> str:
                    """格式化表达式"""
                    if isinstance(node, ast.Name):
                        return node.id
                    elif isinstance(node, ast.Attribute):
                        return f"{self._format_expr(node.value)}.{node.attr}"
                    elif isinstance(node, ast.Subscript):
                        return f"{self._format_expr(node.value)}[{self._format_expr(node.slice)}]"
                    elif isinstance(node, ast.Constant):
                        return str(node.value)
                    else:
                        return ast.unparse(node)
                        
            visitor = DocVisitor()
            visitor.visit(tree)
            return "\n".join(visitor.docs) if visitor.docs else None
            
        except Exception as e:
            self.logger.error(f"生成文件 {file_path} 的API文档时发生错误: {str(e)}")
            return None
            
    @lru_cache(maxsize=1024)
    def _get_type_hints(self, obj: Any) -> Dict[str, Any]:
        """获取类型提示
        
        使用缓存避免重复获取
        
        Args:
            obj: 对象
            
        Returns:
            类型提示字典
        """
        try:
            return get_type_hints(obj)
        except Exception:
            return {} 