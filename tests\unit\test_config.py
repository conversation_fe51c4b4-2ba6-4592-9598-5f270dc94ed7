"""
配置管理器单元测试
"""

import pytest
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from gendocs.config import ConfigManager
from gendocs.utils import ConfigurationError


class TestConfigManager:
    """配置管理器测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        manager = ConfigManager()
        assert manager._config is not None
        assert isinstance(manager._config, dict)
    
    def test_init_with_config_file(self, sample_config_file):
        """测试使用配置文件初始化"""
        manager = ConfigManager()
        manager.load_config(sample_config_file)
        
        config = manager.get_config()
        assert config['ai']['provider'] == 'openai'
        assert config['generation']['output_dir'] == 'docs'
    
    def test_load_nonexistent_config(self, temp_dir):
        """测试加载不存在的配置文件"""
        manager = ConfigManager()
        nonexistent_file = temp_dir / "nonexistent.yaml"
        
        # 应该使用默认配置而不是抛出异常
        manager.load_config(nonexistent_file)
        config = manager.get_config()
        assert config is not None
    
    def test_get_config(self, config_manager):
        """测试获取配置"""
        config = config_manager.get_config()
        assert isinstance(config, dict)
        assert 'ai' in config
        assert 'generation' in config
    
    def test_get_config_value(self, config_manager):
        """测试获取配置值"""
        # 测试存在的键
        ai_config = config_manager.get_config_value('ai')
        assert isinstance(ai_config, dict)
        assert ai_config['provider'] == 'mock'
        
        # 测试嵌套键
        provider = config_manager.get_config_value('ai.provider')
        assert provider == 'mock'
        
        # 测试不存在的键
        nonexistent = config_manager.get_config_value('nonexistent', default='default_value')
        assert nonexistent == 'default_value'
    
    def test_set_config_value(self, config_manager):
        """测试设置配置值"""
        # 设置简单值
        config_manager.set_config_value('test_key', 'test_value')
        assert config_manager.get_config_value('test_key') == 'test_value'
        
        # 设置嵌套值
        config_manager.set_config_value('nested.key', 'nested_value')
        assert config_manager.get_config_value('nested.key') == 'nested_value'
    
    def test_update_config(self, config_manager):
        """测试更新配置"""
        update_data = {
            'ai': {
                'model': 'updated-model',
                'temperature': 0.5
            },
            'new_section': {
                'new_key': 'new_value'
            }
        }
        
        config_manager.update_config(update_data)
        
        # 验证更新
        assert config_manager.get_config_value('ai.model') == 'updated-model'
        assert config_manager.get_config_value('ai.temperature') == 0.5
        assert config_manager.get_config_value('new_section.new_key') == 'new_value'
        
        # 验证未更新的值保持不变
        assert config_manager.get_config_value('ai.provider') == 'mock'
    
    def test_save_config(self, config_manager, temp_dir):
        """测试保存配置"""
        config_file = temp_dir / "saved_config.yaml"
        
        # 修改配置
        config_manager.set_config_value('test_key', 'test_value')
        
        # 保存配置
        config_manager.save_config(config_file)
        
        # 验证文件存在
        assert config_file.exists()
        
        # 验证文件内容
        with open(config_file, 'r', encoding='utf-8') as f:
            saved_data = yaml.safe_load(f)
        
        assert saved_data['test_key'] == 'test_value'
    
    def test_validate_config(self, config_manager):
        """测试配置验证"""
        # 正常配置应该验证通过
        assert config_manager.validate_config() is True
        
        # 删除必需的配置项
        config_manager._config.pop('ai', None)
        # 注意：根据实际验证逻辑调整此测试
    
    @patch.dict('os.environ', {'TEST_ENV_VAR': 'test_value'})
    def test_environment_variable_substitution(self, temp_dir):
        """测试环境变量替换"""
        config_content = """
ai:
  api_key: "${TEST_ENV_VAR}"
  model: "test-model"
"""
        config_file = temp_dir / "env_config.yaml"
        config_file.write_text(config_content)
        
        manager = ConfigManager()
        manager.load_config(config_file)
        
        # 验证环境变量被正确替换
        api_key = manager.get_config_value('ai.api_key')
        assert api_key == 'test_value'
    
    def test_config_file_not_found_error_handling(self):
        """测试配置文件不存在的错误处理"""
        manager = ConfigManager()
        
        # 测试加载不存在的文件
        result = manager.load_config(Path("/nonexistent/path/config.yaml"))
        # 应该优雅地处理，返回False或使用默认配置
        assert result is False or manager._config is not None
    
    def test_invalid_yaml_error_handling(self, temp_dir):
        """测试无效YAML文件的错误处理"""
        invalid_yaml = temp_dir / "invalid.yaml"
        invalid_yaml.write_text("invalid: yaml: content: [unclosed")
        
        manager = ConfigManager()
        
        # 应该优雅地处理YAML解析错误
        with pytest.raises((yaml.YAMLError, ConfigurationError)) or not pytest.raises(Exception):
            manager.load_config(invalid_yaml)
    
    def test_merge_configs(self, config_manager):
        """测试配置合并"""
        additional_config = {
            'ai': {
                'model': 'merged-model',  # 覆盖现有值
                'new_param': 'new_value'  # 添加新值
            },
            'new_section': {
                'key': 'value'
            }
        }
        
        original_provider = config_manager.get_config_value('ai.provider')
        
        # 合并配置
        config_manager.update_config(additional_config)
        
        # 验证合并结果
        assert config_manager.get_config_value('ai.model') == 'merged-model'
        assert config_manager.get_config_value('ai.new_param') == 'new_value'
        assert config_manager.get_config_value('ai.provider') == original_provider  # 未覆盖的值保持不变
        assert config_manager.get_config_value('new_section.key') == 'value'
    
    def test_config_to_dict(self, config_manager):
        """测试配置转换为字典"""
        config_dict = config_manager.to_dict()
        
        assert isinstance(config_dict, dict)
        assert 'ai' in config_dict
        assert 'generation' in config_dict
        
        # 验证是深拷贝而不是引用
        config_dict['ai']['provider'] = 'modified'
        assert config_manager.get_config_value('ai.provider') != 'modified'
    
    def test_reset_config(self, config_manager):
        """测试重置配置"""
        # 修改配置
        config_manager.set_config_value('test_key', 'test_value')
        assert config_manager.get_config_value('test_key') == 'test_value'
        
        # 重置配置
        config_manager.reset_to_defaults()
        
        # 验证重置后的状态
        assert config_manager.get_config_value('test_key') is None
        # 验证默认值存在
        assert config_manager.get_config_value('ai') is not None
    
    def test_config_backup_and_restore(self, config_manager, temp_dir):
        """测试配置备份和恢复"""
        # 修改配置
        original_value = config_manager.get_config_value('ai.provider')
        config_manager.set_config_value('ai.provider', 'modified_provider')
        
        # 备份配置
        backup_file = temp_dir / "config_backup.yaml"
        config_manager.save_config(backup_file)
        
        # 再次修改配置
        config_manager.set_config_value('ai.provider', 'another_modification')
        
        # 从备份恢复
        config_manager.load_config(backup_file)
        
        # 验证恢复结果
        assert config_manager.get_config_value('ai.provider') == 'modified_provider'


class TestConfigValidation:
    """配置验证测试类"""
    
    def test_valid_config(self, test_config):
        """测试有效配置验证"""
        from gendocs.utils.validators import validate_config
        
        result = validate_config(test_config)
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_missing_required_fields(self):
        """测试缺少必需字段的配置"""
        from gendocs.utils.validators import validate_config
        
        invalid_config = {
            'generation': {
                'output_dir': 'docs'
            }
            # 缺少 'ai' 配置
        }
        
        result = validate_config(invalid_config)
        assert result.is_valid is False
        assert any('ai' in error for error in result.errors)
    
    def test_invalid_ai_provider(self):
        """测试无效的AI提供商"""
        from gendocs.utils.validators import validate_config
        
        invalid_config = {
            'ai': {
                'provider': 'invalid_provider',
                'model': 'test-model'
            },
            'generation': {
                'output_dir': 'docs'
            }
        }
        
        result = validate_config(invalid_config)
        # 应该有警告但不一定是错误
        assert len(result.warnings) > 0 or len(result.errors) > 0
    
    def test_invalid_temperature_range(self):
        """测试无效的温度范围"""
        from gendocs.utils.validators import validate_config
        
        invalid_config = {
            'ai': {
                'provider': 'openai',
                'model': 'test-model',
                'temperature': 3.0  # 超出有效范围
            },
            'generation': {
                'output_dir': 'docs'
            }
        }
        
        result = validate_config(invalid_config)
        assert result.is_valid is False
        assert any('temperature' in error for error in result.errors)