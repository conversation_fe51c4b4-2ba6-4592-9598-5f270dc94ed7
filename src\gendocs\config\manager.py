"""
配置管理器

负责配置文件的查找、加载、合并和验证。
"""

import logging
import yaml
from pathlib import Path
from typing import Optional, Dict, Any, List

from .models import GenDocsConfig

logger = logging.getLogger(__name__)


class ConfigLoadError(Exception):
    """配置加载错误"""
    pass


class ConfigManager:
    """统一配置管理器
    
    按优先级查找和加载配置文件：
    1. 当前项目目录 (gendocs.yaml, .gendocs.yaml)
    2. 用户主目录配置 (~/.config/gendocs/config.yaml)
    3. 内置默认配置
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        """初始化配置管理器
        
        Args:
            config_path: 指定配置文件路径，如果为None则自动查找
        """
        self.config_path = config_path
        self._config: Optional[GenDocsConfig] = None
        self._load_config()
    
    @property
    def config(self) -> GenDocsConfig:
        """获取配置对象"""
        if self._config is None:
            raise ConfigLoadError("配置未正确加载")
        return self._config
    
    def _load_config(self) -> None:
        """加载配置"""
        try:
            # 1. 加载默认配置
            default_config = self._load_default_config()
            
            # 2. 查找用户配置文件
            config_files = self._find_config_files()
            
            # 3. 合并配置
            merged_config = default_config
            for config_file in config_files:
                user_config = self._load_config_file(config_file)
                merged_config = self._merge_configs(merged_config, user_config)
                logger.info(f"已加载配置文件: {config_file}")
            
            # 4. 创建配置对象
            self._config = GenDocsConfig.from_dict(merged_config)
            
            logger.info("配置加载完成")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise ConfigLoadError(f"配置加载失败: {e}") from e
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        defaults_path = Path(__file__).parent / "defaults.yaml"
        if not defaults_path.exists():
            raise ConfigLoadError(f"默认配置文件不存在: {defaults_path}")
        
        return self._load_config_file(defaults_path)
    
    def _find_config_files(self) -> List[Path]:
        """查找配置文件"""
        if self.config_path:
            if self.config_path.exists():
                return [self.config_path]
            else:
                raise ConfigLoadError(f"指定的配置文件不存在: {self.config_path}")
        
        # 按优先级搜索配置文件
        search_paths = [
            # 当前项目目录
            Path.cwd() / "gendocs.yaml",
            Path.cwd() / ".gendocs.yaml",
            
            # 用户主目录配置
            Path.home() / ".config" / "gendocs" / "config.yaml",
        ]
        
        found_files = []
        for path in search_paths:
            if path.exists():
                found_files.append(path)
        
        return found_files
    
    def _load_config_file(self, config_path: Path) -> Dict[str, Any]:
        """加载单个配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = yaml.safe_load(f)
                return content or {}
        except yaml.YAMLError as e:
            raise ConfigLoadError(f"YAML解析错误 {config_path}: {e}")
        except Exception as e:
            raise ConfigLoadError(f"读取配置文件失败 {config_path}: {e}")
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并配置字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                result[key] = self._merge_configs(result[key], value)
            else:
                # 直接覆盖
                result[key] = value
        
        return result
    
    def reload_config(self) -> None:
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._config = None
        self._load_config()
    
    def save_config(self, output_path: Path) -> None:
        """保存当前配置到文件"""
        try:
            config_dict = self.config.to_dict()
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    config_dict,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    sort_keys=False,
                    indent=2
                )
            
            logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise ConfigLoadError(f"保存配置失败: {e}") from e
    
    def validate_config(self) -> List[str]:
        """验证配置有效性
        
        Returns:
            验证警告信息列表，空列表表示无问题
        """
        warnings = []
        
        try:
            config = self.config
            
            # 检查AI配置
            if not config.ai.api_key:
                warnings.append("AI API密钥未设置，AI增强功能将不可用")
            
            # 检查模板路径
            if config.templates.custom_path:
                custom_path = Path(config.templates.custom_path)
                if not custom_path.exists():
                    warnings.append(f"自定义模板路径不存在: {custom_path}")
            
            # 检查启用的语言
            supported_languages = ["python", "javascript", "typescript", "java"]
            for lang in config.languages.enabled:
                if lang not in supported_languages:
                    warnings.append(f"语言 '{lang}' 可能不受支持")
            
        except Exception as e:
            warnings.append(f"配置验证时发生错误: {e}")
        
        return warnings
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息摘要"""
        config = self.config
        
        return {
            "ai_provider": config.ai.provider,
            "ai_model": config.ai.model,
            "enabled_languages": config.languages.enabled,
            "output_dir": config.output.base_dir,
            "output_formats": [fmt.value for fmt in config.output.formats],
            "ai_enhanced_docs": config.generation.ai_enhanced_docs,
            "concurrent_jobs": config.generation.concurrent_jobs
        }