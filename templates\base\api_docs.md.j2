# {{ project_name }} API 文档

## 概述

{{ project_name }} 提供 RESTful API 接口，支持标准的 HTTP 方法。

### API 基础信息

- **Base URL**: `{{ api_overview.base_url if api_overview else "http://localhost:8000/api" }}`
- **API 版本**: `{{ api_overview.version if api_overview else "v1" }}`
- **协议**: {{ api_overview.protocol if api_overview else "HTTP/HTTPS" }}
- **响应格式**: {{ api_overview.format if api_overview else "JSON" }}
- **字符编码**: UTF-8
- **接口总数**: {{ api_overview.total_endpoints if api_overview else (apis|length if apis else 0) }}

{% if authentication %}
## 认证方式

### {{ authentication.type if authentication.type else "Bearer Token" }}

{{ authentication.description if authentication.description else "API使用Token认证机制" }}

#### 认证头部格式
```http
{{ authentication.header if authentication.header else "Authorization: Bearer <your-token>" }}
```

#### 示例
```http
{{ authentication.example if authentication.example else "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." }}
```

#### 获取认证令牌
{{ authentication.token_acquisition if authentication.token_acquisition else "请联系系统管理员获取API Token，或通过登录接口获取" }}

{% endif %}

{% if api_groups and api_groups|length > 0 %}
## API 分组

{% for group in api_groups %}
### {{ group.name }}

{{ group.description if group.description else "API接口组" }}

{% for api in group.apis %}
#### {{ api.method }} {{ api.path }}

{{ api.description if api.description else "API接口描述" }}

{% if api.parameters and api.parameters|length > 0 %}
**请求参数**

| 参数名 | 类型 | 必须 | 描述 |
|--------|------|------|------|
{% for param in api.parameters %}
| {{ param.name }} | {{ param.type if param.type else "string" }} | {{ "是" if param.required else "否" }} | {{ param.description if param.description else "参数描述" }} |
{% endfor %}
{% endif %}

**请求示例**

```bash
curl -X {{ api.method }} \
  "{{ api_overview.base_url if api_overview else "http://localhost:8000" }}{{ api.path }}" \
  -H "Content-Type: application/json"{% if authentication %} \
  -H "{{ authentication.header if authentication else "Authorization: Bearer <token>" }}"{% endif %}{% if api.method in ['POST', 'PUT', 'PATCH'] %} \
  -d '{{ api.example_request if api.example_request else '{"key": "value"}' }}'{% endif %}
```

{% if api.responses and api.responses|length > 0 %}
**响应示例**

{% for response in api.responses %}
*{{ response.status_code }} {{ response.description if response.description else "Success" }}*

```json
{{ response.example if response.example else '{"status": "success", "data": {}}' }}
```

{% endfor %}
{% else %}
**200 Success**

```json
{
  "status": "success",
  "data": {},
  "message": "操作成功"
}
```
{% endif %}

---

{% endfor %}
{% endfor %}

{% elif apis and apis|length > 0 %}
## API 接口列表

{% for api in apis %}
### {{ api.method }} {{ api.path }}

{{ api.description if api.description else "API接口描述" }}

{% if api.parameters and api.parameters|length > 0 %}
#### 请求参数

| 参数名 | 类型 | 必须 | 描述 |
|--------|------|------|------|
{% for param in api.parameters %}
| {{ param.name }} | {{ param.type if param.type else "string" }} | {{ "是" if param.required else "否" }} | {{ param.description if param.description else "参数描述" }} |
{% endfor %}
{% endif %}

#### 请求示例

```bash
curl -X {{ api.method }} \
  "{{ api_overview.base_url if api_overview else "http://localhost:8000" }}{{ api.path }}" \
  -H "Content-Type: application/json"{% if authentication %} \
  -H "{{ authentication.header if authentication else "Authorization: Bearer <token>" }}"{% endif %}{% if api.method in ['POST', 'PUT', 'PATCH'] %} \
  -d '{{ api.example_request if api.example_request else '{"key": "value"}' }}'{% endif %}
```

{% if api.responses and api.responses|length > 0 %}
#### 响应示例

{% for response in api.responses %}
**{{ response.status_code }} {{ response.description if response.description else "Success" }}**

```json
{{ response.example if response.example else '{"status": "success", "data": {}}' }}
```

{% endfor %}
{% else %}
#### 响应示例

**200 Success**

```json
{
  "status": "success",
  "data": {},
  "message": "操作成功"
}
```
{% endif %}

---

{% endfor %}
{% else %}
## API 接口

目前没有检测到API接口。如果项目包含API，请检查：

1. 确保API路由已正确配置
2. 检查框架特定的路由文件（如Django的urls.py、Flask的路由装饰器等）
3. 验证API注解或装饰器是否正确使用

{% endif %}

{% if error_codes and error_codes|length > 0 %}
## 错误码说明

| 错误码 | 消息 | 描述 | 解决方案 |
|--------|------|------|----------|
{% for error in error_codes %}
| {{ error.code }} | {{ error.message }} | {{ error.description if error.description else "错误描述" }} | {{ error.solution if error.solution else "请检查请求参数" }} |
{% endfor %}
{% else %}
## 标准错误码

| 错误码 | 消息 | 描述 | 解决方案 |
|--------|------|------|----------|
| 200 | OK | 请求成功 | - |
| 400 | Bad Request | 请求参数错误 | 检查请求参数格式和类型 |
| 401 | Unauthorized | 未授权访问 | 检查认证Token是否有效 |
| 403 | Forbidden | 权限不足 | 确认账户权限 |
| 404 | Not Found | 资源不存在 | 检查请求URL是否正确 |
| 429 | Too Many Requests | 请求过于频繁 | 降低请求频率或升级配额 |
| 500 | Internal Server Error | 服务器内部错误 | 联系技术支持 |
{% endif %}

{% if rate_limiting %}
## 限流策略

### 限流配置

- **默认限制**: {{ rate_limiting.default_limit if rate_limiting.default_limit else "每小时100次请求" }}
- **认证用户**: 根据用户级别有不同的限制
- **限流算法**: 滑动窗口算法

### 限流响应头

API响应会包含以下限流信息头：

```http
{% for header in rate_limiting.headers if rate_limiting.headers else ["X-RateLimit-Limit: 100", "X-RateLimit-Remaining: 99", "X-RateLimit-Reset: 1640995200"] %}
{{ header }}
{% endfor %}
```

### 超出限制处理

当请求超出限制时，API将返回429状态码：

```json
{
  "error": "Rate limit exceeded",
  "message": "请求频率超出限制，请稍后重试",
  "retry_after": 3600
}
```

{% else %}
## 限流说明

为保证服务质量，API 实施以下限流策略：

- **默认限制**: 每小时 1000 次请求
- **认证用户**: 每小时 5000 次请求  
- **超出限制**: 返回 429 状态码

限流信息通过以下响应头返回：
- `X-RateLimit-Limit`: 限制数量
- `X-RateLimit-Remaining`: 剩余次数
- `X-RateLimit-Reset`: 重置时间戳
{% endif %}

## 开发工具与SDK

### 推荐工具

{% if recommended_tools and recommended_tools|length > 0 %}
{% for tool in recommended_tools %}
#### {{ tool.name }}

{{ tool.description if tool.description else "开发工具" }}

- **安装**: `{{ tool.installation if tool.installation else "请查看官方文档" }}`
- **使用**: {{ tool.usage if tool.usage else "请参考工具文档" }}

{% endfor %}
{% else %}
#### API测试工具

- **Postman**: 功能完整的API测试和调试工具
- **Insomnia**: 现代化的API客户端
- **HTTPie**: 命令行HTTP客户端，语法简洁
- **curl**: 经典的命令行工具

#### 开发集成

- **Swagger/OpenAPI**: API文档和测试界面
- **Postman Collections**: 预配置的API请求集合
- **SDK生成器**: 基于OpenAPI规范自动生成SDK
{% endif %}

### 代码示例

{% if examples and examples|length > 0 %}
{% for example in examples %}
#### {{ example.title }}

```{{ example.language if example.language else "bash" }}
{{ example.curl if example.curl else example.code }}
```

**响应**:
```json
{{ example.response if example.response else '{"status": "success"}' }}
```

{% endfor %}
{% else %}
#### Python 示例

```python
import requests
import json

# API配置
BASE_URL = "{{ api_overview.base_url if api_overview else "http://localhost:8000/api" }}"
HEADERS = {
    "Content-Type": "application/json",
    {% if authentication %}"Authorization": "{{ authentication.example if authentication else "Bearer <your-token>" }}"{% endif %}
}

# GET 请求示例
def get_data(endpoint):
    response = requests.get(f"{BASE_URL}/{endpoint}", headers=HEADERS)
    return response.json()

# POST 请求示例  
def create_data(endpoint, data):
    response = requests.post(
        f"{BASE_URL}/{endpoint}", 
        headers=HEADERS,
        json=data
    )
    return response.json()

# 使用示例
try:
    result = get_data("users")
    print(json.dumps(result, indent=2))
except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
```

#### JavaScript 示例

```javascript
// 使用 fetch API
class APIClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl || "{{ api_overview.base_url if api_overview else "http://localhost:8000/api" }}";
        this.headers = {
            "Content-Type": "application/json",
            {% if authentication %}"Authorization": token || "{{ authentication.example if authentication else "Bearer <your-token>" }}"{% endif %}
        };
    }

    async get(endpoint) {
        const response = await fetch(`${this.baseUrl}/${endpoint}`, {
            method: "GET",
            headers: this.headers
        });
        return await response.json();
    }

    async post(endpoint, data) {
        const response = await fetch(`${this.baseUrl}/${endpoint}`, {
            method: "POST", 
            headers: this.headers,
            body: JSON.stringify(data)
        });
        return await response.json();
    }
}

// 使用示例
const client = new APIClient();

client.get("users")
    .then(data => console.log(data))
    .catch(error => console.error("请求失败:", error));
```

#### cURL 示例

```bash
# GET 请求
curl -X GET \
  "{{ api_overview.base_url if api_overview else "http://localhost:8000/api" }}/users" \
  -H "Content-Type: application/json" \
  {% if authentication %}-H "{{ authentication.header if authentication else "Authorization: Bearer <token>" }}" \{% endif %}

# POST 请求
curl -X POST \
  "{{ api_overview.base_url if api_overview else "http://localhost:8000/api" }}/users" \
  -H "Content-Type: application/json" \
  {% if authentication %}-H "{{ authentication.header if authentication else "Authorization: Bearer <token>" }}" \{% endif %}
  -d '{
    "name": "用户名",
    "email": "<EMAIL>"
  }'
```
{% endif %}

## 最佳实践

### 请求优化

1. **批量操作**: 尽可能使用批量接口减少请求次数
2. **分页查询**: 使用分页参数获取大量数据
3. **字段筛选**: 只请求需要的字段减少数据传输
4. **缓存策略**: 合理利用HTTP缓存机制

### 错误处理

1. **状态码检查**: 始终检查HTTP状态码
2. **错误重试**: 对可重试错误实施退避重试
3. **超时设置**: 设置合理的请求超时时间
4. **日志记录**: 记录API请求和响应用于调试

### 安全建议

1. **Token安全**: 妥善保管API Token，定期轮换
2. **HTTPS使用**: 生产环境务必使用HTTPS
3. **参数验证**: 客户端也要进行参数验证
4. **敏感信息**: 避免在URL中传递敏感信息

## 变更历史

{% if api_changelog and api_changelog|length > 0 %}
{% for change in api_changelog %}
### {{ change.version }} ({{ change.date }})

{{ change.description }}

{% if change.breaking_changes and change.breaking_changes|length > 0 %}
**Breaking Changes:**
{% for breaking in change.breaking_changes %}
- {{ breaking }}
{% endfor %}
{% endif %}

{% if change.new_endpoints and change.new_endpoints|length > 0 %}
**新增接口:**
{% for endpoint in change.new_endpoints %}
- {{ endpoint }}
{% endfor %}
{% endif %}

{% if change.deprecated and change.deprecated|length > 0 %}
**废弃接口:**
{% for deprecated in change.deprecated %}
- {{ deprecated }}
{% endfor %}
{% endif %}

{% endfor %}
{% else %}
### 版本历史

API版本遵循语义化版本控制，详细变更历史请查看：

- [CHANGELOG.md](./CHANGELOG.md) - 完整变更日志
- [GitHub Releases]({{ github_url }}/releases) - 版本发布说明

### 版本兼容性

- **v1.x**: 当前稳定版本，向后兼容
- **v2.x**: 计划中的下一代版本（包含破坏性变更）

{% endif %}

## 支持与反馈

### 获取帮助

- **文档中心**: [项目文档]({{ docs_url if docs_url else "./" }})
- **问题报告**: [GitHub Issues]({{ github_url }}/issues)
- **功能请求**: [GitHub Issues]({{ github_url }}/issues)
- **社区讨论**: [GitHub Discussions]({{ github_url }}/discussions)

### 联系方式

- **技术支持**: {{ support_email if support_email else "<EMAIL>" }}
- **商务合作**: {{ business_email if business_email else "<EMAIL>" }}

---

*此文档由 GenDocs 自动生成于 {{ generation_date }}*
*API版本: {{ api_overview.version if api_overview else "v1" }} | 文档版本: {{ doc_version if doc_version else "1.0" }}*