"""
Python项目分析器

专门用于分析Python项目的结构、依赖、API等信息。
"""

import ast
import json
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
import logging

from .base import (
    BaseAnalyzer, ProjectAnalysisResult, ProjectType, FrameworkType,
    DependencyInfo, APIInfo, ModuleInfo, TechStackInfo, 
    ArchitectureInfo, QualityMetrics, AnalysisError
)

logger = logging.getLogger(__name__)


class PythonAnalyzer(BaseAnalyzer):
    """Python项目分析器
    
    分析Python项目的结构、依赖、框架、API接口等信息。
    """
    
    def __init__(self):
        """初始化Python分析器"""
        super().__init__()
        
        # Python框架检测指示器
        self._framework_indicators = {
            FrameworkType.DJANGO.value: [
                'manage.py',
                'settings.py',
                'django',
                '*/settings.py',
                'requirements.txt'  # 需要进一步检查内容
            ],
            FrameworkType.FLASK.value: [
                'app.py',
                'application.py',
                'run.py',
                'wsgi.py',
                'requirements.txt'  # 需要进一步检查内容
            ],
            FrameworkType.FASTAPI.value: [
                'main.py',
                'app.py',
                'requirements.txt'  # 需要进一步检查内容
            ]
        }
    
    @property
    def project_type(self) -> ProjectType:
        """项目类型"""
        return ProjectType.PYTHON
    
    @property
    def supported_extensions(self) -> Set[str]:
        """支持的文件扩展名"""
        return {'.py', '.pyw', '.pyi'}
    
    def can_analyze(self, project_path: Path) -> bool:
        """检查是否能分析该项目
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            是否支持分析该项目
        """
        # 检查是否有Python文件
        python_files = list(project_path.rglob('*.py'))
        if not python_files:
            return False
        
        # 检查是否有典型的Python项目文件
        python_indicators = [
            'setup.py',
            'pyproject.toml',
            'requirements.txt',
            'Pipfile',
            'poetry.lock',
            'main.py',
            'app.py',
            'manage.py'
        ]
        
        for indicator in python_indicators:
            if (project_path / indicator).exists():
                return True
        
        # 如果有较多Python文件，也认为是Python项目
        return len(python_files) >= 3
    
    def analyze_project(self, project_path: Path) -> ProjectAnalysisResult:
        """分析Python项目
        
        Args:
            project_path: 项目根目录路径
            
        Returns:
            项目分析结果
        """
        try:
            logger.info(f"开始分析Python项目: {project_path}")
            
            # 创建分析结果
            result = ProjectAnalysisResult(
                project_name=project_path.name,
                project_type=ProjectType.PYTHON,
                root_path=project_path
            )
            
            # 基本信息分析
            self._analyze_basic_info(project_path, result)
            
            # 依赖分析
            self._analyze_dependencies(project_path, result)
            
            # 框架检测
            result.framework = self.detect_framework(project_path)
            
            # 技术栈分析
            self._analyze_tech_stack(project_path, result)
            
            # 模块结构分析
            self._analyze_modules(project_path, result)
            
            # API分析
            self._analyze_apis(project_path, result)
            
            # 架构分析
            self._analyze_architecture(project_path, result)
            
            # 质量指标分析
            self._analyze_quality_metrics(project_path, result)
            
            # 读取README和CHANGELOG
            self._read_documentation_files(project_path, result)
            
            logger.info("Python项目分析完成")
            return result
            
        except Exception as e:
            logger.error(f"Python项目分析失败: {e}")
            raise AnalysisError(f"Python项目分析失败: {e}")
    
    def _analyze_basic_info(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析基本项目信息"""
        # 从setup.py获取信息
        setup_py = project_path / 'setup.py'
        if setup_py.exists():
            self._parse_setup_py(setup_py, result)
        
        # 从pyproject.toml获取信息
        pyproject_toml = project_path / 'pyproject.toml'
        if pyproject_toml.exists():
            self._parse_pyproject_toml(pyproject_toml, result)
        
        # 从__init__.py获取版本信息
        self._find_version_info(project_path, result)
    
    def _parse_setup_py(self, setup_py: Path, result: ProjectAnalysisResult) -> None:
        """解析setup.py文件"""
        try:
            with open(setup_py, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式提取信息
            patterns = {
                'name': r'name\s*=\s*["\']([^"\']+)["\']',
                'version': r'version\s*=\s*["\']([^"\']+)["\']',
                'description': r'description\s*=\s*["\']([^"\']+)["\']',
                'author': r'author\s*=\s*["\']([^"\']+)["\']',
                'license': r'license\s*=\s*["\']([^"\']+)["\']'
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    setattr(result, key, match.group(1))
                    
        except Exception as e:
            logger.warning(f"解析setup.py失败: {e}")
    
    def _parse_pyproject_toml(self, pyproject_toml: Path, result: ProjectAnalysisResult) -> None:
        """解析pyproject.toml文件"""
        try:
            import tomllib
            with open(pyproject_toml, 'rb') as f:
                data = tomllib.load(f)
            
            # 提取项目信息
            project_info = data.get('project', {})
            if project_info:
                result.project_name = project_info.get('name', result.project_name)
                result.version = project_info.get('version', result.version)
                result.description = project_info.get('description', result.description)
                result.license = str(project_info.get('license', result.license))
                
                authors = project_info.get('authors', [])
                if authors and isinstance(authors[0], dict):
                    result.author = authors[0].get('name', result.author)
                    
        except Exception as e:
            logger.warning(f"解析pyproject.toml失败: {e}")
    
    def _find_version_info(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """在__init__.py或_version.py中查找版本信息"""
        version_files = [
            project_path / '__init__.py',
            project_path / f'{result.project_name}' / '__init__.py',
            project_path / 'src' / result.project_name / '__init__.py',
            project_path / '_version.py',
            project_path / 'version.py'
        ]
        
        version_patterns = [
            r'__version__\s*=\s*["\']([^"\']+)["\']',
            r'VERSION\s*=\s*["\']([^"\']+)["\']',
            r'version\s*=\s*["\']([^"\']+)["\']'
        ]
        
        for version_file in version_files:
            if version_file.exists():
                try:
                    with open(version_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    for pattern in version_patterns:
                        match = re.search(pattern, content)
                        if match:
                            result.version = match.group(1)
                            return
                except Exception:
                    continue
    
    def _analyze_dependencies(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析项目依赖"""
        dependencies = []
        
        # 解析requirements.txt
        requirements_files = [
            'requirements.txt',
            'requirements-dev.txt',
            'dev-requirements.txt',
            'requirements/base.txt',
            'requirements/production.txt'
        ]
        
        for req_file in requirements_files:
            req_path = project_path / req_file
            if req_path.exists():
                deps = self._parse_requirements_file(req_path)
                is_dev = 'dev' in req_file.lower()
                for dep in deps:
                    dep.is_dev_dependency = is_dev
                dependencies.extend(deps)
        
        # 解析Pipfile
        pipfile = project_path / 'Pipfile'
        if pipfile.exists():
            dependencies.extend(self._parse_pipfile(pipfile))
        
        # 解析pyproject.toml依赖
        pyproject_toml = project_path / 'pyproject.toml'
        if pyproject_toml.exists():
            dependencies.extend(self._parse_pyproject_dependencies(pyproject_toml))
        
        result.dependencies = dependencies
    
    def _parse_requirements_file(self, req_file: Path) -> List[DependencyInfo]:
        """解析requirements文件"""
        dependencies = []
        try:
            with open(req_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and not line.startswith('-'):
                        dep = self._parse_requirement_line(line)
                        if dep:
                            dependencies.append(dep)
        except Exception as e:
            logger.warning(f"解析requirements文件失败 {req_file}: {e}")
        
        return dependencies
    
    def _parse_requirement_line(self, line: str) -> Optional[DependencyInfo]:
        """解析单行requirements"""
        # 移除注释
        line = line.split('#')[0].strip()
        if not line:
            return None
        
        # 解析包名和版本
        # 支持格式: package, package==1.0.0, package>=1.0.0, package[extra]>=1.0.0
        match = re.match(r'^([a-zA-Z0-9_-]+(?:\[[^\]]+\])?)\s*([><=!]+[^;]+)?', line)
        if match:
            name = match.group(1)
            version_spec = match.group(2)
            
            return DependencyInfo(
                name=name,
                version=version_spec.strip() if version_spec else None,
                is_external=True
            )
        return None
    
    def _parse_pipfile(self, pipfile: Path) -> List[DependencyInfo]:
        """解析Pipfile"""
        dependencies = []
        try:
            import tomllib
            with open(pipfile, 'rb') as f:
                data = tomllib.load(f)
            
            # 解析生产依赖
            packages = data.get('packages', {})
            for name, version in packages.items():
                dependencies.append(DependencyInfo(
                    name=name,
                    version=str(version) if version != "*" else None,
                    is_external=True,
                    is_dev_dependency=False
                ))
            
            # 解析开发依赖
            dev_packages = data.get('dev-packages', {})
            for name, version in dev_packages.items():
                dependencies.append(DependencyInfo(
                    name=name,
                    version=str(version) if version != "*" else None,
                    is_external=True,
                    is_dev_dependency=True
                ))
                
        except Exception as e:
            logger.warning(f"解析Pipfile失败: {e}")
        
        return dependencies
    
    def _parse_pyproject_dependencies(self, pyproject_toml: Path) -> List[DependencyInfo]:
        """解析pyproject.toml中的依赖"""
        dependencies = []
        try:
            import tomllib
            with open(pyproject_toml, 'rb') as f:
                data = tomllib.load(f)
            
            # 解析project.dependencies
            project_deps = data.get('project', {}).get('dependencies', [])
            for dep in project_deps:
                parsed = self._parse_requirement_line(dep)
                if parsed:
                    dependencies.append(parsed)
            
            # 解析optional-dependencies
            optional_deps = data.get('project', {}).get('optional-dependencies', {})
            for group_name, deps in optional_deps.items():
                is_dev = 'dev' in group_name.lower() or 'test' in group_name.lower()
                for dep in deps:
                    parsed = self._parse_requirement_line(dep)
                    if parsed:
                        parsed.is_dev_dependency = is_dev
                        dependencies.append(parsed)
                        
        except Exception as e:
            logger.warning(f"解析pyproject.toml依赖失败: {e}")
        
        return dependencies
    
    def detect_framework(self, project_path: Path) -> Optional[FrameworkType]:
        """检测Python框架"""
        # 首先使用基类的通用检测
        framework = super().detect_framework(project_path)
        if framework:
            return framework
        
        # Python特定的框架检测
        # 检查依赖中是否包含框架
        dependencies = []
        req_file = project_path / 'requirements.txt'
        if req_file.exists():
            dependencies = self._parse_requirements_file(req_file)
        
        dep_names = {dep.name.lower() for dep in dependencies}
        
        # 根据依赖判断框架
        if 'django' in dep_names:
            return FrameworkType.DJANGO
        elif 'flask' in dep_names:
            return FrameworkType.FLASK
        elif 'fastapi' in dep_names:
            return FrameworkType.FASTAPI
        
        # 检查代码中的import语句
        python_files = self.get_project_files(project_path)
        imports = self._extract_imports(python_files[:10])  # 只检查前10个文件
        
        if any('django' in imp for imp in imports):
            return FrameworkType.DJANGO
        elif any('flask' in imp for imp in imports):
            return FrameworkType.FLASK
        elif any('fastapi' in imp for imp in imports):
            return FrameworkType.FASTAPI
        
        return None
    
    def _extract_imports(self, files: List[Path]) -> List[str]:
        """提取Python文件中的import语句"""
        imports = []
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    tree = ast.parse(f.read())
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.append(node.module)
                            
            except Exception as e:
                logger.debug(f"解析import失败 {file_path}: {e}")
                continue
        
        return imports
    
    def _analyze_tech_stack(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析技术栈"""
        tech_stack = TechStackInfo()
        
        # 语言
        tech_stack.languages = ['Python']
        
        # 框架
        if result.framework:
            tech_stack.frameworks = [result.framework.value]
        
        # 从依赖推断技术栈
        dep_names = {dep.name.lower() for dep in result.dependencies}
        
        # 数据库
        database_deps = {
            'psycopg2', 'psycopg2-binary', 'postgresql': 'PostgreSQL',
            'mysql-connector-python', 'pymysql': 'MySQL',
            'redis': 'Redis',
            'mongodb', 'pymongo': 'MongoDB',
            'sqlite': 'SQLite'
        }
        
        for dep, db in database_deps.items():
            if dep in dep_names and db not in tech_stack.databases:
                tech_stack.databases.append(db)
        
        # 工具和库
        tools = []
        tool_deps = {
            'celery': 'Celery (任务队列)',
            'gunicorn', 'uvicorn': 'WSGI/ASGI服务器',
            'nginx': 'Nginx',
            'docker': 'Docker',
            'pytest': '单元测试',
            'black': '代码格式化',
            'flake8': '代码检查',
            'mypy': '类型检查'
        }
        
        for dep, tool in tool_deps.items():
            if dep in dep_names and tool not in tools:
                tools.append(tool)
        
        tech_stack.tools = tools
        
        # 运行时
        tech_stack.runtime = ['Python 3.x']
        
        result.tech_stack = tech_stack
    
    def _analyze_modules(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析模块结构"""
        modules = []
        
        # 查找Python包目录
        for path in project_path.rglob('__init__.py'):
            module_dir = path.parent
            if self._should_include_file(module_dir):
                module_info = self._analyze_module(module_dir, project_path)
                modules.append(module_info)
        
        result.modules = modules
    
    def _analyze_module(self, module_dir: Path, project_root: Path) -> ModuleInfo:
        """分析单个模块"""
        relative_path = module_dir.relative_to(project_root)
        module_name = str(relative_path).replace('/', '.').replace('\\', '.')
        
        # 统计文件和行数
        python_files = list(module_dir.rglob('*.py'))
        file_count = len(python_files)
        total_lines = 0
        
        for file_path in python_files:
            if self._should_include_file(file_path):
                lines_info = self.count_lines_of_code(file_path)
                total_lines += lines_info['total_lines']
        
        # 获取模块描述
        init_file = module_dir / '__init__.py'
        description = None
        if init_file.exists():
            description = self._extract_module_docstring(init_file)
        
        return ModuleInfo(
            name=module_name,
            path=str(relative_path),
            description=description,
            file_count=file_count,
            line_count=total_lines
        )
    
    def _extract_module_docstring(self, init_file: Path) -> Optional[str]:
        """提取模块文档字符串"""
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                tree = ast.parse(f.read())
            
            if (tree.body and 
                isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, (ast.Str, ast.Constant))):
                if isinstance(tree.body[0].value, ast.Str):
                    return tree.body[0].value.s.strip()
                else:  # ast.Constant (Python 3.8+)
                    value = tree.body[0].value.value
                    if isinstance(value, str):
                        return value.strip()
        except Exception as e:
            logger.debug(f"提取模块文档字符串失败 {init_file}: {e}")
        
        return None
    
    def _analyze_apis(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析API接口"""
        apis = []
        
        # 根据框架类型分析API
        if result.framework == FrameworkType.DJANGO:
            apis.extend(self._analyze_django_apis(project_path))
        elif result.framework == FrameworkType.FLASK:
            apis.extend(self._analyze_flask_apis(project_path))
        elif result.framework == FrameworkType.FASTAPI:
            apis.extend(self._analyze_fastapi_apis(project_path))
        else:
            # 通用API分析
            apis.extend(self._analyze_generic_apis(project_path))
        
        result.apis = apis
    
    def _analyze_django_apis(self, project_path: Path) -> List[APIInfo]:
        """分析Django API"""
        apis = []
        
        # 查找urls.py文件
        for urls_file in project_path.rglob('urls.py'):
            if self._should_include_file(urls_file):
                apis.extend(self._parse_django_urls(urls_file))
        
        return apis
    
    def _parse_django_urls(self, urls_file: Path) -> List[APIInfo]:
        """解析Django URL配置"""
        apis = []
        try:
            with open(urls_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的正则匹配URL模式
            # 这里只做基础解析，复杂的URL模式可能需要更完善的解析
            url_patterns = re.findall(
                r"path\s*\(\s*['\"]([^'\"]+)['\"].*?\)",
                content,
                re.MULTILINE
            )
            
            for pattern in url_patterns:
                apis.append(APIInfo(
                    method="GET",  # Django默认，实际需要分析视图
                    path=f"/{pattern.lstrip('/')}" if not pattern.startswith('/') else pattern,
                    description=f"Django URL: {pattern}",
                    module=str(urls_file.relative_to(urls_file.parents[2]))
                ))
                
        except Exception as e:
            logger.debug(f"解析Django URLs失败 {urls_file}: {e}")
        
        return apis
    
    def _analyze_flask_apis(self, project_path: Path) -> List[APIInfo]:
        """分析Flask API"""
        apis = []
        
        python_files = self.get_project_files(project_path)
        for file_path in python_files:
            apis.extend(self._parse_flask_routes(file_path))
        
        return apis
    
    def _parse_flask_routes(self, file_path: Path) -> List[APIInfo]:
        """解析Flask路由"""
        apis = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找Flask路由装饰器
            route_patterns = re.findall(
                r"@\w*\.route\s*\(\s*['\"]([^'\"]+)['\"](?:,\s*methods\s*=\s*\[([^\]]+)\])?\)",
                content,
                re.MULTILINE
            )
            
            for pattern, methods in route_patterns:
                if methods:
                    method_list = [m.strip().strip("'\"") for m in methods.split(',')]
                else:
                    method_list = ['GET']
                
                for method in method_list:
                    apis.append(APIInfo(
                        method=method.upper(),
                        path=pattern,
                        description=f"Flask route: {pattern}",
                        module=str(file_path.name)
                    ))
                    
        except Exception as e:
            logger.debug(f"解析Flask路由失败 {file_path}: {e}")
        
        return apis
    
    def _analyze_fastapi_apis(self, project_path: Path) -> List[APIInfo]:
        """分析FastAPI"""
        apis = []
        
        python_files = self.get_project_files(project_path)
        for file_path in python_files:
            apis.extend(self._parse_fastapi_routes(file_path))
        
        return apis
    
    def _parse_fastapi_routes(self, file_path: Path) -> List[APIInfo]:
        """解析FastAPI路由"""
        apis = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找FastAPI路由装饰器
            route_patterns = re.findall(
                r"@\w*\.(get|post|put|delete|patch|options|head)\s*\(\s*['\"]([^'\"]+)['\"]",
                content,
                re.MULTILINE | re.IGNORECASE
            )
            
            for method, path in route_patterns:
                apis.append(APIInfo(
                    method=method.upper(),
                    path=path,
                    description=f"FastAPI endpoint: {path}",
                    module=str(file_path.name)
                ))
                
        except Exception as e:
            logger.debug(f"解析FastAPI路由失败 {file_path}: {e}")
        
        return apis
    
    def _analyze_generic_apis(self, project_path: Path) -> List[APIInfo]:
        """通用API分析"""
        # 对于未识别框架的项目，进行通用API分析
        # 可以查找常见的API模式或HTTP相关代码
        return []
    
    def _analyze_architecture(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析项目架构"""
        architecture = ArchitectureInfo()
        
        # 根据框架推断架构模式
        if result.framework == FrameworkType.DJANGO:
            architecture.pattern = "MTV (Model-Template-View)"
            architecture.layers = [
                {"name": "Models", "description": "数据模型层"},
                {"name": "Views", "description": "视图逻辑层"},
                {"name": "Templates", "description": "模板表示层"},
                {"name": "URLs", "description": "URL路由层"}
            ]
        elif result.framework == FrameworkType.FLASK:
            architecture.pattern = "MVC (Model-View-Controller)"
            architecture.layers = [
                {"name": "Models", "description": "数据模型层"},
                {"name": "Views", "description": "控制器层"},
                {"name": "Templates", "description": "视图表示层"}
            ]
        elif result.framework == FrameworkType.FASTAPI:
            architecture.pattern = "API-First Architecture"
            architecture.layers = [
                {"name": "API Routes", "description": "API路由层"},
                {"name": "Business Logic", "description": "业务逻辑层"},
                {"name": "Data Access", "description": "数据访问层"}
            ]
        
        # 检测设计模式
        patterns = self._detect_design_patterns(project_path)
        architecture.design_patterns = patterns
        
        result.architecture = architecture
    
    def _detect_design_patterns(self, project_path: Path) -> List[str]:
        """检测设计模式"""
        patterns = []
        
        # 简单的模式检测逻辑
        python_files = self.get_project_files(project_path)
        
        for file_path in python_files[:20]:  # 限制检查文件数量
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检测常见设计模式
                if 'Singleton' in content or '__new__' in content:
                    if 'Singleton' not in patterns:
                        patterns.append('Singleton')
                
                if 'Factory' in content or 'create_' in content:
                    if 'Factory' not in patterns:
                        patterns.append('Factory')
                
                if 'Observer' in content or 'notify' in content:
                    if 'Observer' not in patterns:
                        patterns.append('Observer')
                
                if 'Strategy' in content or 'algorithm' in content:
                    if 'Strategy' not in patterns:
                        patterns.append('Strategy')
                        
            except Exception:
                continue
        
        return patterns
    
    def _analyze_quality_metrics(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """分析代码质量指标"""
        metrics = QualityMetrics()
        
        python_files = self.get_project_files(project_path)
        
        total_files = len(python_files)
        total_lines = 0
        code_lines = 0
        comment_lines = 0
        blank_lines = 0
        
        for file_path in python_files:
            lines_info = self.count_lines_of_code(file_path)
            total_lines += lines_info['total_lines']
            code_lines += lines_info['code_lines']
            comment_lines += lines_info['comment_lines']
            blank_lines += lines_info['blank_lines']
        
        metrics.total_files = total_files
        metrics.total_lines = total_lines
        metrics.code_lines = code_lines
        metrics.comment_lines = comment_lines
        metrics.blank_lines = blank_lines
        
        # 尝试获取更详细的质量指标
        try:
            complexity = self._calculate_complexity(project_path)
            metrics.cyclomatic_complexity = complexity
        except Exception as e:
            logger.debug(f"计算复杂度失败: {e}")
        
        result.quality_metrics = metrics
    
    def _calculate_complexity(self, project_path: Path) -> Optional[float]:
        """计算圈复杂度"""
        try:
            # 使用radon计算复杂度
            result = subprocess.run(
                ['radon', 'cc', str(project_path), '-a'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # 解析radon输出
                output = result.stdout
                # 简单提取平均复杂度
                for line in output.split('\n'):
                    if 'Average complexity:' in line:
                        complexity_str = line.split(':')[-1].strip()
                        return float(complexity_str.split()[0])
                        
        except Exception as e:
            logger.debug(f"使用radon计算复杂度失败: {e}")
        
        return None
    
    def _read_documentation_files(self, project_path: Path, result: ProjectAnalysisResult) -> None:
        """读取文档文件"""
        # 读取README
        readme_files = ['README.md', 'README.rst', 'README.txt', 'README']
        for readme_name in readme_files:
            readme_path = project_path / readme_name
            if readme_path.exists():
                try:
                    with open(readme_path, 'r', encoding='utf-8') as f:
                        result.readme_content = f.read()
                    break
                except Exception as e:
                    logger.debug(f"读取README失败: {e}")
        
        # 读取CHANGELOG
        changelog_files = ['CHANGELOG.md', 'CHANGELOG.rst', 'CHANGELOG.txt', 'HISTORY.md']
        for changelog_name in changelog_files:
            changelog_path = project_path / changelog_name
            if changelog_path.exists():
                try:
                    with open(changelog_path, 'r', encoding='utf-8') as f:
                        result.changelog_content = f.read()
                    break
                except Exception as e:
                    logger.debug(f"读取CHANGELOG失败: {e}")