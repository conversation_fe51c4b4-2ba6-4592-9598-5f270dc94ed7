# GenDocs

一个智能的企业级Python项目文档生成工具，支持AI增强的文档生成、批量处理、性能监控和多种输出格式。

## 功能特点

### 🤖 AI增强功能
- 智能项目分析和文档生成
- 支持多种AI提供商（OpenAI、DeepSeek等）
- 自动生成项目概览、API文档、架构决策记录等
- 基于项目特点生成定制化内容

### 📋 企业级文档生成
- **项目概览** - 自动分析项目结构和技术栈
- **API文档** - 提取和格式化API接口文档
- **架构文档** - 生成系统架构说明
- **部署指南** - 创建部署和运维文档
- **开发指南** - 生成开发环境设置指南
- **变更日志** - 自动化变更记录
- **运行手册** - 系统运维操作指南
- **架构决策记录** - ADR文档生成
- **用户故事映射** - 需求分析文档

### 🔄 批量处理
- 多项目并行处理
- 作业队列管理
- 任务调度和监控
- 错误恢复机制

### 📊 性能监控
- 实时性能指标收集
- 系统资源监控
- 详细的处理统计
- 性能报告生成

### 🛠️ 高级功能
- 完整的CLI命令行界面
- 灵活的配置管理系统
- 错误处理和日志记录
- 备份和恢复机制
- 模板系统自定义

## 安装

### 基础安装

```bash
pip install -e .
```

### 开发环境安装

```bash
# 克隆项目
git clone <repository-url>
cd gendocs

# 安装依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .
```

### 系统要求

- Python >= 3.8
- 可选：AI提供商API密钥（用于AI增强功能）

## 快速开始

### 1. 初始化配置

```bash
gendocs init
```

这将创建一个默认配置文件 `gendocs.yaml`。

### 2. 生成项目文档

```bash
# 生成完整文档
gendocs generate /path/to/your/project

# 生成特定类型的文档
gendocs generate /path/to/your/project --generators overview,api

# 指定输出目录
gendocs generate /path/to/your/project --output ./docs
```

### 3. 批量处理多个项目

```bash
# 启动批量处理服务
gendocs batch start

# 添加项目到处理队列
gendocs batch add-job /path/to/project1 --output ./docs/project1
gendocs batch add-job /path/to/project2 --output ./docs/project2

# 查看处理状态
gendocs batch status

# 停止批量处理
gendocs batch stop
```

### 4. 监控和报告

```bash
# 启动性能监控
gendocs monitor start

# 查看监控状态
gendocs monitor status

# 生成性能报告
gendocs monitor report --output ./reports/performance.html
```

## 配置

GenDocs使用YAML格式的配置文件。默认配置文件示例：

```yaml
# AI提供商配置
ai:
  provider: openai  # 或 deepseek
  model: gpt-3.5-turbo
  api_key: ${OPENAI_API_KEY}  # 支持环境变量
  temperature: 0.7
  max_tokens: 2000

# 文档生成配置
generation:
  output_dir: docs
  templates_dir: templates
  generators:
    - overview
    - api
    - architecture
    - deployment
    - development
    - changelog

# 批量处理配置
batch:
  max_workers: 4
  timeout: 300
  queue_size: 100

# 监控配置
monitoring:
  enable_performance_tracking: true
  enable_profiling: false
  metrics_retention_days: 30

# 日志配置
logging:
  level: INFO
  enable_file_logging: true
  log_file: gendocs.log
  enable_json_logging: false
```

## 命令行界面

### 主命令

```bash
gendocs --help
```

### 文档生成

```bash
# 基础生成
gendocs generate <project_path>

# 高级选项
gendocs generate <project_path> \
  --output <output_dir> \
  --config <config_file> \
  --generators overview,api,architecture \
  --no-ai  # 禁用AI功能
```

### 配置管理

```bash
# 显示当前配置
gendocs config show

# 设置配置值
gendocs config set ai.provider openai

# 获取配置值
gendocs config get ai.provider

# 验证配置
gendocs config validate
```

### 批量处理

```bash
# 服务管理
gendocs batch start
gendocs batch stop
gendocs batch status

# 作业管理
gendocs batch add-job <project_path> --output <output_dir>
gendocs batch list-jobs
gendocs batch cancel-job <job_id>
```

### 监控

```bash
# 监控服务
gendocs monitor start
gendocs monitor stop
gendocs monitor status

# 报告生成
gendocs monitor report --output report.html
gendocs monitor metrics --format json
```

## 生成器类型

GenDocs支持多种文档生成器：

- **overview** - 项目概览文档
- **api** - API接口文档
- **architecture** - 架构设计文档
- **deployment** - 部署指南
- **development** - 开发指南
- **changelog** - 变更日志
- **runbook** - 运行手册
- **adr** - 架构决策记录
- **user_story** - 用户故事映射

## AI提供商

支持的AI提供商：

### OpenAI
```yaml
ai:
  provider: openai
  model: gpt-3.5-turbo  # 或 gpt-4
  api_key: ${OPENAI_API_KEY}
```

### DeepSeek
```yaml
ai:
  provider: deepseek
  model: deepseek-chat
  api_key: ${DEEPSEEK_API_KEY}
```

## 模板自定义

GenDocs支持Jinja2模板自定义：

```
templates/
├── base/                    # 基础模板
│   ├── project_overview.md.j2
│   ├── api_docs.md.j2
│   └── architecture.md.j2
└── ai_enhanced/             # AI增强模板
    ├── adr.md.j2
    ├── runbook.md.j2
    └── user_story_map.md.j2
```

## 输出示例

生成的文档结构：

```
docs/
├── README.md               # 项目概览
├── api/                    # API文档
│   ├── modules.md
│   └── endpoints.md
├── architecture/           # 架构文档
│   ├── overview.md
│   ├── decisions/
│   │   └── adr-001.md
│   └── diagrams/
├── deployment/             # 部署文档
│   ├── guide.md
│   └── runbook.md
├── development/            # 开发文档
│   ├── setup.md
│   └── guidelines.md
└── CHANGELOG.md           # 变更日志
```

## 开发和测试

### 运行测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_config.py

# 运行集成测试
pytest tests/integration/
```

### 测试覆盖

项目包含完整的测试套件：

- **单元测试** - 4000+行测试代码，覆盖所有核心组件
- **集成测试** - 端到端工作流测试
- **性能测试** - 批量处理和并发测试

### 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 性能优化

### 批量处理最佳实践

- 设置合适的worker数量（通常为CPU核心数）
- 为大型项目增加超时时间
- 使用监控功能跟踪处理性能

### AI使用优化

- 选择合适的模型（速度vs质量权衡）
- 合理设置temperature参数
- 监控API使用量和成本

## 故障排除

### 常见问题

1. **AI API错误**
   ```bash
   # 检查API密钥配置
   gendocs config get ai.api_key
   
   # 验证配置
   gendocs config validate
   ```

2. **生成失败**
   ```bash
   # 查看详细日志
   gendocs generate <project> --verbose
   
   # 检查项目结构
   gendocs analyze <project>
   ```

3. **性能问题**
   ```bash
   # 启用性能监控
   gendocs monitor start
   
   # 生成性能报告
   gendocs monitor report
   ```

### 日志文件

默认日志位置：
- Linux/Mac: `~/.gendocs/logs/gendocs.log`
- Windows: `%USERPROFILE%\.gendocs\logs\gendocs.log`

## 许可证

MIT License

## 联系和支持

- GitHub Issues: 报告bug和请求功能
- 文档: 查看在线文档获取更多信息
- 社区: 加入讨论和分享经验

---

**GenDocs** - 让文档生成变得智能和高效！