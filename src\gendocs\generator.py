"""
文档生成器模块
"""

import logging
import os
import subprocess
from pathlib import Path
from typing import List, Optional

class Config:
    """配置类"""
    def __init__(self):
        self.project_root: Path = Path()
        self.docs_dir: Path = Path()
        self.src_dir: Optional[Path] = None
        self.exclude_dirs: List[str] = []
        self.exclude_files: List[str] = []

class FileUtils:
    """文件工具类"""
    def ensure_dir(self, path: Path) -> None:
        """确保目录存在"""
        path.mkdir(parents=True, exist_ok=True)

    def get_python_files(self, directory: Path) -> List[Path]:
        """获取目录下的所有Python文件"""
        python_files = []
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        return python_files

class DocGenerator:
    """文档生成器类"""
    def __init__(self, config: Config, utils: FileUtils):
        self.config = config
        self.utils = utils
        self.setup_logging()

    def setup_logging(self) -> None:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(levelname)s %(name)s:%(filename)s:%(lineno)d %(message)s'
        )

    def generate(self) -> None:
        """生成所有文档"""
        logging.info("\n==== 开始生成文档 ====")
        
        # 确保输出目录存在
        self.utils.ensure_dir(self.config.docs_dir)
        
        # 生成类图
        self.generate_class_diagrams(self.config.project_root, self.config.docs_dir)
        
        logging.info("\n==== 文档生成完成 ====")

    def generate_class_diagrams(self, project_dir: Path, output_dir: Path) -> None:
        """生成类图"""
        logging.info("\n==== 生成类图 ====")
        
        # 检查是否安装了 Graphviz
        try:
            # 尝试运行 dot -V 命令来检查 Graphviz 是否安装
            subprocess.run(['dot', '-V'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            logging.error("⚠️ 生成类图需要安装 Graphviz。请按照以下步骤安装：")
            logging.error("1. 从 https://graphviz.org/download/ 下载并安装 Graphviz")
            logging.error("2. 将 Graphviz 的 bin 目录添加到系统 PATH 中")
            return
        
        # 记录当前目录，以便后续返回
        original_dir = os.getcwd()
        
        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 切换到项目目录
            os.chdir(str(project_dir))
            logging.info(f"当前工作目录: {os.getcwd()}")
            
            # 查找所有Python包目录（包含__init__.py的目录）
            py_packages = []
            for root, _, files in os.walk('src'):
                if '__init__.py' in files:
                    py_packages.append(root.replace('\\', '/'))
            
            if not py_packages:
                logging.warning(f"⚠️ 在 {project_dir} 中未找到Python包")
                return
            
            logging.info(f"找到的Python包: {py_packages}")
            
            # 构建 pyreverse 命令
            cmd = [
                sys.executable,
                '-m',
                'pylint.pyreverse.main',
                '-o', 'png',  # 输出格式
                '-d', str(output_dir),  # 输出目录
                '-p', 'src'  # 项目名称
            ]
            cmd.extend(py_packages)  # 添加所有Python包
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                if result.stdout:
                    logging.info(result.stdout)
                if result.stderr:
                    logging.warning(result.stderr)
            except subprocess.CalledProcessError as e:
                logging.error(f"⚠️ pyreverse 命令执行失败: {e.stderr}")
                return
            
            # 检查生成的图片文件
            diagram_types = ["classes", "packages"]
            for diagram in diagram_types:
                image_file = output_dir / f"{diagram}_src.png"
                if image_file.exists() and image_file.stat().st_size > 0:
                    logging.info(f"✓ {diagram}图已生成: {image_file}")
                else:
                    logging.warning(f"⚠️ {diagram}图生成失败: 文件不存在或为空 ({image_file})")
        except Exception as e:
            logging.error(f"⚠️ 生成类图时发生错误: {str(e)}")
        finally:
            # 返回原始目录
            os.chdir(original_dir) 