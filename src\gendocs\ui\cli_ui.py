"""
现代化的TUI界面实现
使用Textual库创建交互式终端UI
"""

from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal, Vertical, VerticalScroll
from textual.widgets import (
    <PERSON><PERSON>, Header, Footer, Static, DirectoryTree, 
    Input, Label, Tree, ListView, ListItem
)
from textual.screen import Screen
from textual.binding import Binding
from textual.widgets._directory_tree import DirEntry
from textual.message import Message
from pathlib import Path
from typing import List, Optional, Dict
import logging
import sys
import os

class PathBar(Static):
    """可点击的路径导航栏"""
    
    class PathClicked(Message):
        """路径点击消息"""
        def __init__(self, path: str) -> None:
            self.path = path
            super().__init__()

    def __init__(self, path: str = "./", **kwargs) -> None:
        super().__init__("", **kwargs)
        self.path = Path(path).resolve()
        self.update_path(self.path)

    def update_path(self, new_path: Path) -> None:
        """更新路径显示"""
        self.path = new_path
        parts = []
        current = new_path
        while current != current.parent:
            parts.append(current.name or str(current))
            current = current.parent
        parts.reverse()
        
        # 构建可点击的路径
        path_text = " > ".join(
            f"[@click=path_click({i})]{part}[/]"
            for i, part in enumerate(parts)
        )
        self.update(f"[yellow]{path_text}")

    def action_path_click(self, index: int) -> None:
        """处理路径点击"""
        path = self.path
        for _ in range(len(str(path).split(os.sep)) - index - 1):
            path = path.parent
        self.post_message(self.PathClicked(str(path)))

class QuickAccessButton(Button):
    """快速访问按钮"""
    def __init__(self, path: str, label: str):
        # 使用英文ID
        id_map = {
            "桌面": "desktop",
            "文档": "documents",
            "下载": "downloads",
            "C盘": "c-drive",
            "用户目录": "home"
        }
        button_id = f"quick-{id_map.get(label, 'btn')}"
        super().__init__(label, id=button_id)
        self.path = path

class ExplorerScreen(Screen):
    """资源管理器风格的目录选择界面"""
    
    BINDINGS = [
        Binding("escape", "app.pop_screen", "返回"),
        Binding("enter", "confirm", "确认"),
        Binding("backspace", "go_parent", "返回上级"),
    ]

    def __init__(self):
        super().__init__()
        self.current_path = Path.cwd()

    def compose(self) -> ComposeResult:
        """构建界面"""
        # 快速访问按钮
        quick_access = [
            ("桌面", os.path.join(os.path.expanduser("~"), "Desktop")),
            ("文档", os.path.join(os.path.expanduser("~"), "Documents")),
            ("下载", os.path.join(os.path.expanduser("~"), "Downloads")),
            ("C盘", "C:\\"),
            ("用户目录", os.path.expanduser("~")),
        ]

        yield Container(
            # 顶部工具栏
            Horizontal(
                Button("⬅", id="back-btn"),
                Button("➡", id="forward-btn"),
                Button("⬆", id="up-btn"),
                classes="toolbar"
            ),
            # 路径栏
            PathBar("./", id="path-bar"),
            # 主区域
            Horizontal(
                # 左侧快速访问
                Vertical(
                    *(QuickAccessButton(path, label) for label, path in quick_access),
                    classes="quick-access"
                ),
                # 右侧目录树
                Vertical(
                    DirectoryTree("./", id="dir-tree"),
                    classes="main-tree"
                ),
                classes="main-area"
            ),
            # 底部状态栏
            Label("准备就绪", id="status-bar"),
            classes="explorer"
        )

    def on_mount(self) -> None:
        """挂载后初始化"""
        self.history = [str(self.current_path)]
        self.history_index = 0

    def on_button_pressed(self, event: Button.Pressed) -> None:
        """处理按钮点击"""
        button = event.button
        if isinstance(button, QuickAccessButton):
            if os.path.exists(button.path):
                self.change_directory(button.path)
            else:
                self.notify("目录不存在", severity="error")
        elif button.id == "back-btn":
            self.go_back()
        elif button.id == "forward-btn":
            self.go_forward()
        elif button.id == "up-btn":
            self.action_go_parent()

    def on_path_bar_path_clicked(self, message: PathBar.PathClicked) -> None:
        """处理路径栏点击"""
        self.change_directory(message.path)

    def on_directory_tree_directory_selected(self, event: DirectoryTree.DirectorySelected) -> None:
        """处理目录选择"""
        if event.path.is_dir():
            self.app.selected_dir = event.path
            self.app.pop_screen()

    def change_directory(self, path: str) -> None:
        """切换目录"""
        path = Path(path)
        if path.exists() and path.is_dir():
            self.current_path = path
            # 更新路径栏
            self.query_one(PathBar).update_path(path)
            # 更新目录树
            tree = self.query_one(DirectoryTree)
            tree.path = str(path)
            # 添加到历史
            self.history = self.history[:self.history_index + 1]
            self.history.append(str(path))
            self.history_index = len(self.history) - 1
            # 更新状态栏
            self.query_one("#status-bar").update(f"当前目录: {path}")

    def go_back(self) -> None:
        """后退"""
        if self.history_index > 0:
            self.history_index -= 1
            self.change_directory(self.history[self.history_index])

    def go_forward(self) -> None:
        """前进"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.change_directory(self.history[self.history_index])

    def action_go_parent(self) -> None:
        """返回上级目录"""
        parent = self.current_path.parent
        if parent != self.current_path:
            self.change_directory(str(parent))

class ModuleListItem(ListItem):
    """可点击的模块列表项"""
    
    class Selected(Message):
        """列表项被选中的消息"""
        def __init__(self, item: "ModuleListItem") -> None:
            self.item = item
            super().__init__()

    def __init__(self, module_name: str, module_path: str, is_selected: bool = False):
        """初始化列表项
        Args:
            module_name: 模块名称
            module_path: 模块路径
            is_selected: 是否已选中
        """
        super().__init__()
        self.module_name = module_name
        self.module_path = module_path
        self.is_selected = is_selected
        self.label = Label(self._get_display_text())

    def _get_display_text(self) -> str:
        """获取显示文本"""
        check = "【✓】" if self.is_selected else "【 】"
        return f"{check} {self.module_name:<20} ({self.module_path})"

    def toggle(self) -> None:
        """切换选中状态"""
        self.is_selected = not self.is_selected
        self.label.update(self._get_display_text())

    def compose(self) -> ComposeResult:
        yield self.label

    def on_click(self) -> None:
        """处理点击事件"""
        self.post_message(self.Selected(self))

class ModuleGroup(Static):
    """模块分组"""
    def __init__(self, title: str, modules: List[tuple[str, str]]):
        """初始化模块分组
        Args:
            title: 分组标题
            modules: 模块列表，每项为(模块名, 模块路径)的元组
        """
        super().__init__()
        self.title = title
        self.modules = modules

    def compose(self) -> ComposeResult:
        yield Static(f"=== {self.title} ===", classes="group-title")
        yield ListView(
            *[ModuleListItem(name, path) for name, path in self.modules],
            classes="group-list"
        )

class ModuleSelectionScreen(Screen):
    """模块选择界面"""
    
    BINDINGS = [
        Binding("escape", "app.pop_screen", "返回"),
        Binding("enter", "confirm", "确认"),
        Binding("space", "toggle", "切换选择"),
        Binding("a", "toggle_all", "全选/取消全选"),
    ]

    def __init__(self):
        super().__init__()
        self.selected_modules = set()
        # 扫描并分组模块
        self.module_groups = self._scan_modules()

    def _scan_modules(self) -> Dict[str, List[tuple[str, str]]]:
        """扫描生成器模块
        Returns:
            Dict[str, List[tuple[str, str]]]: 分组的模块列表
        """
        # 扫描generators目录下的功能模块
        generators_path = Path("src/gendocs/generators")
        groups = {
            "文档生成器": [],
            "图表生成器": [],
            "代码分析器": [],
            "其他生成器": []
        }
        
        if generators_path.exists():
            # 扫描所有Python文件
            for file in generators_path.rglob("*.py"):
                if file.name == "__init__.py":
                    continue
                    
                rel_path = file.relative_to(generators_path.parent)
                module_name = file.stem
                module_path = str(rel_path).replace("\\", "/")
                
                # 根据功能分类
                if "api" in str(file) or "doc" in str(file):
                    groups["文档生成器"].append((module_name, module_path))
                elif "diagram" in str(file) or "graph" in str(file):
                    groups["图表生成器"].append((module_name, module_path))
                elif "analyze" in str(file) or "parser" in str(file):
                    groups["代码分析器"].append((module_name, module_path))
                else:
                    groups["其他生成器"].append((module_name, module_path))
        
        # 移除空分组
        return {k: v for k, v in groups.items() if v}

    def compose(self) -> ComposeResult:
        """构建界面"""
        yield Container(
            Label("选择要生成的模块（空格切换选择，A 全选/取消全选）:", id="module-label"),
            VerticalScroll(
                *(ModuleGroup(title, modules) 
                  for title, modules in self.module_groups.items()),
                id="groups-container"
            ),
            Horizontal(
                Label("已选择: 0个模块", id="status-label"),
                Button("确认选择", variant="success", id="confirm-btn"),
                classes="footer"
            ),
            classes="module-screen"
        )

    def _update_status(self) -> None:
        """更新状态标签"""
        self.query_one("#status-label").update(f"已选择: {len(self.selected_modules)}个模块")

    def on_module_list_item_selected(self, message: ModuleListItem.Selected) -> None:
        """处理列表项选中消息"""
        item = message.item
        item.toggle()
        
        # 更新选中状态
        if item.is_selected:
            self.selected_modules.add(item.module_path)
        else:
            self.selected_modules.discard(item.module_path)
        
        self._update_status()

    def action_toggle(self) -> None:
        """响应空格键切换选择"""
        # 遍历所有列表找到高亮项
        for list_view in self.query(ListView):
            for item in list_view.children:
                if isinstance(item, ModuleListItem) and item.has_class("--highlight"):
                    item.toggle()
                    if item.is_selected:
                        self.selected_modules.add(item.module_path)
                    else:
                        self.selected_modules.discard(item.module_path)
                    self._update_status()
                    break

    def action_toggle_all(self) -> None:
        """全选/取消全选"""
        # 检查是否全部选中
        all_items = list(self.query(ModuleListItem))
        all_selected = all(item.is_selected for item in all_items)
        
        # 切换状态
        for item in all_items:
            item.is_selected = not all_selected
            item.label.update(item._get_display_text())
            
            if item.is_selected:
                self.selected_modules.add(item.module_path)
            else:
                self.selected_modules.discard(item.module_path)
        
        self._update_status()

    def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "confirm-btn":
            self.app.selected_modules = list(self.selected_modules)
            self.app.pop_screen()

    CSS = """
    .module-screen {
        height: 100%;
        margin: 0 1;
        background: $surface;
    }

    #module-label {
        text-align: center;
        padding: 1;
        background: $boost;
        color: $text;
        text-style: bold;
    }

    #groups-container {
        height: 85%;
        border: solid $accent;
        min-height: 20;
        margin: 1 0;
    }

    .group-title {
        color: $warning;
        text-align: center;
        padding: 1;
        background: $panel;
        text-style: bold;
        border-bottom: solid $primary;
    }

    .group-list {
        margin: 0 0 1 0;
        height: auto;
    }

    ModuleListItem {
        height: 1;
        padding: 0 1;
        width: 100%;
    }

    ModuleListItem:hover {
        background: $accent-darken-2;
    }

    ModuleListItem.--highlight {
        background: $accent;
        color: $text;
    }

    ModuleListItem Label {
        width: 100%;
        height: 1;
        content-align: left middle;
    }

    .footer {
        height: 3;
        width: 100%;
        align: center middle;
        background: $boost;
        padding: 0 1;
    }

    #status-label {
        content-align: left middle;
        width: 1fr;
        padding: 0 2;
        color: $text;
    }

    #confirm-btn {
        min-width: 20;
        margin: 0 2;
        background: $success;
    }
    """

class DocsGenUI(App):
    """文档生成工具UI"""
    
    CSS = """
    Screen {
        align: center middle;
    }

    #main-container {
        width: 60%;
        height: auto;
        border: solid $success;
        padding: 2;
        align: center middle;
    }

    .button-container {
        width: 100%;
        height: auto;
        align: center middle;
        padding: 1;
    }

    .button-container Button {
        width: 100%;
        margin: 1 0;
        min-height: 3;
        background: $panel;
    }

    #dir-btn {
        border: solid $primary;
    }

    #module-btn {
        border: solid $primary;
    }

    #generate-btn {
        background: $success;
        margin-top: 2;
    }

    #quit-btn {
        background: $error;
        margin-top: 2;
    }
    """

    BINDINGS = [
        ("d", "push_screen('project_dir')", "选择目录"),
        ("m", "push_screen('module_selection')", "选择模块"),
        ("g", "generate", "生成文档"),
        ("q", "quit", "退出"),
    ]

    def __init__(self):
        super().__init__()
        self.selected_dir: Optional[Path] = None
        self.selected_modules: List[str] = []

    def compose(self) -> ComposeResult:
        """创建主界面"""
        yield Header()
        yield Container(
            Vertical(
                Button("选择项目目录 (D)", id="dir-btn"),
                Button("选择模块 (M)", id="module-btn"),
                Button("生成文档 (G)", id="generate-btn"),
                Button("退出 (Q)", id="quit-btn"),
                classes="button-container"
            ),
            id="main-container",
        )
        yield Footer()

    def on_button_pressed(self, event: Button.Pressed) -> None:
        """处理按钮点击事件"""
        if event.button.id == "dir-btn":
            self.push_screen(ExplorerScreen())
        elif event.button.id == "module-btn":
            self.push_screen(ModuleSelectionScreen())
        elif event.button.id == "generate-btn":
            self.action_generate()
        elif event.button.id == "quit-btn":
            self.action_quit()

    def action_generate(self) -> None:
        """生成文档"""
        if not self.selected_dir:
            self.notify("请先选择项目目录", severity="error")
            return
        
        if not self.selected_modules:
            self.notify("请选择要生成的模块", severity="error")
            return

        self.notify(f"开始生成文档\n目录: {self.selected_dir}\n模块: {', '.join(self.selected_modules)}")
        # TODO: 调用生成器逻辑

def run_ui() -> None:
    """启动UI应用"""
    app = DocsGenUI()
    app.run() 

if __name__ == "__main__":
    run_ui() 