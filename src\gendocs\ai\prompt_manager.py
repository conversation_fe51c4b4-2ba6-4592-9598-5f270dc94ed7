"""
提示词管理器

管理各种文档生成的提示词模板。
"""

import json
from typing import Dict, Any, Optional
from pathlib import Path


class PromptManager:
    """提示词管理器
    
    负责加载和管理各种文档生成任务的提示词模板。
    """
    
    def __init__(self, prompts_dir: Optional[Path] = None):
        """初始化提示词管理器
        
        Args:
            prompts_dir: 提示词目录路径，默认使用内置目录
        """
        if prompts_dir is None:
            prompts_dir = Path(__file__).parent / "prompts"
        
        self.prompts_dir = prompts_dir
        self._prompts_cache: Dict[str, str] = {}
        self._load_builtin_prompts()
    
    def _load_builtin_prompts(self):
        """加载内置提示词"""
        # 内置提示词定义
        self._builtin_prompts = {
            "user_story_map": {
                "system": """你是一个产品分析专家，需要基于项目代码分析生成用户故事地图。
请分析提供的项目信息，识别用户角色、用户旅程和功能特性。""",
                
                "user": """基于以下项目分析信息，生成用户故事地图：

项目类型：{project_type}
主要功能模块：{modules}
API端点：{apis}
技术栈：{tech_stack}

请按以下格式生成用户故事地图：

# 用户故事地图

## 用户角色
- 角色1：描述
- 角色2：描述

## 用户旅程
### 旅程1：名称
- 步骤1
- 步骤2

## 功能特性
### 核心功能
- 功能1：描述
- 功能2：描述

### 扩展功能
- 功能1：描述

请确保内容贴合实际的项目功能。"""
            },
            
            "adr": {
                "system": """你是一个技术架构师，需要基于项目代码分析生成架构决策记录(ADR)。
分析项目的技术选型、架构设计和重要决策。""",
                
                "user": """基于以下项目分析信息，生成架构决策记录：

项目类型：{project_type}
技术栈：{tech_stack}
依赖关系：{dependencies}
架构信息：{architecture}

请按ADR格式生成文档：

# 架构决策记录

## ADR-001: {decision_title}

### 状态
已接受

### 背景
描述决策背景和需要解决的问题。

### 决策
描述具体的技术决策。

### 后果
#### 积极影响
- 影响1
- 影响2

#### 消极影响
- 影响1
- 影响2

请为项目中的主要技术选型生成合理的ADR。"""
            },
            
            "runbook": {
                "system": """你是一个运维专家，需要基于项目配置生成运维手册。
分析部署配置、监控需求和故障处理方案。""",
                
                "user": """基于以下项目信息，生成运维手册：

项目类型：{project_type}
部署配置：{deployment_config}
技术栈：{tech_stack}
主要服务：{services}

请生成包含以下内容的运维手册：

# 运维手册

## 应急响应
### 常见问题
#### 问题1：{common_issue}
- 现象：
- 原因：
- 解决方案：

## 监控指标
### 关键指标
- 指标1：正常范围、告警阈值
- 指标2：正常范围、告警阈值

## 维护操作
### 日常维护
- 操作1：步骤
- 操作2：步骤

请确保内容适合实际的运维场景。"""
            },
            
            "project_overview_enhancement": {
                "system": """你是一个技术文档专家，需要基于项目代码分析生成项目概览文档。
重点突出项目的核心功能、技术特点和使用价值。""",
                
                "user": """基于以下项目分析信息，增强项目概览文档：

项目名称：{project_name}
项目类型：{project_type}
框架：{framework}
主要功能：{main_features}
技术栈：{tech_stack}
API数量：{api_count}
模块数量：{module_count}

现有基础内容：
{base_content}

请对基础内容进行智能增强，生成一个专业完整的项目概览文档，包含：
1. 项目徽章和状态
2. 详细的功能特性说明
3. 技术亮点和优势
4. 完整的安装和使用指南
5. 项目架构简介
6. 贡献指南
7. 常见问题解答

确保内容准确、专业且易于理解。请保持Markdown格式。"""
            },
            
            "architecture_enhancement": {
                "system": """你是一个系统架构师，擅长分析和描述软件系统架构。
基于项目分析结果，生成专业的架构文档。""",
                
                "user": """基于以下项目信息，增强架构文档：

项目类型：{project_type}
框架：{framework}
架构模式：{architecture_pattern}
系统组件：{components}
技术栈：{tech_stack}
部署配置：{deployment_info}

现有基础内容：
{base_content}

请生成详细的系统架构文档，包含：
1. 架构概述和设计原则
2. 系统分层结构
3. 核心组件和职责
4. 数据流和交互模式
5. 技术选型决策
6. 扩展性和性能考虑
7. 安全架构设计

确保文档专业、全面且具有指导价值。"""
            },
            
            "api_documentation_enhancement": {
                "system": """你是一个API文档专家，擅长编写清晰易懂的API文档。
基于代码分析结果，生成完整的API文档。""",
                
                "user": """基于以下API分析信息，生成API文档：

项目类型：{project_type}
框架：{framework}
API列表：{apis}
认证方式：{auth_method}

现有基础内容：
{base_content}

请生成专业的API文档，包含：
1. API概述和访问说明
2. 认证和授权机制
3. 详细的接口文档（含请求/响应示例）
4. 错误码和异常处理
5. 限流和配额说明
6. SDK和工具推荐
7. 变更历史

确保文档清晰、完整且易于集成。"""
            },
            
            "development_guide_enhancement": {
                "system": """你是一个软件开发专家，擅长编写开发指南和最佳实践。
基于项目分析，生成完整的开发指南。""",
                
                "user": """基于以下项目信息，生成开发指南：

项目类型：{project_type}
框架：{framework}
技术栈：{tech_stack}
代码质量指标：{quality_metrics}
依赖管理：{dependencies}

现有基础内容：
{base_content}

请生成全面的开发指南，包含：
1. 开发环境搭建详细步骤
2. 代码规范和最佳实践
3. 测试策略和覆盖率要求
4. 调试技巧和工具推荐
5. 性能优化指导
6. 安全开发规范
7. 代码审查流程

确保指南实用、详细且易于遵循。"""
            },
            
            "deployment_guide_enhancement": {
                "system": """你是一个DevOps专家，擅长设计部署策略和运维方案。
基于项目配置，生成专业的部署指南。""",
                
                "user": """基于以下部署配置信息，生成部署指南：

项目类型：{project_type}
部署类型：{deployment_types}
容器化：{has_docker}
云平台：{cloud_platforms}
CI/CD：{has_cicd}

现有基础内容：
{base_content}

请生成完整的部署指南，包含：
1. 部署架构和环境规划
2. 详细的部署步骤（开发/测试/生产）
3. 配置管理和环境变量
4. 监控和日志配置
5. 备份和灾难恢复
6. 性能调优建议
7. 故障排查指南

确保指南专业、可操作且覆盖完整的部署生命周期。"""
            },
            
            "user_story_generation": {
                "system": """你是一个产品经理和用户体验专家，擅长分析用户需求和设计用户旅程。
基于项目功能分析，生成用户故事地图。""",
                
                "user": """基于以下项目分析信息，生成用户故事地图：

项目名称：{project_name}
项目类型：{project_type}
主要功能模块：{modules}
API端点：{apis}
技术特点：{tech_features}

请生成详细的用户故事地图，包含：

# 用户故事地图

## 目标用户群体
根据项目功能识别主要用户角色

## 用户旅程地图
### 核心用户旅程
- 描述主要的用户使用流程

### 扩展用户旅程  
- 描述高级用户或管理员的使用流程

## 用户故事
### Epic 1: [根据主要功能定义]
- Story 1: 作为[用户角色]，我希望[功能]，以便[价值]
- Story 2: [继续...]

## 验收标准
为每个用户故事定义明确的验收标准

## 优先级排序
按照商业价值和技术复杂度排序

确保用户故事贴合项目实际功能且具有实用价值。"""
            },
            
            "adr_generation": {
                "system": """你是一个技术架构师，擅长分析技术决策和记录架构演进。
基于项目技术栈分析，生成架构决策记录。""",
                
                "user": """基于以下项目分析信息，生成架构决策记录：

项目类型：{project_type}
框架选择：{framework}
技术栈：{tech_stack}
依赖关系：{dependencies}
架构模式：{architecture_pattern}
部署方案：{deployment_config}

请生成详细的架构决策记录(ADR)：

# 架构决策记录

## ADR-001: 技术栈选择

### 状态
已接受 - {current_date}

### 背景
描述技术选型的背景和驱动因素

### 决策
记录具体的技术选择和理由

### 后果
分析决策的积极和消极影响

## ADR-002: 架构模式选择
[继续其他重要决策...]

请为项目中的关键技术决策生成专业的ADR，包括但不限于：
- 框架选择
- 数据库选型  
- 架构模式
- 部署策略
- 安全方案

确保ADR内容准确、逻辑清晰且具有指导价值。"""
            },
            
            "runbook_generation": {
                "system": """你是一个SRE（Site Reliability Engineer）专家，擅长设计运维流程和故障处理方案。
基于项目配置分析，生成运维手册。""",
                
                "user": """基于以下项目信息，生成运维手册：

项目类型：{project_type}
技术栈：{tech_stack}
部署配置：{deployment_config}
监控需求：{monitoring_requirements}
关键服务：{critical_services}

请生成专业的运维手册：

# 运维手册

## 系统概览
- 系统架构简图
- 关键组件说明
- 依赖关系图

## 监控和告警
### 关键指标
- 应用性能指标（响应时间、吞吐量、错误率）
- 系统资源指标（CPU、内存、磁盘、网络）
- 业务指标（用户活跃度、事务成功率）

### 告警配置
- 告警级别定义
- 通知渠道配置
- 升级策略

## 应急响应
### 常见故障场景
- 服务不可用
- 性能下降
- 数据库连接问题
- 内存泄漏

### 故障处理流程
- 问题识别
- 初步诊断
- 临时解决方案
- 根因分析
- 永久修复

## 日常维护
### 定期检查项目
- 系统健康检查
- 日志审计
- 性能基线更新
- 备份验证

### 变更管理
- 变更流程
- 回滚计划
- 影响评估

确保手册实用、详细且易于执行。"""
            },
            
            "changelog_enhancement": {
                "system": """你是一个产品经理和技术文档专家，擅长整理和描述产品变更历史。
基于项目信息，生成或增强变更日志。""",
                
                "user": """基于以下项目信息，生成变更日志：

项目名称：{project_name}
当前版本：{current_version}
项目类型：{project_type}
主要功能：{main_features}
现有变更日志内容：{existing_content}

请生成符合 Keep a Changelog 规范的变更日志：

# 变更日志

本项目的所有重要变更都会记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本控制遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 待发布的新功能

### 变更
- 现有功能的变更

### 废弃
- 即将移除的功能

### 移除
- 已移除的功能

### 修复
- 问题修复

### 安全
- 安全相关的修复

## [{current_version}] - {release_date}

根据项目实际情况，生成合理的版本历史记录。

确保变更日志清晰、有条理且对用户有用。"""
            }
        }
    
    def get_prompt(self, prompt_type: str, **kwargs) -> tuple[str, str]:
        """获取提示词
        
        Args:
            prompt_type: 提示词类型
            **kwargs: 模板变量
            
        Returns:
            (system_prompt, user_prompt) 元组
            
        Raises:
            ValueError: 提示词类型不存在时抛出
        """
        if prompt_type not in self._builtin_prompts:
            raise ValueError(f"不支持的提示词类型: {prompt_type}")
        
        prompt_template = self._builtin_prompts[prompt_type]
        
        # 格式化提示词
        system_prompt = prompt_template["system"]
        user_prompt = prompt_template["user"].format(**kwargs)
        
        return system_prompt, user_prompt
    
    def get_available_prompts(self) -> list[str]:
        """获取可用的提示词类型列表"""
        return list(self._builtin_prompts.keys())
    
    def add_custom_prompt(self, prompt_type: str, system: str, user: str):
        """添加自定义提示词
        
        Args:
            prompt_type: 提示词类型
            system: 系统提示词
            user: 用户提示词模板
        """
        self._builtin_prompts[prompt_type] = {
            "system": system,
            "user": user
        }
    
    def save_prompts_to_file(self, file_path: Path):
        """保存提示词到文件
        
        Args:
            file_path: 保存路径
        """
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self._builtin_prompts, f, ensure_ascii=False, indent=2)
    
    def load_prompts_from_file(self, file_path: Path):
        """从文件加载提示词
        
        Args:
            file_path: 文件路径
        """
        if not file_path.exists():
            raise FileNotFoundError(f"提示词文件不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            custom_prompts = json.load(f)
        
        self._builtin_prompts.update(custom_prompts)