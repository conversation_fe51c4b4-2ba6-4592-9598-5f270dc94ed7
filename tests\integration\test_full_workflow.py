"""
完整工作流集成测试
"""

import pytest
import asyncio
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from gendocs.config import ConfigManager
from gendocs.analyzers.registry import get_registry
from gendocs.generators import EnterpriseDocumentManager
from gendocs.batch.processor import BatchProcessor, BatchJobConfig
from gendocs.monitoring import get_performance_monitor


class TestFullWorkflow:
    """完整工作流测试"""
    
    @pytest.mark.asyncio
    async def test_single_project_generation(self, complex_project_dir, temp_dir):
        """测试单个项目的完整文档生成流程"""
        output_dir = temp_dir / "single_project_output"
        
        # 1. 配置管理
        config_manager = ConfigManager()
        config_manager.set_config_value('ai.provider', 'mock')
        config_manager.set_config_value('generation.output_dir', str(output_dir))
        
        # 2. 项目分析
        analyzer_registry = get_registry()
        analysis_result = analyzer_registry.analyze_project(complex_project_dir)
        
        assert analysis_result is not None
        assert analysis_result.project_name == "myproject"
        
        # 3. 文档生成
        with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
            mock_provider = Mock()
            mock_provider.generate_content = AsyncMock(return_value="Generated content")
            mock_ai.return_value = mock_provider
            
            doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
            results = await doc_manager.generate_all_documents(
                complex_project_dir,
                output_dir,
                selected_generators=['overview']
            )
            
            # 验证生成结果
            assert isinstance(results, dict)
            assert len(results) > 0
            
            # 验证输出文件
            assert output_dir.exists()
    
    @pytest.mark.asyncio
    async def test_batch_processing_workflow(self, mock_project_dir, temp_dir):
        """测试批量处理工作流"""
        # 创建多个测试项目
        projects = []
        for i in range(3):
            project_dir = temp_dir / f"project_{i}"
            shutil.copytree(mock_project_dir, project_dir)
            projects.append(project_dir)
        
        # 配置批量处理器
        config = BatchJobConfig(max_workers=2, timeout=30)
        processor = BatchProcessor(config)
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            # 添加批量作业
            job_ids = []
            for i, project_dir in enumerate(projects):
                job_id = await processor.add_job(
                    project_path=project_dir,
                    output_path=temp_dir / f"output_{i}",
                    config={"generators": ["overview"]}
                )
                job_ids.append(job_id)
            
            # 处理所有作业
            await processor.start_processing()
            await asyncio.sleep(1.0)  # 等待处理完成
            await processor.stop_processing()
            
            # 验证所有作业都被处理
            queue_status = processor.get_queue_status()
            assert queue_status['total_jobs'] == 3
            assert queue_status['completed_jobs'] + queue_status['failed_jobs'] + queue_status['running_jobs'] == 3
    
    @pytest.mark.asyncio
    async def test_monitoring_integration(self, mock_project_dir, temp_dir):
        """测试监控系统集成"""
        output_dir = temp_dir / "monitoring_test_output"
        
        # 获取性能监控器
        monitor = get_performance_monitor()
        monitor.start_monitoring()
        
        try:
            # 执行文档生成过程
            config_manager = ConfigManager()
            
            with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
                mock_provider = Mock()
                mock_provider.generate_content = AsyncMock(return_value="Generated content")
                mock_ai.return_value = mock_provider
                
                doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
                
                # 生成文档（这应该被监控记录）
                results = await doc_manager.generate_all_documents(
                    mock_project_dir,
                    output_dir,
                    selected_generators=['overview']
                )
                
                # 等待监控数据收集
                await asyncio.sleep(0.5)
                
                # 验证监控数据
                status = monitor.get_current_status()
                assert status['monitoring_active'] is True
                
                performance_summary = status['performance_summary']
                # 根据实际监控实现验证指标
                assert isinstance(performance_summary, dict)
        
        finally:
            monitor.stop_monitoring()
    
    def test_error_recovery_workflow(self, mock_project_dir, temp_dir):
        """测试错误恢复工作流"""
        config_manager = ConfigManager()
        
        # 第一次尝试：模拟AI提供商错误
        with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
            mock_provider = Mock()
            mock_provider.generate_content = AsyncMock(side_effect=Exception("AI service unavailable"))
            mock_ai.return_value = mock_provider
            
            doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
            
            async def first_attempt():
                try:
                    results = await doc_manager.generate_all_documents(
                        mock_project_dir,
                        temp_dir / "error_output",
                        selected_generators=['overview']
                    )
                    return results
                except Exception as e:
                    # 记录错误但不抛出
                    return {"error": str(e)}
            
            first_result = asyncio.run(first_attempt())
            assert "error" in first_result or not all(first_result.values())
        
        # 第二次尝试：AI服务恢复
        with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
            mock_provider = Mock()
            mock_provider.generate_content = AsyncMock(return_value="Recovered content")
            mock_ai.return_value = mock_provider
            
            doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
            
            async def second_attempt():
                results = await doc_manager.generate_all_documents(
                    mock_project_dir,
                    temp_dir / "recovery_output",
                    selected_generators=['overview']
                )
                return results
            
            second_result = asyncio.run(second_attempt())
            assert isinstance(second_result, dict)
            # 验证恢复后的生成成功
    
    @pytest.mark.asyncio
    async def test_configuration_driven_workflow(self, complex_project_dir, temp_dir):
        """测试配置驱动的工作流"""
        # 创建自定义配置
        config_file = temp_dir / "custom_config.yaml"
        config_content = """
ai:
  provider: mock
  model: test-model
  temperature: 0.7
  max_tokens: 2000

generation:
  output_dir: custom_docs
  templates_dir: custom_templates
  generators:
    - overview
    - api

monitoring:
  enable_performance_tracking: true
  enable_profiling: false

batch:
  max_workers: 2
  timeout: 60
"""
        config_file.write_text(config_content)
        
        # 加载自定义配置
        config_manager = ConfigManager()
        config_manager.load_config(config_file)
        
        # 验证配置加载
        assert config_manager.get_config_value('ai.provider') == 'mock'
        assert config_manager.get_config_value('ai.temperature') == 0.7
        assert config_manager.get_config_value('batch.max_workers') == 2
        
        # 使用配置驱动的文档生成
        output_dir = temp_dir / "config_driven_output"
        
        with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
            mock_provider = Mock()
            mock_provider.generate_content = AsyncMock(return_value="Config-driven content")
            mock_ai.return_value = mock_provider
            
            doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
            
            # 获取配置中指定的生成器列表
            generators = config_manager.get_config_value('generation.generators', ['overview'])
            
            results = await doc_manager.generate_all_documents(
                complex_project_dir,
                output_dir,
                selected_generators=generators
            )
            
            # 验证生成结果符合配置要求
            assert isinstance(results, dict)
            for generator in generators:
                assert generator in results or not results  # 如果没有AI可能失败
    
    @pytest.mark.asyncio
    async def test_concurrent_project_processing(self, temp_dir):
        """测试并发项目处理"""
        # 创建多个不同类型的项目
        projects = []
        
        # Python项目
        python_project = temp_dir / "python_project"
        python_project.mkdir()
        (python_project / "main.py").write_text("print('Python project')")
        (python_project / "requirements.txt").write_text("requests==2.28.0")
        projects.append(("python", python_project))
        
        # JavaScript项目（如果有相应分析器）
        js_project = temp_dir / "js_project"
        js_project.mkdir()
        (js_project / "package.json").write_text('{"name": "js-project", "version": "1.0.0"}')
        (js_project / "index.js").write_text("console.log('JavaScript project');")
        projects.append(("javascript", js_project))
        
        # 并发处理所有项目
        config_manager = ConfigManager()
        
        with patch('gendocs.ai.providers.get_ai_provider') as mock_ai:
            mock_provider = Mock()
            mock_provider.generate_content = AsyncMock(return_value="Concurrent content")
            mock_ai.return_value = mock_provider
            
            async def process_project(project_type, project_path):
                doc_manager = EnterpriseDocumentManager(config_manager, mock_provider)
                output_dir = temp_dir / f"{project_type}_output"
                
                try:
                    results = await doc_manager.generate_all_documents(
                        project_path,
                        output_dir,
                        selected_generators=['overview']
                    )
                    return (project_type, True, results)
                except Exception as e:
                    return (project_type, False, str(e))
            
            # 并发执行所有项目处理
            tasks = [process_project(ptype, ppath) for ptype, ppath in projects]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 验证并发处理结果
            successful_projects = 0
            for result in results:
                if isinstance(result, tuple) and result[1]:  # 成功处理
                    successful_projects += 1
            
            # 至少应该有一个项目处理成功
            assert successful_projects > 0
    
    def test_cli_integration_workflow(self, complex_project_dir, temp_dir):
        """测试CLI集成工作流"""
        from click.testing import CliRunner
        from gendocs.cli.main import cli
        
        runner = CliRunner()
        
        # 1. 初始化配置
        with runner.isolated_filesystem():
            init_result = runner.invoke(cli, ['init'])
            # 配置初始化应该成功或至少不出错
            assert init_result.exit_code == 0 or "error" not in init_result.output.lower()
        
        # 2. 生成文档
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            mock_instance.generate_all_documents = AsyncMock(return_value={"overview": True})
            mock_manager.return_value = mock_instance
            
            generate_result = runner.invoke(cli, [
                'generate',
                str(complex_project_dir),
                '--output', str(temp_dir / "cli_output")
            ])
            
            # 文档生成应该成功
            assert generate_result.exit_code == 0 or "error" not in generate_result.output.lower()
        
        # 3. 检查监控状态
        with patch('gendocs.monitoring.get_performance_monitor') as mock_monitor:
            mock_instance = Mock()
            mock_instance.get_current_status.return_value = {
                'monitoring_active': False,
                'performance_summary': {}
            }
            mock_monitor.return_value = mock_instance
            
            monitor_result = runner.invoke(cli, ['monitor', 'status'])
            assert monitor_result.exit_code == 0
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, temp_dir):
        """测试负载下的性能"""
        import time
        
        # 创建多个大型项目模拟负载
        projects = []
        for i in range(5):
            project_dir = temp_dir / f"load_project_{i}"
            project_dir.mkdir()
            
            # 创建大量文件模拟复杂项目
            for j in range(10):
                (project_dir / f"module_{j}.py").write_text(f"""
class Module{j}:
    def __init__(self):
        self.value = {j}
    
    def method_{j}(self):
        return self.value * {j}
""")
            
            projects.append(project_dir)
        
        # 测量批量处理性能
        start_time = time.time()
        
        config = BatchJobConfig(max_workers=3, timeout=60)
        processor = BatchProcessor(config)
        
        with patch('gendocs.generators.EnterpriseDocumentManager') as mock_manager:
            mock_instance = Mock()
            # 模拟真实的处理时间
            async def slow_generate(*args, **kwargs):
                await asyncio.sleep(0.1)  # 模拟处理时间
                return {"overview": True}
            
            mock_instance.generate_all_documents = slow_generate
            mock_manager.return_value = mock_instance
            
            # 添加所有作业
            job_ids = []
            for i, project_dir in enumerate(projects):
                job_id = await processor.add_job(
                    project_path=project_dir,
                    output_path=temp_dir / f"load_output_{i}",
                    config={}
                )
                job_ids.append(job_id)
            
            # 处理所有作业
            await processor.start_processing()
            
            # 等待处理完成
            max_wait_time = 30  # 最多等待30秒
            waited = 0
            while waited < max_wait_time:
                status = processor.get_queue_status()
                if status['running_jobs'] == 0 and status['pending_jobs'] == 0:
                    break
                await asyncio.sleep(0.5)
                waited += 0.5
            
            await processor.stop_processing()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 验证性能指标
            final_status = processor.get_queue_status()
            assert final_status['total_jobs'] == 5
            assert total_time < 60  # 应该在合理时间内完成
            
            # 计算吞吐量
            throughput = final_status['total_jobs'] / total_time
            assert throughput > 0  # 确保有实际的处理速度